import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "@/utils/request";

interface AdvertisementState {
  advertisements: [];
  isLoading: boolean;
}

const initialState: AdvertisementState = {
  advertisements: [],
  isLoading: false,
};

export const getAdvertisements = createAsyncThunk(
  "advertisement/getAdvertisements",
  async (shopId: string, { rejectWithValue }) => {
    try {
      const response: any = await request(
        "get",
        `/api/user/advertiseuser?skip=0&limit=99&shopId=${shopId}`
      );
      const advertisements = response.data.filter(
        (ad: any) => ad.typePublish === "Publish"
      );
      return advertisements;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to get advertisements");
    }
  }
);

const advertisementSlice = createSlice({
  name: "advertisement",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getAdvertisements.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAdvertisements.fulfilled, (state, action: PayloadAction<any[]>) => {
        state.advertisements = action.payload;
        state.isLoading = false;
      })
      .addCase(getAdvertisements.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default advertisementSlice.reducer;