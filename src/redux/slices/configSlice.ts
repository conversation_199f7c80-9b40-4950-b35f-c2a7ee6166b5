import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { request } from "../../utils/request";

interface AppConfig {
  reviewing?: boolean;
}

interface NotificationState {
  data: AppConfig;
  isLoading: boolean;
  error: string | null;
  isCampaignPopupShowed?: boolean | null;
  deepLinkStartAppNavigated?: boolean | null;
}

const initialState: NotificationState = {
  data: {},
  isLoading: true,
  error: null,
  isCampaignPopupShowed: null,
  deepLinkStartAppNavigated: null,
};

export const getConfig = createAsyncThunk("config/get", async () => {
  const response: any = await request("get", "/api/config");
  return response;
});

const configSlice = createSlice({
  name: "config",
  initialState,
  reducers: {
    setCampaignPopupShowed: (state, action: PayloadAction<boolean | null>) => {
      state.isCampaignPopupShowed = action?.payload;
    },
    setDeepLinkStartAppNavigated: (
      state,
      action: PayloadAction<boolean | null>
    ) => {
      state.deepLinkStartAppNavigated = action?.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getConfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getConfig.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.data = payload.data.attributes.content;
          state.isLoading = false;
        }
      )
      .addCase(getConfig.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export const { setCampaignPopupShowed, setDeepLinkStartAppNavigated } =
  configSlice.actions;
export default configSlice.reducer;
