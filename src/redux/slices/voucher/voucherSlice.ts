import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IVoucherUser } from "../../../types/voucher";
import { ICart } from "@/types/cart";

export interface VoucherDto {
  voucherId: string;
  shopId: string;
  voucherName: string;
  voucherNameOrigin: string;
  image: any;
  voucherType: "Promotion" | string;
  releaseType: "Free" | string;
  exchangePoints: number;
  rewardType: "Point" | string;
  isFixedRewardPoint: boolean;
  rewardPoint: number | null;
  rewardPointMin: number | null;
  rewardPointMax: number | null;
  rewardGiftIds: string[];
  quantity: number;
  quantityUsed: number;
  codeType: "Common" | string;
  voucherCodePrefix: string;
  discountType: "Percent" | "Money" | string;
  minOrder: number;
  maxDiscount: number;
  percentDiscount: number;
  moneyDiscount: number;
  shippingDiscountType: "Free" | string;
  limitType: "All" | string;
  categoryIds: string[] | null;
  productIds: string[];
  conditionType: "All" | string;
  userGroupId: string;
  userIds: string[];
  maxUsagePerUser: number;
  isLongTerm: boolean;
  startDate: string;
  endDate: string;
  status: "Actived" | string;
  isBranchSpecific: boolean;
  voucherDetails?: VoucherDetailDto[];
  rewardProducts: any;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  isDeleted: boolean;
  deletedAt: string | null;
  id: string;
}

export interface VoucherDetailDto {
  voucherDetailId: string;
  voucherId: string;
  voucherCode: string;
  voucherCodeLink: string;
  status: string;
  isSystemAssigned: boolean;
  user: string | null;
  numUse: number | null;
  branchId: string | null;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  isDeleted: boolean;
  deletedAt: string | null;
  id: string;
}

interface AuthState {
  voucherList: VoucherDto[];
  myVoucherList: VoucherDto[];
  isLoading: boolean;
  selectedVoucherItem: VoucherDto | null;
  myVoucherUserList: IVoucherUser[];
  disabledVouchers: any;
  listVoucherByShop: VoucherDto[];
  listVoucherByUser: VoucherDto[];
  listVoucherByCart: VoucherDto[];
  voucherDetail?: VoucherDto;
  userVoucherCount: number;
}

export interface GetVoucherByShopParams {
  NameType?: string;
  SortType?: string;
  PageSize?: number;
  PageIndex?: number;
  Search?: string;
  Name?: string;
  Sort?: string;
  shopId: string | null;
}

const initialState: AuthState = {
  voucherList: [],
  myVoucherList: [],
  isLoading: true,
  selectedVoucherItem: null,
  myVoucherUserList: [],
  disabledVouchers: null,
  listVoucherByShop: [],
  listVoucherByUser: [],
  listVoucherByCart: [],
  voucherDetail: undefined,
  userVoucherCount: 0,
};

export interface TaxInvoiceDto {
  id: string;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  isDeleted: boolean;
  deletedAt: string;
  taxInvoiceId: string;
  shopId: string;
  userId: string;
  taxPayerType: string; // giả định 2 loại
  taxCode: string;
  name: string;
  address: string;
  email: string;
  phoneNumber: string;
  isDefault: boolean;
}

export interface CartDto {
  cartId: string;
  cartNo: string;
  transactionId: string;
  partnerId: string;
  userId: string;
  addressId: string;
  shopId: string;
  listItems: any[]; // bạn có thể định nghĩa chi tiết kiểu của sản phẩm nếu cần
  voucherPromotion: string[]; // hoặc object[] nếu là dạng object
  voucherTransport: string[]; // hoặc object[] nếu là dạng object
  price: number;
  originPrice: number;
  savingMoney: number;
  exchangePoints: number;
  pointPrice: number;
  voucherPromotionPrice: number;
  voucherTransportPrice: number;
  transportPrice: number;
  transportService: string; // ví dụ: "LCOD"
  statusDelivery: string; // ví dụ: "InShop"
  typePay: string; // ví dụ: "COD"
  created: string;
  updated: string;
  branchId: string;
  cartOrigin: string; // ví dụ: "WebPartner"
  paymentId: string;
  taxInvoice: TaxInvoiceDto;
  totalTaxAmount: number;
  totalAfterTax: number;
}

export const getVoucherList = createAsyncThunk("voucher/getAllVoucherList", async () => {
  const response: any = { data: [] };
  return response;
});

export const getMyVoucherList = createAsyncThunk("voucher/getMyVoucherList", async (data: any) => {
  if (data?.shopId) {
    const url = `/api/voucher?ShopId=${data.shopId}&SearchType=All&skip=0&limit=99`;
    const response: any = await request("get", url);
    return response;
  }
});

export const validateVoucherByCart = createAsyncThunk(
  "voucher/validateVoucherByCart",
  async (data: any) => {
    let url = `/api/user/voucher/validatevoucher`;
    if (Array.isArray(data?.voucherIds) && data?.voucherIds.length > 0) {
      const response: any = await request("post", url, data);
      return response;
    }
  }
);

export const redeemPointToVoucher = createAsyncThunk(
  "voucher/redeemPointToVoucher",
  async (data: { voucherId: number }, { rejectWithValue }) => {
    const { voucherId } = data;
    try {
      const response: any = await request("post", "/api/my-voucher/redeem-point", {
        voucherId,
      });

      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getMyVoucherUserList = createAsyncThunk(
  "voucheruser/getMyVoucherUserList",
  async () => {
    let url = `/api/user/voucheruser`;
    const response: any = await request("get", url);
    return response;
  }
);

export const collectVoucher = createAsyncThunk(
  "voucheruser/collectVoucher",
  async (voucherCode: string) => {
    if (voucherCode) {
      let url = `/api/user/voucheruser/redeem?voucherCode=${voucherCode}`;
      console.log({ url });

      const response: any = await request("get", url);
      return response;
    }
  }
);

export const collectVoucherPoint = createAsyncThunk(
  "voucheruser/collectVoucherPoint",
  async (voucherCode: string) => {
    if (voucherCode) {
      let url = `/api/user/voucheruser/redeem-point?voucherCode=${voucherCode}`;
      console.log({ url });

      const response: any = await request("get", url);
      return response;
    }
  }
);

export const searchByPointsCode = createAsyncThunk(
  "voucher/searchByPointsCode",
  async (data: { pointsCode: string; shopId?: string }, { rejectWithValue }) => {
    try {
      const url = `/api/partner/PointPromotionPartner/SearchByPointsCode?pointsCode=${
        data.pointsCode
      }${data.shopId ? `&shopId=${data.shopId}` : ""}`;
      const response: any = await request("get", url);
      return response;
    } catch (error: any) {
      if (error.response && error.response.data) {
        return rejectWithValue(error.response.data);
      }
      return rejectWithValue({ message: "SERVER_ERROR" });
    }
  }
);

export const RedeemPointsFromPointVoucherCode = createAsyncThunk(
  "voucher/RedeemPointsFromPointVoucherCode",
  async (pointsCode: string, { rejectWithValue }) => {
    try {
      const response = await request(
        "post",
        "/api/user/PointPromotionUser/RedeemPointsFromPointVoucherCode",
        {
          pointsCode,
        }
      );

      return response;
    } catch (error) {
      return rejectWithValue({ detail: "Có lỗi xảy ra khi nhận điểm" });
    }
  }
);

export const getListVoucherByShop = createAsyncThunk(
  "voucheruser/getListVoucherByShop",
  async (params: GetVoucherByShopParams) => {
    if (params?.shopId) {
      const queryParams = new URLSearchParams();

      if (params?.NameType) queryParams.append("NameType", params.NameType);
      if (params?.SortType) queryParams.append("SortType", params.SortType);
      if (params?.PageSize !== undefined)
        queryParams.append("PageSize", params.PageSize.toString());
      if (params?.PageIndex !== undefined)
        queryParams.append("PageIndex", params.PageIndex.toString());
      if (params?.Name) queryParams.append("Name", params.Name);
      if (params?.Search) queryParams.append("Search", params.Search);
      if (params?.Sort) queryParams.append("Sort", params.Sort);
      if (params?.shopId) queryParams.append("shopId", params.shopId);

      const url = `/api/user/voucheruser/voucherbyshop?${queryParams.toString()}`;
      const response: any = await request("get", url);
      return { ...response, PageIndex: params.PageIndex };
    }
  }
);
export const getListVoucherByShopPublic = createAsyncThunk(
  "voucheruser/getListVoucherByShopPublic",
  async (params: GetVoucherByShopParams) => {
    if (params?.shopId) {
      const queryParams = new URLSearchParams();

      if (params?.NameType) queryParams.append("NameType", params.NameType);
      if (params?.SortType) queryParams.append("SortType", params.SortType);
      if (params?.PageSize !== undefined)
        queryParams.append("PageSize", params.PageSize.toString());
      if (params?.PageIndex !== undefined)
        queryParams.append("PageIndex", params.PageIndex.toString());
      if (params?.Name) queryParams.append("Name", params.Name);
      if (params?.Search) queryParams.append("Search", params.Search);
      if (params?.Sort) queryParams.append("Sort", params.Sort);
      if (params?.shopId) queryParams.append("shopId", params.shopId);

      const url = `/api/voucher?${queryParams.toString()}`;
      const response: any = await request("get", url);
      return { ...response, PageIndex: params.PageIndex };
    }
  }
);

export const getListVoucherByUser = createAsyncThunk(
  "voucheruser/getListVoucherByUser",
  async (params: GetVoucherByShopParams) => {
    if (params?.shopId) {
      const queryParams = new URLSearchParams();

      if (params?.NameType) queryParams.append("NameType", params.NameType);
      if (params?.SortType) queryParams.append("SortType", params.SortType);
      if (params?.PageSize !== undefined)
        queryParams.append("PageSize", params.PageSize.toString());
      if (params?.PageIndex !== undefined)
        queryParams.append("PageIndex", params.PageIndex.toString());
      if (params?.Name) queryParams.append("Name", params.Name);
      if (params?.Search) queryParams.append("Search", params.Search);
      if (params?.Sort) queryParams.append("Sort", params.Sort);
      if (params?.shopId) queryParams.append("shopId", params.shopId);

      const url = `/api/user/voucheruser/voucherbyuser?${queryParams.toString()}`;
      const response: any = await request("get", url);
      return { ...response, PageIndex: params.PageIndex };
    }
  }
);
export const getListVoucherByCart = createAsyncThunk(
  "voucheruser/getListVoucherByCart",
  async (data: ICart) => {
    if (Array.isArray(data?.listItems) && data?.listItems.length > 0) {
      const url = `/api/user/voucheruser/voucherbycart`;
      const response: any = await request("post", url, data);
      return response;
    }
  }
);

export const getDetailVoucherByCode = createAsyncThunk(
  "voucheruser/getDetailVoucherByCode",
  async (voucherId: string) => {
    if (voucherId) {
      const url = `/api/user/voucheruser/voucherbycode?voucherCode=${voucherId}`;
      const response: any = await request("get", url);
      return response;
    }
  }
);

export const getDetailVoucherByCodePublic = createAsyncThunk(
  "voucheruser/getDetailVoucherByCodePublic",
  async (voucherId: string) => {
    if (voucherId) {
      const url = `/api/voucher/${voucherId}`;
      const response: any = await request("get", url, undefined, {
        headers: {
          Authorization: null,
        },
      });
      return response;
    }
  }
);

// export const getDetailVoucher = createAsyncThunk(
//   "voucheruser/getDetailVoucher",
//   async (voucherId: string) => {
//     if (voucherId) {
//       const url = `/api/user/voucheruser/voucherbycode?voucherId=${voucherId}`;
//       const response: any = await request("get", url);
//       return response;
//     }
//   }
// );

const voucherSlice = createSlice({
  name: "voucherLists",
  initialState,
  reducers: {
    setCurVoucher: (state, action: PayloadAction<VoucherDto | null>) => {
      state.voucherDetail = action?.payload === null ? undefined : action.payload;
    },
    resetListVoucherByShop: (state) => {
      state.listVoucherByShop = [];
    },
    resetListVoucherByUser: (state) => {
      state.listVoucherByUser = [];
    },
    setUserVoucherCount: (state, action: PayloadAction<number>) => {
      state.userVoucherCount = action.payload;
    },
    incrementUserVoucherCount: (state) => {
      state.userVoucherCount += 1;
    },
    decrementUserVoucherCount: (state) => {
      if (state.userVoucherCount > 0) {
        state.userVoucherCount -= 1;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getVoucherList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getVoucherList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        if (payload.data) {
          state.voucherList = payload.data.map((e) => {
            return {
              id: e.id,
              ...e.attributes,
              banner: e.attributes.banner?.data?.attributes,
            };
          });
        }
        state.isLoading = false;
      })
      .addCase(getVoucherList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getMyVoucherUserList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getMyVoucherUserList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.myVoucherUserList = payload.result;
        state.isLoading = false;
      })
      .addCase(getMyVoucherUserList.rejected, (state, action) => {
        state.isLoading = false;
      })

      // v2

      .addCase(getListVoucherByShop.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getListVoucherByShop.fulfilled,
        (
          state,
          action: PayloadAction<Partial<any>> & { meta: { arg: GetVoucherByShopParams } }
        ) => {
          const { payload, meta } = action;
          if (meta.arg.PageIndex === 0) {
            state.listVoucherByShop = payload.result;
          } else {
            state.listVoucherByShop = [...state.listVoucherByShop, ...payload.result];
          }
          state.isLoading = false;
        }
      )
      .addCase(getListVoucherByShop.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(getListVoucherByShopPublic.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getListVoucherByShopPublic.fulfilled,
        (
          state,
          action: PayloadAction<Partial<any>> & { meta: { arg: GetVoucherByShopParams } }
        ) => {
          const { payload, meta } = action;
          if (meta.arg.PageIndex === 0) {
            state.listVoucherByShop = payload.result.result;
          } else {
            state.listVoucherByShop = [...state.listVoucherByShop, ...payload.result.result];
          }
          state.isLoading = false;
        }
      )
      .addCase(getListVoucherByShopPublic.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(getListVoucherByUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getListVoucherByUser.fulfilled,
        (
          state,
          action: PayloadAction<Partial<any>> & { meta: { arg: GetVoucherByShopParams } }
        ) => {
          const { payload, meta } = action;
          if (meta.arg.PageIndex === 0) {
            state.listVoucherByUser = payload.result;
            // Update user voucher count from API total
            if (payload.total !== undefined) {
              state.userVoucherCount = payload.total;
            }
          } else {
            state.listVoucherByUser = [...state.listVoucherByUser, ...payload.result];
          }
          state.isLoading = false;
        }
      )
      .addCase(getListVoucherByUser.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getListVoucherByCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getListVoucherByCart.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.listVoucherByCart = payload.result;
        state.isLoading = false;
      })
      .addCase(getListVoucherByCart.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(getDetailVoucherByCode.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getDetailVoucherByCode.fulfilled, (state, action: PayloadAction<any>) => {
        const { payload } = action;
        state.voucherDetail = payload.result.data;
        state.isLoading = false;
      })
      .addCase(getDetailVoucherByCode.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(getDetailVoucherByCodePublic.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getDetailVoucherByCodePublic.fulfilled, (state, action: PayloadAction<any>) => {
        const { payload } = action;
        state.voucherDetail = payload.result;
        state.isLoading = false;
      })
      .addCase(getDetailVoucherByCodePublic.rejected, (state, action) => {
        state.isLoading = false;
      })

      // v2

      .addCase(getMyVoucherList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getMyVoucherList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.myVoucherList = Array.isArray(payload?.result?.result) ? payload.result.result : [];
        state.isLoading = false;
      })
      .addCase(getMyVoucherList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(redeemPointToVoucher.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(redeemPointToVoucher.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        state.isLoading = false;
      })
      .addCase(redeemPointToVoucher.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(validateVoucherByCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(validateVoucherByCart.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.disabledVouchers = payload;
        state.isLoading = false;
      })
      .addCase(validateVoucherByCart.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(searchByPointsCode.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(searchByPointsCode.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        state.isLoading = false;
      })
      .addCase(searchByPointsCode.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export const {
  setCurVoucher,
  resetListVoucherByShop,
  resetListVoucherByUser,
  setUserVoucherCount,
  incrementUserVoucherCount,
  decrementUserVoucherCount,
} = voucherSlice.actions;

export default voucherSlice.reducer;
