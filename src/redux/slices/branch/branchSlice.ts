import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IBranchItem } from "../../../types/branch";

interface BranchState {
  list: IBranchItem[];
  defaultBranch: IBranchItem | null;
  isLoading: boolean;
}

const initialState: BranchState = {
  list: [],
  defaultBranch: null,
  isLoading: true,
};

export const getBranchList = createAsyncThunk("branch/getBranchList", async (query?: string) => {
  let url = `/api/user/branchuser`;
  if (query) {
    url += `?search=${query}`;
  }
  const response: any = await request("get", url);

  return response;
});

const branchSlice = createSlice({
  name: "address",
  initialState,
  reducers: {
    setDefaultBranch: (state, action: PayloadAction<IBranchItem>) => {
      state.defaultBranch = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getBranchList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getBranchList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.list = payload.data;
        state.defaultBranch = payload.data.length > 0 ? payload.data[0] : null;
        state.isLoading = false;
      })
      .addCase(getBranchList.rejected, (state) => {
        state.isLoading = false;
      });
  },
});

export const { setDefaultBranch } = branchSlice.actions;
export default branchSlice.reducer;
