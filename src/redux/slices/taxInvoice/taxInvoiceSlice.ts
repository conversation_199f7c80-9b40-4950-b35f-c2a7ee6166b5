import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "@/utils/request";
import { ITaxInvoiceConfiguration, ITaxInvoiceBusinessInfo } from "@/types/taxInvoice";

interface TaxInvoiceState {
  list: ITaxInvoiceConfiguration[];
  businessInfo: ITaxInvoiceBusinessInfo | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: TaxInvoiceState = {
  list: [],
  businessInfo: null,
  isLoading: false,
  error: null,
};

// GET: api/user/taxinvoiceconfiguration/{shopId}?userId=...
export const getTaxInvoiceList = createAsyncThunk(
  "taxInvoice/getList",
  async (
    params: { shopId: string; userId?: string },
    { rejectWithValue }
  ) => {
    try {
      const { shopId, userId } = params;
      let url = `/api/user/taxinvoiceconfiguration/${shopId}`;
      if (userId) url += `?userId=${userId}`;
      const response: any = await request("get", url);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to get tax invoice list");
    }
  }
);

// POST: api/user/taxinvoiceconfiguration
export const createTaxInvoice = createAsyncThunk(
  "taxInvoice/create",
  async (data: ITaxInvoiceConfiguration, { rejectWithValue }) => {
    try {
      const response: any = await request("post", "/api/user/taxinvoiceconfiguration", data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to create tax invoice");
    }
  }
);

// PUT: api/user/taxinvoiceconfiguration
export const updateTaxInvoice = createAsyncThunk(
  "taxInvoice/update",
  async (data: ITaxInvoiceConfiguration, { rejectWithValue }) => {
    try {
      const response: any = await request("put", "/api/user/taxinvoiceconfiguration", data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to update tax invoice");
    }
  }
);

// DELETE: api/user/taxinvoiceconfiguration/{shopId}/{id}
export const deleteTaxInvoice = createAsyncThunk(
  "taxInvoice/delete",
  async (
    params: { shopId: string; id: string },
    { rejectWithValue }
  ) => {
    try {
      const { shopId, id } = params;
      const url = `/api/user/taxinvoiceconfiguration/${shopId}/${id}`;
      const response: any = await request("delete", url);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to delete tax invoice");
    }
  }
);

// PUT: api/user/taxinvoiceconfiguration/default/{shopId}/{userId}/{id}
export const setDefaultTaxInvoice = createAsyncThunk(
  "taxInvoice/setDefault",
  async (
    params: { shopId: string; userId: string; id: string },
    { rejectWithValue }
  ) => {
    try {
      const { shopId, userId, id } = params;
      const url = `/api/user/taxinvoiceconfiguration/default/${shopId}/${userId}/${id}`;
      const response: any = await request("put", url);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to set default tax invoice");
    }
  }
);

// GET: api/user/taxinvoiceconfiguration/business/info/{taxCode}
export const getBusinessInfoByTaxCode = createAsyncThunk(
  "taxInvoice/getBusinessInfoByTaxCode",
  async (taxCode: string, { rejectWithValue }) => {
    try {
      const url = `/api/user/taxinvoiceconfiguration/business/info/${taxCode}`;
      const response: any = await request("get", url);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to get business info");
    }
  }
);

// GET: api/user/taxinvoiceconfiguration/individual/info/{taxCode}?name=...
export const getBusinessInfoBySlug = createAsyncThunk(
  "taxInvoice/getBusinessInfoBySlug",
  async (
    params: { taxCode: string; name: string },
    { rejectWithValue }
  ) => {
    try {
      const { taxCode, name } = params;
      const url = `/api/user/taxinvoiceconfiguration/individual/info/${taxCode}?name=${encodeURIComponent(name)}`;
      const response: any = await request("get", url);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to get business info by slug");
    }
  }
);

const taxInvoiceSlice = createSlice({
  name: "taxInvoice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getTaxInvoiceList.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getTaxInvoiceList.fulfilled, (state, action: PayloadAction<any>) => {
        state.list = action.payload?.result || [];
        state.isLoading = false;
      })
      .addCase(getTaxInvoiceList.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(createTaxInvoice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createTaxInvoice.fulfilled, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
      })
      .addCase(createTaxInvoice.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(updateTaxInvoice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateTaxInvoice.fulfilled, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
      })
      .addCase(updateTaxInvoice.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deleteTaxInvoice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteTaxInvoice.fulfilled, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
      })
      .addCase(deleteTaxInvoice.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(setDefaultTaxInvoice.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(setDefaultTaxInvoice.fulfilled, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
      })
      .addCase(setDefaultTaxInvoice.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getBusinessInfoByTaxCode.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getBusinessInfoByTaxCode.fulfilled, (state, action: PayloadAction<any>) => {
        state.businessInfo = action.payload?.result || null;
        state.isLoading = false;
      })
      .addCase(getBusinessInfoByTaxCode.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getBusinessInfoBySlug.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getBusinessInfoBySlug.fulfilled, (state, action: PayloadAction<any>) => {
        state.businessInfo = action.payload?.result || null;
        state.isLoading = false;
      })
      .addCase(getBusinessInfoBySlug.rejected, (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default taxInvoiceSlice.reducer;
