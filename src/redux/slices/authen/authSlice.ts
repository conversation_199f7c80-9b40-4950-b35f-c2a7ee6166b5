import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { axiosInstance, request } from "@/utils/request";
import { clearItem, getItem, setItem } from "@/utils/storage";
import { StorageKeys } from "@/constants/storageKeys";
import { getAccessToken, getPhoneNumber, getUserInfo } from "zmp-sdk";
import { IUser, UserZalo } from "@/types/user";
import { formatPhoneNumber, showToast, trackError, trackEvent } from "@/utils/common";
import md5 from "md5";

interface AuthState {
  user?: IUser;
  userZalo?: UserZalo;
  token?: string;
  loading: boolean;
}

const initialState: AuthState = {
  user: undefined,
  userZalo: undefined,
  token: undefined,
  loading: true,
};

const NullHeader = {
  headers: {
    Authorization: null,
  },
};

export const loginForWeb = createAsyncThunk(
  "auth/loginForWeb",
  async (
    data: {
      userPhoneZalo: string;
      password: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const { userPhoneZalo, password } = data;
      const response: any = await request(
        "post",
        `/api/user/authuser/login`,
        {
          provider: "Phone",
          phoneNumber: formatPhoneNumber(userPhoneZalo),
          password: md5(password),
        },
        NullHeader
      );
      await setItem(StorageKeys.AccessToken, response.accessToken);
      await setItem(StorageKeys.RefreshToken, response.refreshToken);
      await setItem(StorageKeys.User, response);
      axiosInstance.defaults.headers.common.Authorization = `Bearer ${response.accessToken}`;

      return response;
    } catch (error: any) {
      if (error.message === "Invalid identifier or password") {
        showToast({
          content: "Số điện thoại hoặc mật khẩu không đúng",
          type: "error",
        });
      }
      rejectWithValue(error);
    }
  }
);

export const registerForWeb = createAsyncThunk(
  "auth/registerForWeb",
  async (data: { phone: string; password: string; referCode: string }) => {
    try {
      const { phone, password, referCode } = data;

      const response: any = await request(
        "post",
        `/api/user/authuser/register`,
        {
          provider: "Phone",
          phoneNumber: formatPhoneNumber(phone),
          password,
          referralCode: referCode,
          fullname: phone,
        },
        NullHeader
      );
      await setItem(StorageKeys.AccessToken, response.accessToken);
      await setItem(StorageKeys.RefreshToken, response.refreshToken);
      await setItem(StorageKeys.User, response);
      axiosInstance.defaults.headers.common.Authorization = `Bearer ${response.accessToken}`;

      return response;
    } catch (error: any) {
      return error;
    }
  }
);

export const getUser = createAsyncThunk("auth/getUser", async () => {
  const response: any = await request("get", "/api/user/me");
  return response;
});

export const updateUser = createAsyncThunk("auth/updateUser", async (data: any) => {
  const response: any = await request("put", `/api/users/me`, data);
  return response;
});

export const updateMe = createAsyncThunk("auth/updateMe", async (data: any) => {
  const response: any = await request("put", `/api/user/me`, data);
  return response;
});

export const changePassword = createAsyncThunk("auth/changePassword", async (data: any) => {
  const response: any = await request("post", `/api/user/authuser/setpass`, data);
  return response;
});

export const loadTokenFromStorage = createAsyncThunk("auth/loadTokenFromStorage", async () => {
  const token = await getItem(StorageKeys.AccessToken);
  return token;
});

export const getMyParent = createAsyncThunk("auth/getMyParent", async () => {
  const response: any = await request("get", "/api/my-parent/");
  return response;
});

export const getUserZalo = createAsyncThunk("auth/getUserZalo", async () => {
  const response = await getUserInfo();
  return response.userInfo;
});

export const authZalo = createAsyncThunk("auth/authZalo", async (referralCode?: string) => {
  try {
    let tokenPhone = await getPhoneNumber();
    if (!tokenPhone?.token) {
      tokenPhone = await getPhoneNumber();
      if (!tokenPhone?.token) {
        const errorMsg = "You don't have permission to call this api";
        return { error: errorMsg };
      }
    }

    const accessToken = await getAccessToken();

    if (!tokenPhone?.token || !accessToken) return;

    const { userInfo } = await getUserInfo({ autoRequestPermission: true });
    const response: any = await request(
      "post",
      `/api/user/authuser/activeaccount`,
      {
        provider: "Zalo",
        phoneNumber: "",
        zlPhoneToken: tokenPhone.token,
        zlAccessToken: accessToken,
        fullname: userInfo.name,
        avatar: userInfo.avatar,
        zaloId: userInfo.id,
        zaloIdByOA: userInfo.idByOA,
        password: "",
        referralCode,
      },
      NullHeader
    );

    await setItem(StorageKeys.AccessToken, response.accessToken);
    await setItem(StorageKeys.RefreshToken, response.refreshToken);
    await setItem(StorageKeys.User, JSON.stringify(response));
    axiosInstance.defaults.headers.common.Authorization = `Bearer ${response.accessToken}`;

    return response;
  } catch (error: any) {
    return { error: error.message, data: error };
  }
});

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      clearItem();
      state.token = undefined;
      state.user = undefined;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    updateUserPoint: (state, action) => {
      if (state.user) {
        state.user.point = action.payload;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginForWeb.pending, (state) => {})
      .addCase(loginForWeb.fulfilled, (state, action) => {
        state.token = action.payload?.accessToken;
        state.user = action.payload;
      })
      .addCase(loginForWeb.rejected, (state) => {})
      .addCase(registerForWeb.pending, (state) => {})
      .addCase(registerForWeb.fulfilled, (state, action) => {
        state.token = action.payload?.accessToken;
        if (action.payload?.idFor) {
          state.user = action.payload;
        }
      })
      .addCase(registerForWeb.rejected, (state) => {})
      .addCase(getUserZalo.pending, (state) => {})
      .addCase(getUserZalo.fulfilled, (state, action) => {
        state.userZalo = action.payload;
      })
      .addCase(getUserZalo.rejected, (state) => {})
      .addCase(authZalo.pending, (state) => {})
      .addCase(authZalo.fulfilled, (state, action) => {
        state.token = action.payload?.accessToken;
        if (action.payload?.idFor) {
          state.user = action.payload;
        }
      })
      .addCase(authZalo.rejected, (state) => {})
      .addCase(getUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(getUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
      })
      .addCase(getUser.rejected, (state) => {
        state.loading = false;
      })
      .addCase(updateUser.pending, (state) => {})
      .addCase(updateUser.fulfilled, (state, action) => {
        state.user = action.payload;
      })
      .addCase(updateUser.rejected, (state) => {})
      .addCase(updateMe.pending, (state) => {})
      .addCase(updateMe.fulfilled, (state) => {})
      .addCase(updateMe.rejected, (state) => {})
      .addCase(changePassword.pending, (state) => {})
      .addCase(changePassword.fulfilled, (state) => {})
      .addCase(changePassword.rejected, (state) => {})
      .addCase(loadTokenFromStorage.pending, (state) => {})
      .addCase(loadTokenFromStorage.fulfilled, (state, action) => {
        state.token = action.payload;
      })
      .addCase(loadTokenFromStorage.rejected, (state) => {})
      .addCase(getMyParent.pending, (state) => {})
      .addCase(getMyParent.fulfilled, (state) => {})
      .addCase(getMyParent.rejected, (state) => {});
  },
});

export const { logout, updateUserPoint } = authSlice.actions;
export default authSlice.reducer;
