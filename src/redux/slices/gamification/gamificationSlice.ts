import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "@/utils/request";

interface GamificationState {
  campaignId?: string;
  apiUrl?: string;
  checksum?: string;
  thumbnailLink?: string;
  requestedAt?: string;
  isAvailable: boolean;
  status?: string;
  message?: string;
  startTime?: string;
  endTime?: string;
  isLoading: boolean;
  error?: string;
}

const initialState: GamificationState = {
  campaignId: undefined,
  apiUrl: undefined,
  checksum: undefined,
  thumbnailLink: undefined,
  requestedAt: undefined,
  isAvailable: false,
  status: undefined,
  message: undefined,
  startTime: undefined,
  endTime: undefined,
  isLoading: false,
  error: undefined,
};

export const fetchGamificationData = createAsyncThunk("gamification/fetchData", async () => {
  try {
    const response = await request<any>("get", `/api/user/gamificationuser`);
    return response;
  } catch (error: any) {}
});

export const getCampaign = createAsyncThunk("gamification/getCampaign", async () => {
  try {
    const response = await request<any>("get", `/api/gamification`);
    return response;
  } catch (error: any) {}
});

const gamificationSlice = createSlice({
  name: "gamification",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchGamificationData.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(fetchGamificationData.fulfilled, (state, action: PayloadAction<any>) => {
        state.campaignId = action.payload.result.campaignId;
        state.apiUrl = action.payload.result.apiUrl;
        state.checksum = action.payload.result.checksum;
        state.thumbnailLink = action.payload.result.thumbnailLink;
        state.requestedAt = action.payload.result.requestedAt;
        state.isAvailable = action.payload.result.isAvailable;
        state.status = action.payload.result.status;
        state.message = action.payload.result.message;
        state.startTime = action.payload.result.startTime;
        state.endTime = action.payload.result.endTime;
        state.isLoading = false;
        state.error = undefined;
      })
      .addCase(fetchGamificationData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(getCampaign.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(getCampaign.fulfilled, (state, action: PayloadAction<any>) => {
        if (action.payload.result) {
          state.campaignId = action.payload.result.id;
          state.thumbnailLink = action.payload.result.thumbnailLink;
        }
        state.isLoading = false;
        state.error = undefined;
      })
      .addCase(getCampaign.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export default gamificationSlice.reducer;
