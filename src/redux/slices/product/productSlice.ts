import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IProduct, IProductCategory, MockProductCategories } from "../../../types/product";

interface AuthState {
  list: any;
  productDetail: IProduct | null;
  productCategoryInHome: IProductCategory[];
  productCategoryList: IProductCategory[];
  productCategoryListLevel1: IProductCategory[];
  productCategoryListLevel2: IProductCategory[];
  isLoading: boolean;
}

const initialState: AuthState = {
  list: [],
  productDetail: null,
  productCategoryInHome: [],
  productCategoryListLevel1: [],
  productCategoryListLevel2: [],
  productCategoryList: [],
  isLoading: true,
};

interface ICatSearchCondition {
  itemsType?: string;
  search?: string;
  level?: string;
  showInHome?: boolean;
  skip?: number;
  limit?: number;
}

export const getProductCategoryList = createAsyncThunk(
  "product/getProductCategoryList",
  async (conditon?: ICatSearchCondition) => {
    const response: any = await request("get", "/api/user/categoryuser/listcategory", {
      ...conditon,
      skip: conditon?.skip || 0,
      limit: conditon?.limit || 99,
    });
    return response;
  }
);

export const getProductDetail = createAsyncThunk(
  "product/getProductDetail",
  async (itemsCode: string) => {
    const response: any = await request(
      "get",
      `/api/user/itemsuser/detailgroupitems?itemsCode=${itemsCode}`
    );
    return response;
  }
);

export const getProductDetailById = createAsyncThunk(
  "product/getProductDetailById",
  async (itemId: string) => {
    const response: any = await request("get", `/api/user/itemsuser/detailitem?itemId=${itemId}`);
    return response;
  }
);

const authSlice = createSlice({
  name: "product",
  initialState,
  reducers: {
    clearProductDetail: (state) => {
      state.productDetail = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getProductCategoryList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getProductCategoryList.fulfilled, (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        const published = payload.data.filter((item) => item.publish === "Publish");
        state.productCategoryList = published;
        state.productCategoryListLevel1 = published.filter((item) => item.categoryLevel === "1");
        state.productCategoryListLevel2 = published.filter((item) => item.categoryLevel === "2");
        state.productCategoryInHome = published.filter((item) => item.parentId === null);
        state.isLoading = false;
      })
      .addCase(getProductCategoryList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getProductDetail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getProductDetail.fulfilled, (state, action: PayloadAction<IProduct>) => {
        const { payload } = action;
        state.productDetail = payload;
        state.isLoading = false;
      })
      .addCase(getProductDetail.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export default authSlice.reducer;
export const { clearProductDetail } = authSlice.actions;
