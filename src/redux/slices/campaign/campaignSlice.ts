import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { ICampaign } from "../../../types/campaign";

interface CampaignState {
  campaignList: ICampaign[];
  isLoading: boolean;
}

const initialState: CampaignState = {
  campaignList: [],
  isLoading: true,
};

export const getCampaignList = createAsyncThunk(
  "campaign/getCampaignList",
  async () => {
    const now = new Date().toISOString();
    const response: any = await request(
      "get",
      `/api/campaigns?populate=banner&filters[endTime][$gte]=${now}&filters[startTime][$lte]=${now}`
    );
    return response;
  }
);

const campaignSlice = createSlice({
  name: "campaign",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getCampaignList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getCampaignList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          if (payload.data) {
            state.campaignList = payload.data;
          }
          state.isLoading = false;
        }
      )
      .addCase(getCampaignList.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export default campaignSlice.reducer;
