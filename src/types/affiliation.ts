export interface IRecruitmentPage {
  bannerFilePath: string;
  content: string;
  navBarContent: string;
  title: string;
}
export type AffiliationStatus = "Actived" | "InActived";

export interface IAffiliateReport {
  totalCustomers: number;
  customersPurchased: number;
  successfulOrders: number;
  totalRevenue: number;
  pendingCommission: number;
  paidCommission: number;
  pointEarnByShare: number;
  requireMinSpent: number | null;
  levelOneCommissionPercentage: number;
  levelTwoCommissionPercentage: number | null;
  totalUserWithPurchased: number;
  totalUserWithoutPurchase: number;
  thisMonthApprovedCommission: number;
  totalCommissionReceived: number;
}

export type TCommissionType = "Pending" | "Approved";

export interface ICommissionItem {
  fullName: string | null;
  userId: string;
  avatar: string | null;
  orderId: string;
  orderNo: string;
  created: string;
  commissionPrice: number;
}

export interface ISuccessOrderAffiliate {
  orderId: string;
  orderNo: string;
  price: number;
  creator: {
    userId: string;
    fullName: string;
  };
  created: string; // ISO date string
  updated: string; // ISO date string
}
