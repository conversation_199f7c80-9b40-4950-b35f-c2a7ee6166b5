import { VoucherDto } from "@/redux/slices/voucher/voucherSlice";

export interface IVoucherUser {
  id: string;
  voucherUserId: string;
  voucherId: string;
  userId: string;
  shopId: string;
  exchangePoints: number;
  usedDate: string[];
  created: string;
  updated: string;
  voucherDetail: VoucherDto;
  quantityAvailable: number;
}
export interface IDataCreateVoucherUser {
  voucherId: string;
  shopId: string;
}
