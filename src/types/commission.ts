export interface ICommissionReport {
  commissionReportId: string | null;
  userId: string;
  revenue: number;
  commissionValue: number;
  personalIncomeTax: number;
  fullName: string;
  bankAccountName: string;
  typePay: string; // enum
  bankName: string;
  bankAccountNumber: string;
  month: number;
  year: number;
  paymentStatus: string; // enum
  created: string; // ISO Date string
  updated: string; // ISO Date string
}
