export interface IAddress {
  id: string;
  address: string;
  shippingAddressId: string;
  provinceId: string;
  provinceName: string;
  districtId: string;
  districtName: string;
  wardId: string;
  wardName: string;
  fullName: string;
  phoneNumber: string;
  isDefault: boolean;
}

export interface IProvince {
  provinceID: string;
  provinceName: string;
}
export interface IDistrict {
  districtID: string;
  districtName: string;
  provinceID: string;
}
export interface IWard {
  wardID: string;
  wardName: string;
  districtID: string;
}

export interface IBank {
  vn_name: string;
  en_name: string;
  bankId: string;
  bicCode: string;
  atmBin: string;
  cardLength: number;
  shortName: string;
  bankCode: string;
  type: string;
  napasSupported: boolean;
  status: string;
  channel: string;
}
