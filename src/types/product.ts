export interface IProductCategory {
  categoryId: string;
  parentId: string;
  categoryName: string;
  categoryIcon: string;
  isHome?: boolean;
  categoryLevel: string;
  image?: { link: string };
}

export interface IProduct {
  rating: number;
  itemsId?: string;
  categoryId: string;
  images: [
    {
      link: string;
      type: string;
    }
  ];
  isTop: boolean;
  isVariant: boolean;
  itemsCode: string;
  itemsHeight: number;
  itemsInfo?: any;
  itemsLength: number;
  itemsName: string;
  itemsPosition: number;
  itemsType: string;
  itemsWeight: number;
  itemsWidth: number;
  listVariant: IProductVariant[];
  partnerId: string;
  seoTags: any[];
  shopId: string;
  sold: number;
  subCategoryId: string;
  warehouseId: string;
  price?: number;
  priceReal?: number;
  variantImage?: {
    link?: string;
    type?: string;
  };
  priceCapital?: number;
  variantNameOne?: string | null;
  variantNameThree?: string | null;
  variantNameTwo?: string | null;
  variantValueOne?: string | null;
  variantValueThree?: string | null;
  variantValueTwo?: string | null;
  quantity: number;
  extraItemOptionGroups?: IExtraItemOptionGroups[];
  extraOptions?: string[];
  note?: string;
  categoryIds?: string[];
  taxAmount?: string;
  taxRate?: string;
  totalAfterTax?: number;
  totalBeforeTax?: number;
}

export interface IExtraItemOptionGroups {
  itemOptionGroupId: string;
  itemOptionIds: string[];
}

export interface ISelectedVariant {
  variantNameOne?: string | null;
  variantValueOne?: string | null;
  variantNameTwo?: string | null;
  variantValueTwo?: string | null;
  variantNameThree?: string | null;
  variantValueThree?: string | null;
}

export interface IProductVariant {
  itemsId?: string;
  price: number;
  priceCapital?: number;
  priceReal?: number;
  quantity: number;
  quantityPurchase?: number;
  sellOver?: boolean;
  variantImage?: {
    link: string;
    type: string;
  };
  variantNameOne?: string | null;
  variantNameThree?: string | null;
  variantNameTwo?: string | null;
  variantValueOne?: string | null;
  variantValueThree?: string | null;
  variantValueTwo?: string | null;
}

export interface IPagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface IProductImage {
  type: string;
  link: string;
}

export interface IProductSearchCondition {
  search?: string;
  itemsType?: string;
  categoryId?: string | null;
  subCategoryId?: string | null;
  showInHome?: boolean;
  parentId?: string;
  skip?: number;
  limit?: number;
  sortBy?: {
    field: string;
    order: "default" | "asc" | "desc";
  };
  shopId?: string;
  minPrice?: number;
  maxPrice?: number;
  priceFilter?: boolean;
  forceReload?: boolean;
  sortField?: string;
  sortOrder?: "default" | "asc" | "desc";
}

export const MockProductCategories = [
  {
    categoryId: "1",
    categoryName: "Cà phê",
    categoryIcon: "/demo/images/cat1.png",
    isHome: true,
    categoryLevel: "1",
    cateChildren: null,
    parentId: "",
  },
  {
    categoryId: "2",
    categoryName: "Trà trái cây",
    categoryIcon: "/demo/images/cat2.png",
    isHome: true,
    categoryLevel: "1",
    cateChildren: null,
    parentId: "",
  },
  {
    categoryId: "3",
    categoryName: "Trà sữa",
    categoryIcon: "/demo/images/cat3.png",
    isHome: true,
    categoryLevel: "1",
    cateChildren: null,
    parentId: "",
  },
  {
    categoryId: "4",
    categoryName: "Trà xanh",
    categoryIcon: "/demo/images/cat4.png",
    isHome: true,
    categoryLevel: "1",
    cateChildren: null,
    parentId: "",
  },
  {
    categoryId: "5",
    categoryName: "Bánh",
    categoryIcon: "/demo/images/cat5.png",
    isHome: true,
    categoryLevel: "1",
    cateChildren: null,
    parentId: "",
  },
  {
    categoryId: "6",
    categoryName: "Test",
    categoryIcon: "/demo/images/cat6.png",
    isHome: true,
    categoryLevel: "1",
    cateChildren: null,
    parentId: "",
  },
];

export const MockProducts = [
  {
    id: 1,
    attributes: {
      name: "Lashrehab Rouge Allure",
      price: 900000,
      commission: 0,
      bonusPoint: 0,
      discount: 20,
      description: "Test",
      quantity: 10,
      createdAt: "07/01/2025",
      updatedAt: "07/01/2025",
      publishedAt: "07/01/2025",
      image: {
        data: [
          {
            attributes: {
              formats: "image/png",
              url: "/demo/images/product.png",
            },
          },
        ],
      },
      product_cats: {
        data: [],
      },
      productVariants: [],
      selectVariant: null,
    },
  },
  {
    id: 2,
    attributes: {
      name: "Lashrehab Rouge Allure",
      price: 900000,
      commission: 0,
      bonusPoint: 0,
      discount: 20,
      description: "Test",
      quantity: 10,
      createdAt: "07/01/2025",
      updatedAt: "07/01/2025",
      publishedAt: "07/01/2025",
      image: {
        data: [
          {
            attributes: {
              formats: "image/png",
              url: "/demo/images/product.png",
            },
          },
        ],
      },
      product_cats: {
        data: [],
      },
      productVariants: [],
      selectVariant: null,
    },
  },
  {
    id: 3,
    attributes: {
      name: "Lashrehab Rouge Allure",
      price: 900000,
      commission: 0,
      bonusPoint: 0,
      discount: 20,
      description: "Test",
      quantity: 10,
      createdAt: "07/01/2025",
      updatedAt: "07/01/2025",
      publishedAt: "07/01/2025",
      image: {
        data: [
          {
            attributes: {
              formats: "image/png",
              url: "/demo/images/product.png",
            },
          },
        ],
      },
      product_cats: {
        data: [],
      },
      productVariants: [],
      selectVariant: null,
    },
  },
  {
    id: 4,
    attributes: {
      name: "Lashrehab Rouge Allure",
      price: 900000,
      commission: 0,
      bonusPoint: 0,
      discount: 20,
      description: "Test",
      quantity: 10,
      createdAt: "07/01/2025",
      updatedAt: "07/01/2025",
      publishedAt: "07/01/2025",
      image: {
        data: [
          {
            attributes: {
              formats: "image/png",
              url: "/demo/images/product.png",
            },
          },
        ],
      },
    },
  },
  {
    id: 5,
    attributes: {
      name: "Lashrehab Rouge Allure",
      price: 900000,
      commission: 0,
      bonusPoint: 0,
      discount: 20,
      description: "Test",
      quantity: 10,
      createdAt: "07/01/2025",
      updatedAt: "07/01/2025",
      publishedAt: "07/01/2025",
      image: {
        data: [
          {
            attributes: {
              formats: "image/png",
              url: "/demo/images/product.png",
            },
          },
        ],
      },
      product_cats: {
        data: [],
      },
      productVariants: [],
      selectVariant: null,
    },
  },
];
