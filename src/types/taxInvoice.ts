export interface ITaxInvoiceConfiguration {
  taxInvoiceId?: string;
  shopId: string;
  userId?: string;
  taxCode: string;
  name: string;
  address?: string;
  email?: string;
  phoneNumber?: string;
  isDefault?: boolean;
  created?: string;
  updated?: string;
  taxPayerType?: "Business" | "Individual";
}

export interface ITaxInvoiceBusinessInfo {
  name: string;
  address: string;
  taxCode: string;
  email?: string;
  phoneNumber?: string;
}
