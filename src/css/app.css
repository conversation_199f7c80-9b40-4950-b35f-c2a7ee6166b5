@import url("https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");
@import "react-image-gallery/styles/css/image-gallery.css";
@import "./fonts.css";

* {
  font-family: "Google Sans", "Helvetica Neue", sans-serif !important;
}

html,
body {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

#app {
  max-width: 450px;
  margin: auto;
}

.zaui-btn-container .zaui-btn-icon {
  align-items: center;
  display: flex;
  margin-left: 8px;
}

@media (min-width: 450px) {
  #app {
    max-width: 450px;
    margin: auto;
  }

  .zaui-header {
    max-width: 450px !important;
    min-width: 0 !important;
    left: 50%;
    transform: translate(-50%);
  }

  .web-bottom-tab {
    max-width: 450px;
    width: 100%;
    left: unset !important;
    right: unset !important;
  }

  html,
  body {
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  html::-webkit-scrollbar,
  body::-webkit-scrollbar {
    display: none;
  }
}

.zaui-header {
  height: calc(var(--zaui-safe-area-inset-top, 0px) + 60px);
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

.page {
  padding: 16px 16px 96px 16px;
}

.section-container {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.label {
  white-space: nowrap;
}

.root {
  min-width: none;
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* .product .Mui-selected {
  background-color: #fff;
} */

.product-detail-action :first-child {
  width: 26px;
}

.bottom-mainmenu a {
  min-width: 0;
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: 1;
}

.bottom-mainmenu {
  display: flex !important;
  justify-content: space-between;
}

.bottom-mainmenu .Mui-selected {
  font-size: 9 !important;
  font-weight: 700;
  color: #0973ba;
}

.order-history .Mui-selected {
  color: #ee4d2d;
  font-weight: 700;
}

.collab-history .Mui-selected {
  color: #0973ba !important;
  font-weight: 700 !important;
  font-size: 14 !important;
  display: flex !important;
  gap: 1rem;
  justify-content: space-between !important;
  width: 50%;
}

.bottom-mainmenu .label {
  white-space: nowrap;
}

.bottom-mainmenu .root {
  min-width: none;
}

.popup-add-to-cart .MuiDialog-scrollPaper {
  align-items: flex-end;
}

.popup-add-to-cart .MuiDialog-paperFullScreen {
  height: max-content;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.input-discount-popup-add-to-cart input {
  /* padding: 2; */
  border: #ee4d2d 1px solid;
  border-radius: 5px;
}

.input-discount-popup-add-to-cart label {
  /* top: -13px; */
  font-size: 12px;
  color: #ee4d2d;
}

.homepage {
  /* background-image: url("/images/Bg header.png"); */
}

/* .zaui-bottom-navigation-content {
    height: 60px;
} */

.zaui-bottom-navigation-content {
  height: calc(var(--zaui-safe-area-inset-bottom) + 60px);
}

.voucher-profile .wrap {
  width: 100%;
  background-color: rgb(240, 240, 240);
}

/* .voucher-profile .coupon {
    display: flex;
    overflow: hidden;
    border-radius: 10px;
    position: relative;
} */

.voucher-profile .wrap .coupon {
  display: flex;
}

.voucher-profile .coupon-left {
  width: 30%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.voucher-profile .coupon-left::before {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: rgb(240, 240, 240);
  top: -5px;
  right: -5px;
  border-radius: 50%;
}

.voucher-profile .coupon-left::after {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: rgb(240, 240, 240);
  bottom: -5px;
  right: -5px;
  border-radius: 50%;
}

.voucher-profile .coupon-con {
  height: 100%;
  width: 70%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.voucher-profile .coupon-con::before {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: rgb(240, 240, 240);
  top: -5px;
  left: -5px;
  border-radius: 50%;
}

.voucher-profile .coupon-con::after {
  content: "";
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: rgb(240, 240, 240);
  bottom: -5px;
  left: -5px;
  border-radius: 50%;
}

.popup-voucher .MuiDialog-paperFullScreen {
  background-color: #e9ebed;
  height: 90%;
  max-width: 450px;
}

.popup-voucher .MuiDialog-scrollPaper {
  align-items: flex-end;
}

.popup-voucher .MuiDialogContent-root {
  background-color: rgb(240, 240, 240) !important;
}

.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center; /* Để căn giữa nội dung trong popup */
}

.popup-content p {
  margin-bottom: 20px;
}

.popup-content button {
  padding: 10px 20px;
  background-color: #ee4d2d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease; /* Hiệu ứng hover */
}

.popup-content button:hover {
  background-color: #ee4d2d; /* Đổi màu khi hover */
}

.zaui-bottom-navigation-item .zaui-bottom-navigation-item-icon {
  justify-content: center;
}

.react-datepicker-wrapper input {
  padding: 17px;
  border-radius: 8px;
  border: 1px solid #b9bdc1;
  min-width: min-content;
  font-size: 1rem;
  width: 100%;
}

.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker-popper {
  left: 10px !important;
}

.dots-banner-with-branch li button:before {
  font-size: 10px !important;
  color: #b1b1b1 !important;
}

.slick-dots li.slick-active button:before {
  font-size: 10px;
  color: #fff !important;
}

.dots-banner-with-branch {
  bottom: 12px !important;
}

.dots-banner-with-branch li.slick-active button:before {
  font-size: 10px;
  color: #fff !important;
}

.search-input {
  background: transparent !important;
}

.search-input .MuiOutlinedInput-root {
  border-radius: 10px;
  background: #fff;
}

.search-input .MuiOutlinedInput-root .MuiOutlinedInput-input {
  height: 30px;
}

.zaui-header::after {
  background: none;
}

.no-data-view {
  width: 100%;
}

.image-gallery-slides {
  height: 50vh;
}

.image-gallery-slides .image-gallery-slide {
  height: 100%;
}

/* .image-gallery-thumbnails {
  background-color: #fff;
} */
.image-gallery-thumbnails-wrapper {
  margin-top: 5px;
  margin-inline: 10px;
}

.image-gallery-thumbnails .image-gallery-thumbnails-container {
  text-align: left;
}

.image-gallery-thumbnails .image-gallery-thumbnail {
  width: 56px;
  height: 56px;
  border-radius: 8px;
}
.image-gallery-thumbnails .image-gallery-thumbnail img {
  border-radius: 5px;
}

.refer-container .slick-dots {
  bottom: 10px;
}

.input-search > div > input::placeholder {
  color: #b3b3b3;
}
.input-search > div > input {
  color: black;
  padding: 15px 45px;
}
.box-product-item > div {
  margin-inline: 5px;
}

/* .box-product-item {
  width: calc(calc(100vw - 40px) / 2) !important;
} */

.btn-menu-container {
  width: calc(calc(100vw - 40px) / 5);
  padding: 0;
}

@media (min-width: 450px) {
  .btn-menu-container {
    width: calc(calc(450px - 40px) / 5) !important;
  }
}

.popup-add-to-cart .MuiDialog-paperFullScreen {
  max-width: 450px;
}

.zaui-header-title {
  font-weight: 700;
  font-size: 19px;
}
.promo-game {
  width: 100% !important;
  height: 100% !important;
}
.list-news-slider .slick-slide {
  padding: 0 8px; /* tạo khoảng cách trái-phải giữa các slide */
}

.list-news-slider .slick-list {
  margin: 0 -8px; /* để tránh bị thụt lề do padding */
}

/* Voucher slider styles for consistent height */
.voucher-slider .slick-slide {
  height: 160px !important;
}

.voucher-slider .slick-slide > div {
  height: 100% !important;
}

.voucher-slider .slick-track {
  display: flex !important;
  align-items: stretch !important;
  gap: 10px;
}

.voucher-slider .slick-slide .wrap {
  height: 100% !important;
}

.voucher-slider .slick-slide .coupon {
  height: 100% !important;
}

.game-box-fixed {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1200;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  background: #fff;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
}
.game-box-fixed:hover {
  transform: scale(1.08);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
}

@keyframes shake {
  0% {
    transform: rotate(0deg);
  }
  20% {
    transform: rotate(8deg);
  }
  40% {
    transform: rotate(0eg);
  }
  60% {
    transform: rotate(-8deg);
  }
  80%,
  100% {
    transform: none;
  }
}
.game-bg {
  background-image: url("../../public/images/game-bg.png");
  background-repeat: "no-repeat";
  background-size: "cover";
  background-position: "center";
}
