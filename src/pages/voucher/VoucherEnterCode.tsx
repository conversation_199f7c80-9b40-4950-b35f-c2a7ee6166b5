import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { Box, Button, TextField, Typography, Card, Stack, CircularProgress } from "@mui/material";
import { AppDispatch } from "@/redux/store";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import { showToast } from "@/utils/common";
import { getDetailVoucherByCodePublic } from "@/redux/slices/voucher/voucherSlice";
import { COLORS } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

export default function VoucherEnterCode() {
  const [voucherCode, setVoucherCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { color } = useConfigApp();

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setVoucherCode(event.target.value);
  };

  const handleApplyVoucher = async () => {
    if (!voucherCode.trim()) {
      showToast({
        content: "Vui lòng nhập mã ưu đãi",
        type: "error",
      });
      return;
    }

    setIsLoading(true);
    try {
      // First, try to get voucher details by code
      const voucherDetailRes = await dispatch(getDetailVoucherByCodePublic(voucherCode.trim()));

      if (voucherDetailRes?.payload?.result.voucherId) {
        // Voucher found, navigate to voucher detail page
        navigate(`/voucher/${voucherCode.trim()}`);
        return;
      } else {
        // Show error message for non-existent voucher code
        showToast({
          content: "Mã voucher không tồn tại",
          type: "error",
        });
      }
    } catch (error) {
      console.error("Error applying voucher:", error);
      showToast({
        content: "Có lỗi xảy ra khi áp dụng mã ưu đãi",
        type: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleApplyVoucher();
    }
  };

  return (
    <FrameContainerFull title="Nhập mã ưu đãi" onBackClick={() => navigate(Router.voucher.home)}>
      <Box
        sx={{
          minHeight: "100vh",
          px: 2,
          py: 3,
        }}
      >
        <Card
          sx={{
            p: 3,
            borderRadius: 3,
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
            background: "rgba(255, 255, 255, 0.95)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          <Stack spacing={2}>
            <Box display={"flex"} gap={1} alignItems={"center"}>
              <img src="/icons/voucher.png" height={30} width={30} alt="voucher" />

              {/* Title */}
              <Typography variant="h3" fontSize={18} fontWeight={700} textAlign="center">
                Nhập mã ưu đãi
              </Typography>
            </Box>

            {/* Input Field */}
            <TextField
              fullWidth
              value={voucherCode}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Nhập mã ưu đãi..."
              variant="outlined"
              disabled={isLoading}
              InputProps={{
                sx: {
                  borderRadius: 2,
                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                  "& .MuiOutlinedInput-notchedOutline": {
                    border: "1px solid rgba(0, 0, 0, 0.23)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    border: "1px solid rgba(0, 0, 0, 0.23)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    border: "1px solid rgba(0, 0, 0, 0.23)",
                  },
                },
              }}
              sx={{
                "& .MuiInputBase-input": {
                  fontSize: 16,
                  fontWeight: 500,
                },
              }}
            />
            {/* Description */}
            <Typography variant="body1" color={COLORS.neutral5} fontWeight={500}>
              Nhập mã ưu đãi của bạn để nhận voucher hoặc điểm thưởng
            </Typography>

            {/* Apply Button */}
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handleApplyVoucher}
              disabled={!voucherCode.trim() || isLoading}
              sx={{
                py: 1.5,
                borderRadius: 2,
                backgroundColor: color.primary,
                boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                fontSize: 16,
                fontWeight: 600,
                textTransform: "none",
                "&:disabled": {
                  background: "rgba(0, 0, 0, 0.1)",
                  color: "rgba(0, 0, 0, 0.3)",
                },
                "&:hover": {
                  background: color.primary,
                },
                transition: "all 0.3s ease",
              }}
            >
              {isLoading ? <CircularProgress size={24} color="inherit" /> : "Áp dụng"}
            </Button>
          </Stack>
        </Card>
      </Box>
    </FrameContainerFull>
  );
}
