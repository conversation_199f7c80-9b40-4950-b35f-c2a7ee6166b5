import React from "react";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import { useNavigate } from "react-router-dom";
import { Router } from "@/constants/Route";
import VoucherTabsOptimized from "./VoucherTabsOptimized";
import { RightPoint } from "./Voucher";

export default function VoucherTabsPage() {
  const navigate = useNavigate();

  return (
    <FrameContainerFull
      title="Phần thưởng"
      rightComponent={<RightPoint />}
      onBackClick={() => navigate(Router.promotion.index)}
    >
      <VoucherTabsOptimized />
    </FrameContainerFull>
  );
}
