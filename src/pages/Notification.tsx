import React, { useEffect } from "react";
import FrameContainerFull from "../components/layout/ContainerFluid";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import { getNotifications } from "../redux/slices/notification/notificationSlice";
import { CircularProgress, Stack, Typography } from "@mui/material";
import { INotification } from "../types/INotification";
import dayjs from "dayjs";

export default function Notification() {
  const dispatch = useDispatch<AppDispatch>();
  const { data, isLoading } = useSelector(
    (state: RootState) => state.notification,
  );

  useEffect(() => {
    dispatch(getNotifications());
  }, []);

  /**
   * @description styles
   */
  const itemContainerStyle: React.CSSProperties = {
    padding: 12,
    margin: 6,
    background: "#fff",
    borderRadius: 4,
  };
  const titleItemStyle: React.CSSProperties = {
    fontSize: 14,
  };
  const timeItemStyle: React.CSSProperties = {
    color: "#969595",
    fontSize: 12,
    paddingTop: 4,
    textAlign: "right",
  };

  /**
   * @description render UI
   */
  const renderNotificationItem = (item: INotification) => {
    return (
      <Stack key={`noti-${item.id}`} style={itemContainerStyle}>
        <Typography style={titleItemStyle}>{item.content}</Typography>
        {/* <Typography style={timeItemStyle}>
          {dayjs(item.createdAt).format("HH:mm DD/MM/YYYY")}
        </Typography> */}
      </Stack>
    );
  };

  return (
    <FrameContainerFull
      title="Thông báo"
      overrideStyle={{ background: "transparent" }}
    >
      {isLoading ? (
        <Stack sx={styles.loadingContainer}>
          <CircularProgress />
        </Stack>
      ) : data && data.length > 0 ? (
        <Stack style={{ paddingInline: 2, paddingBottom: 64 }}>
          {data?.map(renderNotificationItem)}
        </Stack>
      ) : (
        <Stack direction="row" justifyContent={"center"} padding={2}>
          Không có thông báo nào
        </Stack>
      )}
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 4,
    width: "100%",
  },
};
