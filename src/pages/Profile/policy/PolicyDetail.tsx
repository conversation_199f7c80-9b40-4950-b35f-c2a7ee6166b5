import { Divider, Stack } from "@mui/material";
import React, { useEffect } from "react";
import { useLocation, useParams } from "react-router-dom";
import FrameContainer from "../../../components/layout/Container";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../redux/store";
import { COLORS } from "@/constants/themes";

export default function PolicyDetail() {
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const policy = location.state?.policy;


  return (
    <FrameContainer title={policy?.title || ""}>
      <Stack sx={styles.contentContainer}>
        <Stack p={2} textAlign="center" fontWeight={700}>
          <span> {policy?.title}</span>
        </Stack>
        <Divider />
        <Stack p={2}>
          <div
            dangerouslySetInnerHTML={{
              __html: policy?.content || "",
            }}
          />
        </Stack>
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
    fontSize: "14px",
    backgroundColor: COLORS.white,
    paddingBlock: "12px",
    borderRadius: "10px",
    color: COLORS.black,
  },
};
