import React, { useState } from "react";
import { Box, Button, Checkbox, Divider, FormControlLabel, Stack, Typography } from "@mui/material";
import { COLORS, commonStyle } from "@/constants/themes";
import FrameContainer from "@/components/layout/Container";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import CheckIcon from "@/components/icon/CheckIcon";
import { useNavigate } from "react-router-dom";
import { Router } from "@/constants/Route";
import PopupAffliateRegister from "./components/AffiliateRegisterSheet";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

export default function RegisterPartner() {
  const { color, ...appConfig } = useConfigApp();
  const { recruitmentPage } = useSelector((state: RootState) => state.affiliation);
  const { showAlert } = useAlert();
  const navigate = useNavigate();
  const loyaltyPolicy =
    recruitmentPage?.content ||
    `<p>Đ<PERSON> đáp ứng nhu cầu sử dụng của người dùng, hài hòa lợi ích của các bên và tuân thủ các quy định của pháp luật, ${appConfig?.shopName} có quyền điều chỉnh bất kỳ nội dung nào trong thoả thuận này tại bất kỳ thời điểm nào bằng việc đăng tải bản cập nhật và bản cập nhật sẽ có hiệu lực ngay lập tức sau khi được đăng tải.</p>
  `;
  const [openPopup, setOpenPopup] = useState(false);
  const { user } = useSelector((state: RootState) => state.auth);

  const [checked, setChecked] = useState(false);
  const [isReged, setIsReged] = useState(false);

  const handleChange = (event) => {
    setChecked(event.target.checked);
  };

  const onClickRegister = async () => {
    showAlert({
      icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
      title: "Đăng ký thành công",
      content: "Chào mừng bạn đến với trung tâm quản lý tiếp thị liên kết của chúng tôi",
      buttons: [
        {
          title: "Quay lại",
          action: () => {
            navigate(Router.profile.index);
          },
          customButtonStyle: {
            ...styles.alertBtn,
            border: `1px solid ${color.primary}`,
            color: color.primary,
          },
        },
        {
          title: "Quản lý",
          action: () => {
            navigate(Router.collabhistory.index);
          },
          customButtonStyle: {
            ...styles.alertBtn,
            background: color.primary,
            border: `1px solid ${color.primary}`,
          },
        },
      ],
      titleStyle: {
        ...commonStyle.headline18,
        color: COLORS.neutral1,
      },
      contentStyle: {
        color: COLORS.neutral5,
      },
    });
  };

  return (
    <FrameContainer
      title="Đăng ký đối tác"
      childOrdStl={{
        padding: 0,
      }}
    >
      <Stack>
        <Box marginBottom={"5px"}>
          <img
            style={styles.imageContainer}
            src={recruitmentPage.bannerFilePath || "/demo/images/banner.png"}
          />
        </Box>
        <Box padding={"10px"}>
          <Box style={styles.contentBox}>
            <Typography
              style={{
                ...commonStyle.headline18,
                textAlign: "center",
                color: COLORS.neutral1,
              }}
            >
              {recruitmentPage.title ||
                `Chào mừng bạn đến với chương trình Affiliate ${appConfig?.shopName}`}
            </Typography>
            <Divider
              style={{
                marginTop: 5,
                marginBottom: 15,
              }}
            />
            <Box
              dangerouslySetInnerHTML={{
                __html: loyaltyPolicy,
              }}
              style={styles.contentBoxStyle}
            />
            <Box>
              {!user?.affiliationStatus ? (
                <>
                  {/* <FormControlLabel
                    control={<Checkbox checked={checked} onChange={handleChange} />}
                    label="Tôi đã hiểu và đồng ý với chương trình nhà phân phối"
                  /> */}
                  <Button
                    style={{
                      ...styles.btnReg,
                      background: color.primary,
                    }}
                    // disabled={!checked}
                    // onClick={onClickRegister}
                    onClick={() => setOpenPopup(true)}
                  >
                    {recruitmentPage.navBarContent || "Đăng ký Affiliate"}
                  </Button>
                  <PopupAffliateRegister open={openPopup} onClose={() => setOpenPopup(false)} />
                </>
              ) : (
                <>
                  <Button
                    style={{
                      ...styles.btnReg,
                      color: COLORS.neutral2,
                      fontWeight: 400,
                      border: `1px solid ${COLORS.neutral11}`,
                    }}
                  >
                    Bạn sẽ sử dụng chức năng này sau khi đơn đăng ký được phê duyệt
                  </Button>
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  imageContainer: {
    display: "flex",
    borderRadius: 5,
    overflow: "hidden",
    aspectRatio: 2,
    width: "100%",
    backgroundSize: "cover",
    backgroundPosition: "center",
  },
  contentBox: {
    background: COLORS.white,
    borderRadius: "5px",
    padding: "15px",
  },
  contentBoxStyle: {
    height: "calc(100vh - 572px)",
    overflowY: "auto",
    overflowX: "hidden",
  },
  btnReg: {
    width: "100%",
    padding: 12,
    borderRadius: 99,
    color: COLORS.white,
    background: COLORS.neutral17,
    marginTop: "11px",
  },
  alertBtn: {
    width: "48%",
    background: COLORS.white,
    boxShadow: "none",
    padding: "10px",
    color: COLORS.white,
  },
};
