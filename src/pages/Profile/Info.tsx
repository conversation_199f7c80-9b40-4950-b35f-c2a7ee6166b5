import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Base, Divider, <PERSON>, <PERSON>Item, Stack, Typography } from "@mui/material";
import React, { useState } from "react";
import { useSelector } from "react-redux";
import FrameContainer from "../../components/layout/Container";
import PopupEditInfo from "../../components/UI/PopupEditInfo";
import PopupView from "../../components/UI/PopupView";
import { RootState } from "../../redux/store";
import { COLORS, commonStyle } from "../../constants/themes";
import { IAccountItem, MyInfoActionItems, MyInfoActionItemType } from "../../constants/Const";
import { RightChevron } from "../../constants/IconSvg";
import PopupEditPassword from "../../components/UI/PopupEditPassword";
import PopupEditProfile from "../../components/UI/PopupEditProfile";
import { Icon } from "../../constants/Assets";
import { useConfigApp } from "@/hooks/useConfigApp";
import EditIcon from "@mui/icons-material/Edit";
import dayjs from "dayjs";

export default function Info() {
  const [openSetPassword, setOpenSetPassword] = useState(false);
  const [openEditProfile, setOpenEditProfile] = useState(false);
  const user = useSelector((state: RootState) => state.auth.user);
  console.log(user);
  const { color, ...appConfig } = useConfigApp();
  const listInfo = [
    {
      label: "Họ và tên",
      value: user?.fullname,
    },
    {
      label: "Ngày sinh",
      value: user?.birthdate !== null && dayjs(user?.birthdate).format("DD/MM/YYYY"),
    },
    {
      label: "Địa chỉ",
      value: user?.address?.trim()
        ? `${user.address} ${[
            user?.wardName ?? "",
            user?.districtName ?? "",
            user?.provinceName ?? "",
          ]
            .filter(Boolean)
            .join(", ")}`
        : [user?.wardName ?? "", user?.districtName ?? "", user?.provinceName ?? ""]
            .filter(Boolean)
            .join(", "),
    },
    {
      label: "Email",
      value: user?.email,
    },
    {
      label: "Số điện thoại",
      value: user?.phoneNumber,
    },
  ];

  const onClickToItem = (item: IAccountItem) => {
    switch (item.id) {
      case MyInfoActionItemType.ChangePass:
        setOpenSetPassword(true);
        break;
    }
  };

  const MyInfoActionItem = (item: IAccountItem) => (
    <ButtonBase
      key={item.id}
      sx={{
        marginBottom: "5px",
        marginTop: "5px",
        alignItems: "center",
        flexDirection: "row",
        gap: 2,
      }}
      onClick={() => onClickToItem(item)}
    >
      <Stack gap={0.5} textAlign={"left"}>
        <span style={{ fontWeight: 700, fontSize: "15px", color: color.primary }}>
          {item.title}
        </span>
        <Typography style={{ color: COLORS.neutral4, fontSize: "11px", fontWeight: 400 }}>
          {item.subTitle}
        </Typography>
      </Stack>
      <RightChevron width={23} height={30} />
    </ButtonBase>
  );

  return (
    <FrameContainer title="Thông tin tài khoản">
      <Stack sx={styles.contentContainer}>
        {/* User Profile Header */}
        <Stack style={styles.headerContainer}>
          <Stack direction="row" alignItems="center" gap={2} style={{ width: "100%" }}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "transparent",
                borderRadius: "50%",
                border: "1px solid #E0E0E0",
                p: 1,
                width: 56,
                height: 56,
              }}
            >
              <img
                width={48}
                height={48}
                style={{ borderRadius: "50%", objectFit: "contain" }}
                src={
                  user?.avatar ??
                  (typeof appConfig.shopLogo === "string" && appConfig.shopLogo
                    ? appConfig.shopLogo
                    : appConfig.shopLogo?.link) ??
                  Icon.logo
                }
                alt="Avatar"
              />
            </Box>
            <Stack style={{ minWidth: 0, width: "100%" }}>
              <Stack
                direction="row"
                alignItems="center"
                gap={1}
                style={{ minWidth: 0, width: "100%" }}
              >
                <Typography
                  sx={{
                    fontWeight: 700,
                    fontSize: 18,
                    color: color.primary,
                    lineHeight: 1,
                  }}
                >
                  {user?.fullname ?? user?.phoneNumber}
                </Typography>
              </Stack>
              {user?.membershipLevel?.levelName && (
                <Typography
                  sx={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: color.primary,
                    opacity: 0.85,
                    mt: 0.5,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "-webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                    whiteSpace: "normal",
                  }}
                >
                  {user.membershipLevel.levelName}
                </Typography>
              )}
            </Stack>
          </Stack>
        </Stack>

        <Stack style={styles.container}>
          <Typography style={{ ...styles.mainTitle, color: color.primary }}>
            Thông tin cơ bản
          </Typography>
          <Divider />
          <List>
            {listInfo?.map((item, idx) => (
              <ListItem
                key={idx}
                style={{
                  padding: "8px 0",
                }}
              >
                <Stack direction={"row"} style={styles.itemContainer}>
                  <Typography style={styles.title}>{item.label}</Typography>
                  <Typography style={{ ...styles.value, color: color.primary }}>
                    {item.value}
                  </Typography>
                </Stack>
              </ListItem>
            ))}
          </List>
          <Stack
            direction="row"
            justifyContent="center"
            sx={{
              marginTop: "25px",
            }}
          >
            <Button
              onClick={() => setOpenEditProfile(true)}
              variant="contained"
              sx={{
                backgroundColor: color.primary,
                color: COLORS.white,
                fontWeight: 700,
                fontSize: 14,
                borderRadius: "25px",
                padding: "12px 30px",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: color.primary,
                  opacity: 0.9,
                },
              }}
            >
              Cập nhật thông tin cá nhân
            </Button>
          </Stack>
        </Stack>

        <Stack style={styles.container}>
          {MyInfoActionItems.map((item, idx) => (
            <Box key={item.id}>
              <MyInfoActionItem {...item} />
              <Divider />
            </Box>
          ))}
        </Stack>
      </Stack>

      <PopupEditProfile open={openEditProfile} setOpen={setOpenEditProfile} />
      <PopupEditPassword {...{ openSetPassword, setOpenSetPassword }} />
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
  },
  headerContainer: {
    background: COLORS.white,
    color: COLORS.neutral2,
    padding: 20,
    marginBottom: 16,
    borderRadius: 10,
  },
  container: {
    background: COLORS.white,
    color: COLORS.neutral2,
    padding: 20,
    marginBottom: 16,
    borderRadius: 10,
  },
  mainTitle: {
    ...commonStyle.headline17,
    color: COLORS.neutral1,
    marginBottom: "10px",
  },
  title: {
    color: COLORS.neutral1,
    fontWeight: 500,
    fontSize: "15px",
    width: "130px",
  },
  value: {
    color: COLORS.neutral13,
    fontWeight: 500,
    fontSize: "15px",
    textAlign: "right",
  },
  itemContainer: {
    width: "100%",
    justifyContent: "space-between",
    alignItems: "center",
  },
};
