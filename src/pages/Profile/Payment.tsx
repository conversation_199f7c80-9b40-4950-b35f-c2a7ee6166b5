import {
  Avatar,
  Box,
  But<PERSON>,
  Divider,
  IconButton,
  List,
  ListItem,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect } from "react";
import FrameContainer from "../../components/layout/Container";
import { COLORS, commonStyle } from "../../constants/themes";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import { Icon } from "@/constants/Assets";
import PopupEditBank from "@/components/UI/PopupEditBank";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getBank } from "@/redux/slices/address/addressSlice";
import { IBank } from "@/types/address";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import { copy, showToast } from "@/utils/common";
import { Clipboard } from "@/constants/IconSvg";

export default function PaymentInfo() {
  const { color } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const { bank } = useSelector((state: RootState) => state.address);
  const dispatch = useDispatch<AppDispatch>();
  useEffect(() => {
    if (!bank || bank.length == 0) {
      dispatch(getBank());
    }
  }, [bank]);
  const userBank: IBank = bank.find((b) => b.shortName === user?.bankName) ?? {
    bankId: "",
    shortName: "",
    vn_name: "",
    en_name: "",
    bankCode: "",
    atmBin: "",
    cardLength: 0,
    bicCode: "",
    type: "",
    napasSupported: false,
    status: "",
    channel: "",
  };

  const listInfoPayment = [
    {
      label: "Tên tài khoản:",
      value: user?.fullname,
    },
    {
      label: "Số tài khoản:",
      value: user?.bankAccountNumber,
    },
    {
      label: "Ngân hàng:",
      value: `${userBank?.vn_name} (${userBank?.shortName})`,
    },
    // {
    //   label: "Chi nhánh",
    //   value: user?.bankInfo?.branch,
    // },
    {
      label: "Số CCCD:",
      value: user?.identityCardNumber,
    },
    {
      label: "Mã số thuế:",
      value: user?.taxCode,
    },
  ];

  const handleCopy = () => {
    copy(user?.referralCode || "", "mã giới thiệu");
  };

  const handleWallet = () => {
    showToast({
      content: "Tính năng đang phát triển",
      type: "blank",
    });
  };

  return (
    <FrameContainerFull title="Thông tin tài khoản">
      <Stack sx={styles.contentContainer}>
        <Box sx={styles.containerHeader}>
          <Stack direction={"row"} alignItems="center" justifyContent={"center"} gap={3}>
            <Avatar src={user?.avatar} style={{ width: "100px", height: "100px" }} />
            <Stack>
              <Typography sx={{ fontSize: 14, color: COLORS.white }}>Tên tài khoản:</Typography>
              <Typography sx={{ fontSize: 16, fontWeight: 700, color: COLORS.white }}>
                {user?.fullname}
              </Typography>
              <Typography sx={{ fontSize: 14, color: COLORS.white }}>Mã giới thiệu:</Typography>
              <Stack direction="row" alignItems="center" gap={0.5}>
                <Typography sx={{ fontSize: 14, fontWeight: 700, color: COLORS.white }}>
                  {user?.referralCode}
                </Typography>
                <IconButton onClick={handleCopy} sx={{ p: 0.5 }}>
                  <Clipboard color={COLORS.white} />
                </IconButton>
              </Stack>
            </Stack>
          </Stack>
        </Box>

        <Stack style={styles.container}>
          <Typography style={{ ...styles.mainTitle, color: color.primary }}>
            Thông tin thanh toán
          </Typography>
          <Divider />
          <List
            style={{
              padding: "15px 0 0",
            }}
          >
            {listInfoPayment?.map((item, idx) => (
              <ListItem
                key={idx}
                style={{
                  padding: "10px 0",
                }}
              >
                <Stack direction={"row"} style={styles.itemContainer}>
                  <Typography style={styles.title}>{item.label}</Typography>
                  <Typography
                    style={{
                      ...styles.value,
                      fontSize: "15px",
                      color: COLORS.neutral13,
                    }}
                  >
                    {item.value}
                  </Typography>
                </Stack>
              </ListItem>
            ))}
          </List>
          <Stack
            direction="row"
            justifyContent="flex-end"
            sx={{
              marginTop: "25px",
            }}
          >
            <PopupEditBank />
          </Stack>
        </Stack>

        <Box paddingInline={2} boxShadow={"0 2px 12px rgba(0,0,0,0.04)"}>
          <Stack
            style={styles.container}
            direction={"row"}
            justifyContent={"space-between"}
            alignItems={"center"}
            gap={1}
          >
            <Box width={"60%"}>
              <Typography style={{ ...styles.mainTitle, color: color.primary }}>
                Liên kết ví thanh toán
              </Typography>
              <Typography style={{ color: COLORS.neutral2 }}>
                Liên kết tài khoản ZaloPay để nhận hoa hồng tự động
              </Typography>
            </Box>

            <Stack>
              <Button
                onClick={() => {
                  handleWallet();
                }}
                style={{
                  ...styles.btnContainer,
                  background: color.primary,
                }}
              >
                Liên kết
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Stack>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  containerHeader: {
    paddingBlock: "40px",
    background: "#1531AD",
  },
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
  },
  container: {
    background: COLORS.white,
    color: COLORS.neutral2,
    padding: "16px",
    marginBottom: 16,
    borderRadius: 10,
  },
  mainTitle: {
    ...commonStyle.headline17,
    color: COLORS.neutral1,
    marginBottom: "10px",
  },
  title: {
    color: COLORS.neutral1,
    fontWeight: 500,
    fontSize: "15px",
    width: "120px",
  },
  value: {
    color: COLORS.neutral13,
    fontWeight: 400,
    fontSize: "15px",
    textAlign: "right",
  },
  itemContainer: {
    width: "100%",
    justifyContent: "space-between",
    // alignItems: "center",
    color: COLORS.neutral2,
  },
  btnContainer: {
    fontSize: "14px",
    fontWeight: 700,
    color: COLORS.white,
    padding: "9px 27px",
    borderRadius: "25px",
  },
};
