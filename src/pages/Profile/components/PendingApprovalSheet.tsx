import React, { useEffect } from "react";
import { Box, Button, Stack, Typography } from "@mui/material";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import { openChat } from "@/utils/openChat";
import { COLORS } from "@/constants/themes";
import BottomSheet from "@/components/common/BottomSheet";
import { Icon } from "@/constants/Assets";
import {
  getAffiliateConfig,
  getAffiliateReport,
} from "@/redux/slices/affiliation/affiliationSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { formatPrice } from "@/utils/formatPrice";

interface PendingApprovalSheetProps {
  open: boolean;
  onClose: () => void;
  onOpen: () => void;
}

const PendingApprovalSheet = ({ open, onClose, onOpen }: PendingApprovalSheetProps) => {
  const { color, ...appConfig } = useConfigApp();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { affiliateConfig } = useSelector((state: RootState) => state.affiliation);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  useEffect(() => {
    if (user && shopId) dispatch(getAffiliateConfig(shopId));
  }, [user, shopId]);
  const pendingContent = (
    <Stack sx={{ padding: 4, textAlign: "center" }} gap={3}>
      <Box paddingInline={4}>
        <img width="100%" src={Icon.affiliatePending} />
      </Box>
      <Typography
        sx={{
          fontSize: "17px",
          fontWeight: "700",
          color: color.primary,
          overflow: "hidden",
          textOverflow: "ellipsis",
          display: "-webkit-box",
          WebkitLineClamp: 3,
          WebkitBoxOrient: "vertical",
          whiteSpace: "normal",
        }}
      >
        {`Tài khoản của bạn đang chờ duyệt để trở thành Đối tác ${appConfig?.shopName}`}
      </Typography>
      <Typography sx={{ fontSize: "12px", color: color.primary }}>
        Cảm ơn bạn đã đồng hành cùng chúng tôi!
      </Typography>
      <Typography sx={{ fontSize: "12px", color: COLORS.neutral3 }}>
        Hiện tại, bạn chưa đáp ứng đủ điều kiện phê duyệt.{" "}
        {affiliateConfig?.advancedCommissionsConfig?.minSpendToApproved
          ? `Hệ thống sẽ tự động xét duyệt và kích
        hoạt quyền lợi sau khi bạn hoàn thành đơn chi tiêu tối thiểu ${formatPrice(
          affiliateConfig?.advancedCommissionsConfig?.minSpendToApproved || ""
        )}.`
          : ``}
      </Typography>
    </Stack>
  );

  const pendingFooter = (
    <Stack direction="row" spacing={2} sx={{ padding: 2 }}>
      <Button
        fullWidth
        variant="contained"
        onClick={() => openChat(appConfig.oaId || "")}
        sx={{ borderRadius: 99, background: color.primary, color: COLORS.white }}
      >
        Liên hệ hỗ trợ
      </Button>
      <Button
        fullWidth
        variant="outlined"
        onClick={() => navigate(Router.menu)}
        sx={{ borderRadius: 99, color: color.primary, borderColor: color.primary }}
      >
        Mua hàng
      </Button>
    </Stack>
  );

  return (
    <BottomSheet
      open={open}
      onClose={onClose}
      onOpen={onOpen}
      sx={{ bgcolor: "white" }}
      height="85vh"
      footer={pendingFooter}
      disableSwipeToOpen
    >
      {pendingContent}
    </BottomSheet>
  );
};

export default PendingApprovalSheet;
