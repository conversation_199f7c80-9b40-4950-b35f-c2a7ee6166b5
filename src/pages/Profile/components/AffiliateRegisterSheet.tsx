import React, { useEffect, useState } from "react";
import {
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Checkbox,
  Button,
  Typography,
  Paper,
  Box,
  FormControlLabel,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Radio,
} from "@mui/material";
import { JustifyContentEnum } from "zmp-ui/cluster";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { Controller, useForm } from "react-hook-form";
import { getBank } from "@/redux/slices/address/addressSlice";
import { registerAffiliate } from "@/redux/slices/affiliation/affiliationSlice";
import { showToast } from "@/utils/common";
import { getUser } from "@/redux/slices/authen/authSlice";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import * as yup from "yup";
import { PhoneRegex } from "@/constants/Const";
import { yupResolver } from "@hookform/resolvers/yup";
import { IBank } from "@/types/address";
import BottomSheet from "@/components/common/BottomSheet";
import { COLORS } from "@/constants/themes";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { openChat } from "@/utils/openChat";
import CheckIcon from "@/components/icon/CheckIcon";
import { Icon } from "@/constants/Assets";

const affiliateSchema = yup.object().shape({
  fullName: yup
    .string()
    .required("Vui lòng nhập họ và tên")
    .matches(/^[a-zA-ZÀ-ỹ\s]+$/, "Họ và tên chỉ được chứa chữ"),
  phoneNumber: yup
    .string()
    .required("Vui lòng nhập số điện thoại")
    .min(10, "Tối thiểu 10 ký tự")
    .max(13, "Tối đa 13 ký tự")
    .matches(PhoneRegex, "Định dạng số điện thoại không đúng"),
  email: yup.string().required("Vui lòng nhập email").email("Email không hợp lệ"),
  bankAccountNumber: yup.string().required("Vui lòng nhập số tài khoản ngân hàng"),
  bankName: yup.string().required("Vui lòng chọn ngân hàng"),
  bankAccountName: yup
    .string()
    .required("Vui lòng nhập tên chủ tài khoản")
    .matches(/^[a-zA-ZÀ-ỹ\s]+$/, "Họ và tên chỉ được chứa chữ"),
  identityCardNumber: yup
    .string()
    .required("Vui lòng nhập số CCCD")
    .matches(/^\d+$/, "Số CCCD chỉ được chứa số"),
  taxCode: yup.string().optional(),
  referrerCode: yup.string().optional(),
});

interface AffiliateRegistration {
  fullName: string;
  phoneNumber: string;
  email: string;
  bankAccountNumber: string;
  bankName: string;
  bankAccountName: string;
  identityCardNumber: string;
  taxCode: string | undefined;
  referrerCode: string | undefined;
}

interface AffiliateRegisterSheetProps {
  open: boolean;
  onClose: () => void;
  onOpen: () => void;
  onPending: () => void;
}

const initData: AffiliateRegistration = {
  fullName: "",
  phoneNumber: "",
  email: "",
  bankAccountNumber: "",
  bankName: "",
  bankAccountName: "",
  identityCardNumber: "",
  taxCode: "",
  referrerCode: "",
};

function convertPhoneToLocal(phone: string): string {
  if (phone.startsWith("+84")) {
    return "0" + phone.slice(3);
  }
  return phone;
}

const AffiliateRegisterSheet = ({
  open,
  onClose,
  onOpen,
  onPending,
}: AffiliateRegisterSheetProps) => {
  const [checked, setChecked] = useState(false);
  const { color, ...appConfig } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const { bank } = useSelector((state: RootState) => state.address);
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { showAlert } = useAlert();
  const { recruitmentPage } = useSelector((state: RootState) => state.affiliation);
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };
  const {
    control,
    handleSubmit,
    register,
    watch,
    reset,
    setValue,
    formState: { errors },
  } = useForm<AffiliateRegistration>({
    defaultValues: initData,
    resolver: yupResolver(affiliateSchema),
  });
  useEffect(() => {
    if (!bank || bank.length == 0) {
      dispatch(getBank());
    }
  }, [bank]);

  const [focusSelect, setFocusSelect] = useState(false);

  const handleSubmitForm = async (data) => {
    try {
      const res = await dispatch(registerAffiliate(data)).unwrap();

      if (res.detail) {
        showToast({
          content: res?.detail,
          type: "error",
        });
        return;
      }

      if (res.data === "Actived") {
        onClose();
        showAlert({
          icon: <img width="50%" src={Icon.affiliateSuccess} />,
          title: "Đăng ký thành công",
          content: "Chào mừng bạn đến với trung tâm quản lý tiếp thị liên kết của chúng tôi",
          buttons: [
            {
              title: "Quản lý",
              customButtonStyle: {
                borderRadius: 99,
                background: color.primary,
                color: COLORS.white,
                width: "50%",
                margin: 8,
              },
              action: () => {
                navigate(Router.collabhistory.index);
              },
            },
            {
              title: "Trang chủ",
              customButtonStyle: {
                borderRadius: 99,
                background: COLORS.white,
                color: color.primary,
                border: `1px solid ${color.primary}`,
                width: "50%",
                margin: 8,
              },
              action: () => {
                navigate(Router.homepage);
              },
            },
          ],
        });
      } else if (res.data === "InActived") {
        onPending();
      }

      dispatch(getUser());
    } catch (error: any) {
      showToast({
        content: "Có lỗi xảy ra. Vui lòng thử lại sau",
        type: "error",
      });
    }
  };

  useEffect(() => {
    if (open) {
      if (user) {
        setValue("fullName", user.fullname || "");
        setValue("phoneNumber", convertPhoneToLocal(user.phoneNumber) || "");
        setValue("email", user.email || "");
      }
    } else {
      reset(initData);
      setChecked(false);
    }
  }, [open, user, setValue, reset]);

  const sheetContent = (
    <form>
      <Stack gap={2} sx={{ padding: "0 16px 16px 16px" }}>
        {user?.referrerCode ? (
          <Typography>Mã người giới thiệu: {user.referrerCode}</Typography>
        ) : (
          <Controller
            name="referrerCode"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Mã giới thiệu"
                variant="outlined"
                sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
                size="small"
              />
            )}
          />
        )}

        <Controller
          name="fullName"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              required
              label="Họ và tên"
              variant="outlined"
              error={!!errors.fullName}
              helperText={errors.fullName?.message}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
              size="small"
            />
          )}
        />
        <Controller
          name="phoneNumber"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              required
              label="Số điện thoại"
              variant="outlined"
              error={!!errors.phoneNumber}
              helperText={errors.phoneNumber?.message}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
              size="small"
            />
          )}
        />
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              required
              label="Email"
              variant="outlined"
              error={!!errors.email}
              helperText={errors.email?.message}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
              size="small"
            />
          )}
        />

        <FormControl variant="outlined" fullWidth>
          <Controller
            name="bankName"
            control={control}
            rules={{ required: "Vui lòng chọn ngân hàng" }}
            render={({ field, fieldState }) => (
              <Autocomplete
                disablePortal
                options={bank}
                getOptionLabel={(option) =>
                  typeof option === "string" ? option : `${option.vn_name} (${option.shortName})`
                }
                isOptionEqualToValue={(option, value) =>
                  typeof value === "string"
                    ? option.shortName === value
                    : option.shortName === value.shortName
                }
                value={bank.find((b) => b.shortName === field.value) ?? null}
                onChange={(_, selectedOption) => {
                  field.onChange(selectedOption?.shortName ?? "");
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    name="bank"
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                    label="Ngân hàng"
                    sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
                    size="small"
                  />
                )}
              />
            )}
          />
        </FormControl>

        <Controller
          name="bankAccountNumber"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              required
              label="Số tài khoản ngân hàng"
              error={!!errors.bankAccountNumber}
              helperText={errors.bankAccountNumber?.message}
              variant="outlined"
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
              size="small"
            />
          )}
        />
        <Controller
          name="bankAccountName"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              required
              error={!!errors.bankAccountName}
              helperText={errors.bankAccountName?.message}
              label="Tên chủ tài khoản ngân hàng"
              variant="outlined"
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
              size="small"
            />
          )}
        />
        <Controller
          name="identityCardNumber"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              required
              label="Số CCCD"
              variant="outlined"
              error={!!errors.identityCardNumber}
              helperText={errors.identityCardNumber?.message}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
              size="small"
            />
          )}
        />

        <Controller
          name="taxCode"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Mã số thuế (nếu có)"
              variant="outlined"
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: 99 } }}
              size="small"
            />
          )}
        />
      </Stack>
    </form>
  );

  const sheetFooter = (
    <Box sx={{ paddingInline: 2, paddingBlock: 1, background: COLORS.white, textAlign: "center" }}>
      <FormControlLabel
        sx={{
          paddingInline: 2,
          marginRight: 0,
          "& .MuiRadio-root": {
            padding: 0,
            marginRight: 1,
          },
          "& .MuiFormControlLabel-label": {
            fontSize: 13,
          },
        }}
        control={<Radio checked={checked} onChange={handleChange} />}
        label={`Tôi đã hiểu và đồng ý với chính sách Affiliate của ${appConfig?.shopName}`}
      />
      <Button
        style={{
          width: "100%",
          padding: 12,
          borderRadius: 99,
          color: COLORS.white,
          marginTop: "11px",
          background: checked ? color.primary : COLORS.neutral6,
        }}
        onClick={handleSubmit(handleSubmitForm)}
        disabled={!checked}
      >
        {recruitmentPage?.navBarContent || "Đăng ký Affiliate"}
      </Button>
    </Box>
  );

  return (
    <BottomSheet
      open={open}
      onClose={onClose}
      onOpen={onOpen}
      sx={{ bgcolor: "white" }}
      height="90vh"
      footer={sheetFooter}
      disableSwipeToOpen
    >
      <>
        <Typography
          align="center"
          sx={{ fontSize: "17px", fontWeight: "700", padding: 1, color: color.primary }}
        >
          Thông tin đăng ký
        </Typography>
        <Typography
          align="center"
          sx={{
            fontSize: "12px",
            paddingInline: 8,
            color: COLORS.neutral3,
            marginBottom: 3,
          }}
        >
          Vui lòng nhập chính xác thông tin, vì đây là cơ sở để chúng tôi ghi nhận và thanh toán hoa
          hồng đầy đủ, đúng hạn cho bạn.
        </Typography>
        {sheetContent}
      </>
    </BottomSheet>
  );
};

export default AffiliateRegisterSheet;
