import React, { useState, useRef } from "react";
import { <PERSON>, Container, Stack, <PERSON><PERSON>ield, Typography } from "@mui/material";
import { COLORS } from "@/constants/themes";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import FrameContainerWeb from "@/components/layout/Layout";

const OtpPage: React.FC = () => {
  const navigate = useNavigate();
  const [otp, setOtp] = useState<string[]>(["", "", "", ""]);
  const inputRefs = useRef<HTMLInputElement[]>([]);

  const handleChange = (index: number, value: string) => {
    if (!/^[0-9]*$/.test(value)) return;

    const updatedOtp = [...otp];
    updatedOtp[index] = value;
    setOtp(updatedOtp);

    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, event: React.KeyboardEvent) => {
    if (event.key === "Backspace" && otp[index] === "") {
      if (index > 0) {
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  const handleResendOtp = () => {
    console.log("Resend OTP clicked");
  };

  const handleSubmit = () => {
    const enteredOtp = otp.join("");
    console.log("Entered OTP:", enteredOtp);
  };

  return (
    <FrameContainerWeb overrideStyle={styles.container}>
      <Stack
        direction={"column"}
        justifyContent={"center"}
        alignItems={"center"}
        mt={-20}
      >
        <Box>
          <Typography fontSize={24} fontWeight={700} color={COLORS.black}>
            Nhập mã OTP
          </Typography>
        </Box>
        <Box mt={2}>
          <Typography textAlign={"center"} color="textSecondary">
            Chúng tôi đã gửi mã qua SMS. Vui lòng nhập mã OTP vừa gửi để xác
            nhận Đăng ký.
          </Typography>
        </Box>
        <Stack direction="row" gap={2} mt={8}>
          {otp.map((digit, index) => (
            <input
              key={index}
              type="text"
              maxLength={1}
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              ref={(el) => (inputRefs.current[index] = el!)}
              style={styles.imputNumber}
            />
          ))}
        </Stack>
        <Box mt={2}>
          <Typography align="center" color="textSecondary" sx={{ mt: 2 }}>
            Bạn chưa nhận được mã?{" "}
            <Typography
              component="span"
              color="primary"
              style={{ cursor: "pointer", textDecoration: "underline" }}
              onClick={() => navigate(`${Router.login}`)}
            >
              Gửi lại
            </Typography>
          </Typography>
        </Box>
      </Stack>
    </FrameContainerWeb>
  );
};

const styles: Record<string, React.CSSProperties> = {
  container: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    minHeight: "100vh",
    padding: "20px",
  },
  imputNumber: {
    width: 60,
    height: 60,
    fontSize: 40,
    fontWeight: 700,
    border: "1px solid #ccc",
    borderRadius: 5,
    outline: "none",
    transition: "border-color 0.3s ease",
    textAlign: "center",
  },
  itemContainer: {
    width: "100%",
    justifyContent: "space-between",
    alignItems: "center",
  },
};

export default OtpPage;
