import { Box } from "@mui/material";
import React, { useEffect } from "react";
import Categories from "../components/menu/Categories";
import ProductTwoColumn from "../components/products/ProductTwoColumn";
import FrameContainer from "../components/layout/Container";
import { COLORS } from "@/constants/themes";
import { useLocation } from "react-router-dom";
import { useCart } from "@/hooks/useCart";

export default function Product() {
  const location = useLocation();
  const { cartPayment, setCartAndSave } = useCart();
  useEffect(() => {
    setCartAndSave({
      ...cartPayment,
      statusDelivery: location.state?.statusDelivery,
    });
  }, [location]);

  return (
    <FrameContainer title={"Danh mục sản phẩm"}>
      <Categories />
      <Box mt={2.5} px={1} borderRadius={"2px"} bgcolor={COLORS.white}>
        <ProductTwoColumn />
      </Box>
    </FrameContainer>
  );
}
