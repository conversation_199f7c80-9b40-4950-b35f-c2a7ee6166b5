import {
  Avatar,
  Box,
  Button,
  ButtonBase,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { COLORS, commonStyle } from "../../../constants/themes";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { formatPrice } from "../../../utils/formatPrice";
import { useNavigate } from "react-router-dom";
import { Router } from "../../../constants/Route";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Clipboard, Eye, EyeSlash } from "@/constants/IconSvg";
import { copy, showToast } from "@/utils/common";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";

export default function ProfileHeader() {
  const { color } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const { affiliateReport } = useSelector((state: RootState) => state.affiliation);
  const navigate = useNavigate();
  const [showCommission, setShowCommission] = useState(false);

  const handleCopy = () => {
    copy(user?.referralCode || "", "mã giới thiệu");
  };

  return (
    <Box sx={{ ...styles.container, background: color.primary }}>
      <Stack direction={"row"} justifyContent={"space-between"} alignItems={"start"}>
        <Stack direction={"row"} alignItems="center" gap={1.5}>
          <Avatar src={user?.avatar} style={{ width: "48px", height: "48px" }} />
          <Stack>
            <Typography sx={{ fontSize: 16, fontWeight: 700, color: COLORS.white }}>
              {user?.fullname}
            </Typography>
            <Stack direction="row" alignItems="center" gap={0.5}>
              <Typography sx={{ fontSize: 14, color: COLORS.white }}>
                ID: {user?.referralCode}
              </Typography>
              <IconButton onClick={handleCopy} sx={{ p: 0.5 }}>
                <Clipboard color={COLORS.white} />
              </IconButton>
            </Stack>
          </Stack>
        </Stack>
        <IconButton
          style={{
            paddingInline: 10,
            paddingBlock: 5,
            background: COLORS.white,
            borderRadius: "50px",
            alignItems: "center",
            display: "flex",
          }}
          onClick={() => navigate(Router.collabhistory.commission.index)}
        >
          <Typography sx={{ fontSize: 10, color: color.primary, fontWeight: 700 }}>
            Lịch sử thu nhập
          </Typography>
          <KeyboardArrowRightIcon style={{ color: color.primary, fontSize: 16 }} />
        </IconButton>
      </Stack>

      <Stack sx={{ mt: 2, color: COLORS.white, alignItems: "center" }}>
        <Stack direction="row" alignItems="center" ml={2}>
          <Typography sx={{ fontSize: 14 }}>Hoa hồng tháng này</Typography>
          <IconButton onClick={() => setShowCommission(!showCommission)}>
            {showCommission ? <Eye color={COLORS.white} /> : <EyeSlash color={COLORS.white} />}
          </IconButton>
        </Stack>
        <Typography
          sx={{
            fontSize: 32,
            fontWeight: 700,
            pb: 1,
            mb: 1,
            lineHeight: 1.2,
          }}
        >
          {showCommission
            ? formatPrice(affiliateReport.thisMonthApprovedCommission || 0)
            : "********"}
        </Typography>
        <Button
          onClick={() => navigate(Router.profile.payment)}
          sx={{
            color: COLORS.white,
            p: 0,
            justifyContent: "flex-start",
            fontSize: 13,
            textTransform: "none",
            textDecoration: "underline",
          }}
        >
          Cập nhật tài khoản nhận hoa hồng
        </Button>
      </Stack>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    padding: "16px",
  },
};
