import React from "react";
import { Dialog, DialogContent, Stack, Typography } from "@mui/material";
import { COLORS, commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { formatPrice } from "@/utils/formatPrice";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

export default function PopupCmsPending({ isOpen, setIsOpen }) {
  const { color } = useConfigApp();
  const { pendingCommission, affiliateReport } = useSelector(
    (state: RootState) => state.affiliation
  );

  const listCommisstion = pendingCommission.map((c) => ({
    userName: c.fullName,
    type: "Hoa hồng mua hàng",
    amount: c.commissionPrice,
  }));
  return (
    <Dialog
      fullWidth
      open={isOpen}
      onClose={() => setIsOpen(false)}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: "12px",
          padding: "20px 20px 25px",
        },
      }}
    >
      <DialogContent
        sx={{
          padding: 0,
        }}
      >
        <Stack
          direction={"row"}
          alignItems={"center"}
          justifyContent={"space-between"}
          gap={0.6}
          style={{
            paddingBottom: "6px",
            borderBottom: `1px solid ${COLORS.neutral12}`,
          }}
        >
          <Typography style={{ ...commonStyle.headline16, color: COLORS.neutral1 }}>
            Hoa hồng chờ duyệt
          </Typography>

          <Typography
            style={{
              ...commonStyle.headline18,
              color: color.primary,
            }}
          >
            {formatPrice(affiliateReport.pendingCommission)}
          </Typography>
        </Stack>
        <Stack style={styles.listContainer}>
          {listCommisstion.map((item, index) => (
            <Stack
              key={index}
              direction={"row"}
              justifyContent={"space-between"}
              alignItems={"end"}
              style={{
                padding: "10px 5px",
              }}
            >
              <Stack>
                <Typography
                  style={{
                    ...commonStyle.headline14,
                    color: color.primary,
                  }}
                >
                  {item.userName}
                </Typography>
                <Typography>{item.type}</Typography>
              </Stack>
              <Stack>
                <Typography
                  style={{
                    color: color.primary,
                    fontSize: "16px",
                    fontWeight: 500,
                  }}
                >
                  +{formatPrice(item.amount)}
                </Typography>
              </Stack>
            </Stack>
          ))}
        </Stack>
      </DialogContent>
    </Dialog>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: COLORS.white,
    borderRadius: 5,
    padding: "17px 15px",
    marginBottom: 20,
  },
  listContainer: {
    height: 400,
    overflowY: "auto",
  },
};
