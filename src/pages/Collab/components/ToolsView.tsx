import { Box, Grid, Stack, Typography } from "@mui/material";
import React from "react";
import { Icon } from "../../../constants/Assets";
import { COLORS, commonStyle } from "../../../constants/themes";
import { useNavigate } from "../../../utils/component-util";
import { Router } from "../../../constants/Route";

type ToolType = {
  icon: string;
  title: string;
  type: "commission" | "members" | "share";
  navPath?: string;
};

const tools: Array<ToolType> = [
  {
    icon: Icon.icon_commission,
    title: "Hoa hồng",
    type: "commission",
    navPath: Router.collabhistory.commission.index,
  },
  {
    icon: Icon.icon_share,
    title: "Link chia sẻ",
    type: "share",
    navPath: Router.refer.index,
  },
  {
    icon: Icon.icon_members,
    title: "Danh sách khách hàng",
    type: "members",
    navPath: Router.collabhistory.members.index,
  },
];

export default function ToolsView() {
  const navigator = useNavigate();

  return (
    <Box
      style={{ ...commonStyle.shadowBorder, ...styles.container }}
      sx={{ width: "100%" }}
    >
      <Typography style={styles.title}>Công cụ</Typography>
      <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 3 }}>
        {tools.map((item) => {
          return (
            <Grid
              item
              xs={4}
              key={item.type}
              onClick={() => {
                navigator(item.navPath!);
              }}
            >
              <Stack alignItems="center" justifyContent="center">
                <img src={item.icon} width="38px" height="38px" alt="" />
                <Typography textAlign="center" style={styles.smallTitle}>
                  {item.title}
                </Typography>
              </Stack>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    borderRadius: 5,
    padding: 15,
    marginBottom: 16,
  },
  smallTitle: {
    fontSize: 12,
    fontWeight: 700,
    color: COLORS.primary1,
  },
  title: {
    fontWeight: 700,
    color: COLORS.primary1,
    paddingBottom: 8,
  },
};
