import { Box, Button, Grid, Stack, Typography } from "@mui/material";
import dayjs from "dayjs";
import React from "react";
import { LevelName } from "../../../constants/Const";
import { Avatar } from "zmp-ui";
import { formatPrice } from "../../../utils/formatPrice";
import { Icon } from "../../../constants/Assets";
import { COLORS, commonStyle } from "../../../constants/themes";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";
import { useConfigApp } from "@/hooks/useConfigApp";

type ItemType = {
  icon: string;
  title: string;
  value: number;
  isPrice?: boolean;
};

export default function MemberItem({ item }) {
  const { color } = useConfigApp();
  const navigate = useNavigate();
  const infoItems: Array<ItemType> = [
    {
      icon: Icon.receipt_item,
      title: "Tổng chi tiêu",
      value: item?.totalPurchase ?? 0,
      isPrice: true,
    },
  ];

  return (
    <Button
      sx={{
        width: "100%",
        display: "block",
        textAlign: "left",
      }}
      // onClick={() => {
      //   navigate(`/collabhistory/members/${item.id}`);
      // }}
    >
      <Stack style={styles.container}>
        <Stack direction={"row"} alignItems="center" gap={1}>
          <Box>
            <Avatar src={item.avatar} size={45} />
          </Box>
          <Stack
            direction={"row"}
            justifyContent={"space-between"}
            width="100%"
            sx={{
              borderBottom: `1px solid #E8E8E8`,
              paddingBottom: "8px",
            }}
          >
            <Stack>
              <Box width="100%">
                <Typography style={styles.usernameText}>{item.fullName ?? "N/A"}</Typography>
                <Typography style={{ ...styles.smallTitle }}>
                  Ngày tham gia: {dayjs(item?.created).format("DD/MM/YYYY")}
                </Typography>
              </Box>
            </Stack>
            <Stack>
              <Typography style={{ ...styles.rightText }}>
                Hệ cấp:{" "}
                <span
                  style={{
                    ...styles.levelText,
                    color: color.primary,
                  }}
                >
                  F{item?.fLevel}
                </span>
              </Typography>
            </Stack>
          </Stack>
        </Stack>
        {infoItems.map((info) => (
          <Stack
            key={info.title}
            direction={"row"}
            alignItems="center"
            gap={1}
            padding={"11px 0 0"}
          >
            <Stack width={"58px"} alignItems={"center"} justifyContent={"center"}>
              <img src={info.icon} width={20} height={20} style={{ objectFit: "contain" }} />
            </Stack>

            <Stack direction={"row"} justifyContent={"space-between"} width="100%">
              <Typography style={styles.infoTitleText}>{info?.title}</Typography>
              <Typography style={styles?.valueText}>
                {info?.isPrice ? formatPrice(info?.value) : info?.value}
              </Typography>
            </Stack>
          </Stack>
        ))}
      </Stack>
    </Button>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: COLORS.white,
    borderRadius: 5,
    padding: "17px 15px",
  },
  smallTitle: {
    fontSize: 12,
    color: "#ACACAC",
  },
  rightText: {
    color: COLORS.neutral4,
    textAlign: "right",
    paddingBottom: 12,
  },
  levelText: {
    color: COLORS.primary2,
    fontWeight: 700,
  },
  usernameText: {
    ...commonStyle.headline16,
    color: COLORS.neutral1,
  },
  valueText: {
    color: COLORS.neutral1,
    fontWeight: 700,
  },
  infoTitleText: {
    color: COLORS.neutral2,
  },
};
