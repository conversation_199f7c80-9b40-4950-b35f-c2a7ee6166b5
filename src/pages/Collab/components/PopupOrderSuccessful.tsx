import React, { useEffect } from "react";
import { CircularProgress, Dialog, DialogContent, Stack, Typography } from "@mui/material";
import { COLORS, commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { formatPrice } from "@/utils/formatPrice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import dayjs from "dayjs";
import InfiniteScroll from "@/components/UI/InfiniteScroll";
import { getSuccessOrderAffiliate } from "@/redux/slices/affiliation/affiliationSlice";

export default function PopupOrderSuccessful({ isOpen, setIsOpen }) {
  const { color } = useConfigApp();
  const { successOrders, affiliateReport } = useSelector((state: RootState) => state.affiliation);
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (user) {
      dispatch(getSuccessOrderAffiliate());
    }
  }, [user]);

  const hasMore =
    successOrders.pagination.skip + successOrders.pagination.limit < successOrders.pagination.total;
  const fetchMoreProducts = () => {
    dispatch((_, getState) => {
      const state = getState().affiliation;
      if (!state) return;
      dispatch(
        getSuccessOrderAffiliate({
          skip: state.successOrders.pagination.skip + state.successOrders.pagination.limit,
          limit: state.successOrders.pagination.limit,
        })
      );
    });
  };

  return (
    <Dialog
      fullWidth
      open={isOpen}
      onClose={() => setIsOpen(false)}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: "12px",
          padding: "20px 20px 25px",
        },
      }}
    >
      <DialogContent
        sx={{
          padding: 0,
        }}
      >
        <Stack
          direction={"row"}
          alignItems={"center"}
          justifyContent={"space-between"}
          gap={0.6}
          style={{
            paddingBottom: "6px",
            borderBottom: `1px solid ${COLORS.neutral12}`,
          }}
        >
          <Typography style={{ ...commonStyle.headline16, color: COLORS.neutral1 }}>
            Đơn hàng thành công
          </Typography>

          {/* <Typography
            style={{
              ...commonStyle.headline18,
              color: color.primary,
            }}
          >
            {formatPrice(5450000)}
          </Typography> */}
        </Stack>
        <Stack style={styles.listContainer}>
          <InfiniteScroll
            loader={
              <Stack justifyContent={"center"} alignItems={"center"} padding={4}>
                <CircularProgress />
              </Stack>
            }
            className="osfi"
            fetchMore={fetchMoreProducts}
            hasMore={hasMore}
            endMessage={null}
          >
            {successOrders.list.map((item, index) => (
              <Stack
                key={index}
                direction={"row"}
                justifyContent={"space-between"}
                alignItems={"end"}
                style={{
                  padding: "10px 5px",
                }}
              >
                <Stack>
                  <Typography
                    style={{
                      ...commonStyle.headline14,
                      color: color.primary,
                    }}
                  >
                    {item.creator.fullName}
                  </Typography>
                  <Typography>{dayjs(item?.created).format("DD/MM/YYYY")}</Typography>
                </Stack>
                <Stack>
                  <Typography
                    style={{
                      color: color.primary,
                      fontSize: "16px",
                      fontWeight: 500,
                    }}
                  >
                    {formatPrice(item.price)}
                  </Typography>
                </Stack>
              </Stack>
            ))}
          </InfiniteScroll>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: COLORS.white,
    borderRadius: 5,
    padding: "17px 15px",
    marginBottom: 20,
  },
  listContainer: {
    height: 400,
    overflowY: "auto",
  },
};
