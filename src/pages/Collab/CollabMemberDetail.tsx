import React, { useEffect, useState } from "react";
import FrameContainer from "../../components/layout/Container";
import { Box, Stack, Typography } from "@mui/material";
import { COLORS, commonStyle } from "@/constants/themes";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { Icon } from "@/constants/Assets";
import { Avatar } from "zmp-ui";
import dayjs from "dayjs";
import { formatPrice } from "@/utils/formatPrice";
import { useConfigApp } from "@/hooks/useConfigApp";

type ItemType = {
  icon: string;
  title: string;
  value: number;
  isPrice?: boolean;
};

export default function CollabMemberDetail() {
  const { id } = useParams();
  const { color } = useConfigApp();
  const { list } = useSelector((state: RootState) => state.team);
  const [curMember, setCurMember] = useState<any>(null);
  const historyCommisstion = [
    {
      id: 1,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 2,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 3,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 4,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 5,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 6,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 7,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 8,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 9,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
    {
      id: 10,
      content: "+ 20.000đ giao dịch mua hàng ngày 24/11/2024 đơn hàng 100.000đ",
    },
  ];
  const infoItems: Array<ItemType> = [
    {
      icon: Icon.receipt_item,
      title: "Tổng chi tiêu",
      value: curMember?.commissionInfo?.totalCommission ?? 0,
      isPrice: true,
    },
  ];
  useEffect(() => {
    setCurMember(list.find((item) => String(item.id) === String(id)));
  }, [list, id]);
  return (
    <FrameContainer title="Chi tiết hoa hồng thành viên">
      <Stack sx={styles.contentContainer}>
        <Stack style={styles.container}>
          <Stack direction={"row"} alignItems="center" gap={1}>
            <Box>
              <Avatar src={curMember?.avatarUrl} size={45} />
            </Box>
            <Stack
              direction={"row"}
              justifyContent={"space-between"}
              width="100%"
              sx={{
                borderBottom: `1px solid #E8E8E8`,
                paddingBottom: "8px",
              }}
            >
              <Stack>
                <Box width="100%">
                  <Typography style={styles.usernameText}>
                    {curMember?.name ?? "N/A"}
                  </Typography>
                  <Typography style={{ ...styles.smallTitle }}>
                    Ngày tham gia:{" "}
                    {dayjs(curMember?.createdAt).format("DD/MM/YYYY")}
                  </Typography>
                </Box>
              </Stack>
              <Stack>
                <Typography style={{ ...styles.rightText }}>
                  Hệ cấp:{" "}
                  <span
                    style={{
                      ...styles.levelText,
                      color: color.primary,
                    }}
                  >
                    F{curMember?.fLevel}
                  </span>
                </Typography>
              </Stack>
            </Stack>
          </Stack>
          {infoItems.map((info) => (
            <Stack
              key={info.title}
              direction={"row"}
              alignItems="center"
              gap={1}
              padding={"11px 0 0"}
            >
              <Stack
                width={"58px"}
                alignItems={"center"}
                justifyContent={"center"}
              >
                <img
                  src={info.icon}
                  width={20}
                  height={20}
                  style={{ objectFit: "contain" }}
                />
              </Stack>

              <Stack
                direction={"row"}
                justifyContent={"space-between"}
                width="100%"
              >
                <Typography style={styles.infoTitleText}>
                  {info.title}
                </Typography>
                <Typography style={styles.valueText}>
                  {info.isPrice ? formatPrice(info.value) : info.value}
                </Typography>
              </Stack>
            </Stack>
          ))}
        </Stack>

        <Stack style={styles.listContainer}>
          {historyCommisstion.map((item, index) => (
            <Stack
              key={item.id}
              padding={"8px 0"}
              color={COLORS.neutral2}
              fontSize={14}
              letterSpacing={0.14}
              lineHeight={"23px"}
            >
              {item.content}
            </Stack>
          ))}
        </Stack>
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
    background: COLORS.bgColor.secondary,
  },
  container: {
    background: COLORS.white,
    borderRadius: 5,
    padding: "17px 15px",
    boxShadow: `0px 0px 4px 0px rgba(0, 0, 0, 0.25)`,
    zIndex: 2,
  },
  listContainer: {
    background: COLORS.neutral10,
    borderRadius: 5,
    padding: "25px 15px 15px",
    marginTop: -10,
    marginBottom: 20,
    boxShadow: `0px 0px 4px 0px rgba(0, 0, 0, 0.25)`,
    height: "calc(100vh - 270px)", //60px + 120p + 70px
    overflowY: "auto",
  },
  rightText: {
    color: COLORS.neutral4,
    textAlign: "right",
    paddingBottom: 12,
  },
  levelText: {
    color: COLORS.primary2,
    fontWeight: 700,
  },
  usernameText: {
    ...commonStyle.headline16,
    color: COLORS.neutral1,
  },
  valueText: {
    color: COLORS.neutral1,
    fontWeight: 700,
  },
  infoTitleText: {
    color: COLORS.neutral2,
  },
};
