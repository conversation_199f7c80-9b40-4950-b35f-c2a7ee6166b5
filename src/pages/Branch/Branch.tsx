import React, { useState } from "react";
import dayjs from "dayjs";
import isToday from "dayjs/plugin/isToday";
import BranchList from "../../components/branch/BranchList";
import FrameContainer from "../../components/layout/Container";
import { Search } from "../Search";
import { COLORS } from "../../constants/themes";
import { Button, Stack } from "@mui/material";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useLocation } from "react-router-dom";
import { useNavigate } from "@/utils/component-util";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";

dayjs.extend(isToday);

export default function Branch() {
  const [keyword, setKeyword] = useState("");
  const [select, setSelect] = useState(0);
  const { color } = useConfigApp();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  return (
    <FrameContainer title={"Danh sách cửa hàng"}>
      <Stack sx={styles.contentContainer}>
        {/* <Stack
          direction={"row"}
          justifyContent={"space-between"}
          gap={1.5}
          style={{
            marginBottom: "20px",
          }}
        > */}
        {/* <Stack
            style={{
              width: "100%",
            }}
          >
            <Search
              keyword={keyword}
              setKeyword={setKeyword}
              startIcon={true}
              textPlaceholder={""}
              size={"small"}
            />
          </Stack> */}
        {/* <Stack
            style={{
              minWidth: "120px",
            }}
          >
            <Button
              style={{
                ...styles.container,
                background: color.primary,
                color: color.accent,
              }}
            >
              Thành phố
            </Button>
          </Stack> */}
        {/* </Stack> */}
        <BranchList
          search={keyword}
          shouldGoBack={location.state?.shouldGoBack || false}
          select={select}
          setSelect={setSelect}
        />
      </Stack>
    </FrameContainer>
  );
}
const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1.25,
    marginBottom: 4,
  },
  container: {
    fontSize: "15px",
    fontWeight: 500,
    color: COLORS.white,
    padding: "12px 20px",
    borderRadius: "8px",
  },
};
