import React, { useEffect, useState } from "react";
import FrameContainerFull from "../components/layout/ContainerFluid";
import { useDispatch, useSelector } from "react-redux";
import store, { AppDispatch, RootState } from "../redux/store";
import { Box, Divider, Stack, Typography } from "@mui/material";
import { LevelIcon, LevelName, LevelPolicy } from "../constants/Const";
import { commonStyle } from "../constants/themes";
import { getUser } from "../redux/slices/authen/authSlice";
import { getReport } from "../redux/slices/team/team";
import MembershipSlider from "./Profile/components/MembershipSliderFnB";

export default function Point() {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const [indexSelected, setIndex] = useState(0);
  const numLevel = Object.values(LevelIcon).length;

  useEffect(() => {
    setIndex(user?.level || 0);
    store.dispatch(getUser());
    store.dispatch(getReport());
  }, []);

  return (
    <FrameContainerFull title="Thẻ tích điểm" overrideStyle={styles.container}>
      <MembershipSlider />
      <Stack style={styles.sectionContainer}>
        <Typography style={styles.titleStyle}>Chi tiết chương trình</Typography>
        <Divider />
        <Box style={styles.contentContainer}>
          <Typography style={styles.textStyle}>Tích lũy 1% điểm trên giá trị đơn hàng</Typography>
          <Typography style={styles.textStyle}>
            Đổi điểm thành ưu đãi giảm giá khi mua hàng
          </Typography>
          <Typography style={styles.textStyle}>Voucher tặng theo chương trình</Typography>
        </Box>
      </Stack>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: "transparent",
    marginBlock: 12,
  },
  sectionContainer: {
    borderRadius: "5px",
    background: "#fff",
    marginBlock: 12,
    marginInline: 12,
    padding: 12,
  },
  titleStyle: {
    color: "#1D1D1D",
    fontSize: 16,
    fontWeight: 700,
    marginBottom: 5,
  },
  textStyle: {
    color: "#424242",
    fontSize: 14,
    fontWeight: 400,
    marginBlock: 4,
  },
  contentContainer: {
    marginTop: 10,
  },
};
