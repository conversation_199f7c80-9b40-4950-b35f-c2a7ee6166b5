import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import FrameContainerFull from "../components/layout/ContainerFluid";
import ProductTwoColumn from "@/components/products/ProductTwoColumn";
import { getProductListByCategory, setSearchCondition, resetSortBy } from "../redux/slices/product/productListSlice";
import type { AppDispatch, RootState } from "../redux/store";
import type { IProductCategory } from "@/types/product";

export default function ProductCategoryPage() {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation() as { state?: { categoryId?: string; categoryName?: string } };
  const { shopId, shopInfo } = useSelector((state: RootState) => state.appInfo);
  const { searchCondition } = useSelector((state: RootState) => state.productList);
  const { productCategoryList } = useSelector((state: RootState) => state.product);

  const categoryId = location.state?.categoryId;
  let categoryName = location.state?.categoryName;
  if (!categoryName && categoryId && productCategoryList?.length) {
    const found = productCategoryList.find((cat: IProductCategory) => cat.categoryId === categoryId);
    categoryName = found?.categoryName || "Danh mục sản phẩm";
  }

  useEffect(() => {
    if (shopId && categoryId) {
      dispatch(setSearchCondition({ ...searchCondition, categoryId, parentId: undefined }));
      dispatch(getProductListByCategory({ ...searchCondition, categoryId, parentId: undefined, shopId }));
    }
    return () => {
      dispatch(resetSortBy());
    };
  }, [shopId, categoryId]);

  return (
    <FrameContainerFull title={categoryName || "Danh mục sản phẩm"} overrideStyle={{ height: "100vh" }}>
      <div style={{ width: "100%", height: "calc(100vh - 120px - var(--zaui-safe-area-inset-bottom, 0px) - var(--zaui-safe-area-inset-top, 0px))", background: "white", overflow: "auto", paddingBottom: 50 }}>
        {<ProductTwoColumn />}
      </div>
    </FrameContainerFull>
  );
}
