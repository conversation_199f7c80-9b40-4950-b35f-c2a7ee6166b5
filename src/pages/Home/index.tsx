import ActiveUser from "@/components/common/ActiveUser";
import BtnAction from "@/components/common/BtnAction";
import FollowOA from "@/components/common/FollowOA";
import PointBox from "@/components/common/PointBox";
import Banner from "@/components/home/<USER>";
import BannerV2 from "@/components/home/<USER>/BannerRetail";
import BranchNow from "@/components/home/<USER>";
import HeaderBar from "@/components/home/<USER>";
import HtmlRender from "@/components/home/<USER>";
import ListItem from "@/components/home/<USER>";
import MenuAction from "@/components/home/<USER>";
import Policy from "@/components/home/<USER>";
import ProductListGridSlice from "@/components/home/<USER>";
import ProductListGridSliceV2 from "@/components/home/<USER>/ProductListGridSliceRetail";
import ShopInfo from "@/components/home/<USER>";
import ListSlider from "@/components/home/<USER>";
import VoucherHome from "@/components/home/<USER>";
import ArrowRightIcon from "@/components/icon/ArrowRightIcon";
import LayoutHomePage from "@/components/layout/LayoutHomepage";
import MenuCategories from "@/components/menucategory";
import NewsItem from "@/components/news/NewsItem";
import NewsItemHorizontalV2 from "@/components/news/newsitemhorizontal/NewsItemHorizontalRetail";
import NoDataView from "@/components/UI/NoDataView";
import PopupAdvertise from "@/components/UI/PopupAdvertise";
import { Platform } from "@/config";
import { HomeTabActions } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { COLORS } from "@/constants/themes";
import { useCart } from "@/hooks/useCart";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import LoginPopup from "@/components/LoginPopup";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getAdvertisements } from "@/redux/slices/advertise/advertisementSlice";
import { ObjectHome } from "@/redux/slices/appInfo/appInfoSlice";
import {
  getListNewsUser,
  getNewsListByParams,
  fetchListArticleMap,
} from "@/redux/slices/news/newsListSlice";
import {
  getProductListByCategory,
  setSearchCondition,
} from "@/redux/slices/product/productListSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { keyComponent } from "@/types/home";
import { MenuType } from "@/types/menu";
import { INews } from "@/types/news";
import { useNavigate } from "@/utils/component-util";
import { Box, Container, Grid, Skeleton } from "@mui/material";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { configAppView } from "zmp-sdk/apis";
import GameBox from "@/components/common/GameBox";

configAppView({
  headerColor: "#1843EF",
  headerTextColor: "white",
  hideAndroidBottomNavigationBar: true,
  hideIOSSafeAreaBottom: true,
  actionBar: {
    hide: true,
    leftButton: "none",
  },
  statusBarType: "transparent",
});

const HomePage: React.FunctionComponent = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { home } = useConfigApp();
  const { container } = useConfigApp();
  const appConfig = useConfigApp();

  const { user } = useSelector((state: RootState) => state.auth);
  const { productCategoryInHome } = useSelector((state: RootState) => state.product);
  const { list } = useSelector((state: RootState) => state.branch);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { advertisements, isLoading } = useSelector((state: RootState) => state.advertisement);
  const { listArticleMap, isLoadingMap } = useSelector((state: RootState) => state.newsList);
  const { cartPayment, setCartAndSave } = useCart();
  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();
  const { defaultBranch } = useSelector((state: RootState) => state.branch);
  const { thumbnailLink } = useSelector((state: RootState) => state.gamification);

  useEffect(() => {
    if (!home || !shopId) return;
    home.forEach((item, index) => {
      if (item.type === keyComponent.ListNews2) {
        const cateId = item.articleCategoryId?.trim();
        const key = cateId || `default_${index}`;
        dispatch(fetchListArticleMap({ shopId, articleCategoryId: cateId, key }));
      }
    });
  }, [home, shopId, dispatch]);

  // useEffect(() => {
  //   getProductData();
  // }, [productCategoryInHome]);

  useEffect(() => {
    if (!shopId) return;
    dispatch(getAdvertisements(shopId));
    dispatch(fetchListArticleMap({ shopId, key: "default_news" }));
  }, [shopId, dispatch]);

  const containerStyleItem = container as any;
  const containerBgColor = containerStyleItem?.backgroundColor || "";
  const containerBgImage = containerStyleItem?.backgroundImage || "";

  const containerStyle = {
    paddingInline: 0,
    backgroundColor: containerBgImage ? undefined : containerBgColor,
    backgroundImage: containerBgImage ? `url(${containerBgImage})` : undefined,
    backgroundSize: containerBgImage ? "cover" : undefined,
    backgroundRepeat: containerBgImage ? "no-repeat" : undefined,
    backgroundPosition: containerBgImage ? "center" : undefined,
  };

  const getProductData = async () => {
    if (productCategoryInHome.length > 0) {
      await dispatch(
        getProductListByCategory({
          categoryId: productCategoryInHome[0].categoryId,
        })
      );
    }
  };

  const renderNewsSkeletonLoading = (isSlice = false) => {
    const imageHeight = 110;
    const borderRadius = 1;
    const titleHeight = 20;
    const dateHeight = 14;
    if (isSlice) {
      return (
        <Box sx={{ width: "100%", px: 0 }}>
          <Box
            sx={{
              display: "flex",
              gap: 1.5,
              overflow: "hidden",
            }}
          >
            {[...Array(1)].map((_, index) => (
              <Box
                key={index}
                sx={{
                  minWidth: "100%",
                  flexShrink: 0,
                  borderRadius: borderRadius,
                  p: 0.5,
                }}
              >
                <Skeleton
                  variant="rectangular"
                  height={imageHeight}
                  sx={{ borderRadius: borderRadius }}
                />
                <Skeleton variant="text" width="80%" height={titleHeight} sx={{ mt: 1 }} />
                <Skeleton
                  variant="text"
                  width="50%"
                  height={dateHeight}
                  sx={{ mt: 0.5, bgcolor: "grey.200" }}
                />
              </Box>
            ))}
          </Box>
        </Box>
      );
    }

    return (
      <Grid container spacing={2} sx={{ width: "100%", margin: 0 }}>
        {[...Array(2)].map((_, index) => (
          <Grid item xs={6} key={index}>
            <Box sx={{ mb: 1.5, borderRadius: borderRadius, p: 0.5 }}>
              <Skeleton
                variant="rectangular"
                height={imageHeight}
                sx={{ borderRadius: borderRadius }}
              />
              <Skeleton variant="text" width="80%" height={titleHeight} sx={{ mt: 1 }} />
              <Skeleton
                variant="text"
                width="50%"
                height={dateHeight}
                sx={{ mt: 0.5, bgcolor: "grey.200" }}
              />
            </Box>
          </Grid>
        ))}
      </Grid>
    );
  };

  // useEffect(() => {
  //   const calculateCountdown = () => {
  //     const now = dayjs();
  //     const end = dayjs(endTime);
  //     const diff = end.diff(now);

  //     if (diff <= 0) {
  //       setCountdown("Flash Sale đã kết thúc!");
  //       return null;
  //     }

  //     const hours = Math.floor(diff / (1000 * 60 * 60));
  //     const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  //     const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  //     return `${hours}:${minutes}:${seconds} `;
  //   };

  //   const updateCountdown = () => {
  //     const timeLeft = calculateCountdown();
  //     if (timeLeft !== null) {
  //       setCountdown(timeLeft);
  //     }
  //   };

  //   updateCountdown();
  //   const timer = setInterval(updateCountdown, 1000);

  //   return () => clearInterval(timer);
  // }, [endTime]);

  // useEffect(() => {
  // if (user) {
  //   setCartAndSave({
  //     ...cartPayment,
  //     branchId: defaultBranch?.branchId,
  //     transportService: TransportService.LCOD,
  //   });
  // }
  // }, [user]);

  const onNavigateToProduction = async () => {
    // if (cateKey) {
    //   const categoryIds = cateKey.split("&").map((id) => id);
    //   dispatch(
    //     setSearchCondition({
    //       categoryId: categoryIds[0],
    //     }),
    //   );
    //   dispatch(getProductListByCategory({ categoryId: categoryIds[0] }));
    // }
    // navigate(Router.menu);
  };

  const onClickItemCategory = async (cateKey) => {
    if (cateKey) {
      dispatch(
        setSearchCondition({
          categoryId: cateKey.id,
        })
      );
    }
    navigate(Router.menu);
  };

  const onClickSeeAllNewProduct = async () => {
    dispatch(
      setSearchCondition({
        categoryId: null,
      })
    );

    navigate(Router.menu);
  };

  const onNavigateToPost = (title?: string) => {
    navigate("/posts", { state: { title: title ?? "Tin tức" } });
  };

  const renderComponent = (item: ObjectHome, index: number) => {
    if (!item.show) return;

    let com;
    switch (item.type) {
      case keyComponent.Banner2:
        com = (
          <BannerV2
            key={index}
            item={item}
            styleBanner={{
              ...(item.style?.aspectRatio && { aspectRatio: item.style.aspectRatio }),
              // thêm thuộc tính khác nếu cần
            }}
            slickSliderOptions={{ dots: true, dotsClass: "dots-banner-with-branch" }}
          />
        );
        break;

      case keyComponent.BannerWithBranch:
        com = (
          <Box key={index} pt={1}>
            <Banner item={item} />
          </Box>
        );
        break;
      case keyComponent.BranchLocation:
        com = (
          <Box
            mt={-5}
            mx={2}
            px={2}
            borderRadius={2}
            bgcolor={COLORS.white}
            position={"relative"}
            zIndex={998}
            key={index}
            onClick={() => navigate(Router.branch.index)}
          >
            <BranchNow branch={list[0]} />
          </Box>
        );
        break;
      case keyComponent.MenuAction:
        com = (
          <Box key={index} mt={2} mx={2} borderRadius={5}>
            <MenuAction homeTabs={HomeTabActions} />
          </Box>
        );
        break;
      case keyComponent.MenuCategory:
        com = (
          <MenuCategories
            key={index}
            title={item.style?.title}
            listMenu={productCategoryInHome.map((item) => ({
              id: item.categoryId,
              title: item.categoryName,
              icon: item.categoryIcon,
              image: item.image,
              type: MenuType.LINK,
            }))}
            item={item}
            onClickMenu={onClickItemCategory}
            onClickSubBtn={onNavigateToProduction}
            itemOption={2}
          />
        );
        break;
      case keyComponent.MenuCategory2:
        com = (
          <MenuCategories
            key={index}
            title={item.style?.title}
            listMenu={productCategoryInHome.map((item) => ({
              id: item.categoryId,
              title: item.categoryName,
              icon: item.categoryIcon,
              image: item.image,
              type: MenuType.LINK,
            }))}
            item={item}
            onClickMenu={onClickItemCategory}
            onClickSubBtn={onNavigateToProduction}
            itemOption={1}
          />
        );
        break;
      case keyComponent.BannerHome:
        com = (
          <Box key={index} mt={1}>
            <Banner item={item} />
          </Box>
        );
        break;
      case keyComponent.ActiveAccount:
        com = !user && (
          <Box key={index} px={2} pt={1}>
            <ActiveUser item={item as { description?: string }} />
          </Box>
        );
        break;
      case keyComponent.FollowOA:
        com = !user?.isZaloOA && Platform === "zalo" && (
          <Box key={index} px={2} pt={1} borderRadius={5}>
            <FollowOA />
          </Box>
        );
        break;
      case keyComponent.Policy:
        com = <Policy style={item.style} />;
        break;
      case keyComponent.ShopInfo:
        com = <ShopInfo style={item.style} />;
        break;
      case keyComponent.Html:
        com = <HtmlRender htmlString={item.data} />;
        break;
      case keyComponent.Game1:
        com = (
          <>
            {thumbnailLink && (
              <img
                src={thumbnailLink}
                alt="Game 1"
                style={{
                  width: "100%",
                  height: "auto",
                  display: "block",
                  cursor: "pointer",
                  marginTop: 2,
                }}
                onClick={() => checkLogin(() => navigate(Router.game))}
              />
            )}
          </>
        );
        break;
      case keyComponent.Game1:
        com = (
          <>
            {thumbnailLink && (
              <img
                src={thumbnailLink}
                alt="Game 3"
                style={{
                  width: "100%",
                  height: "auto",
                  display: "block",
                  cursor: "pointer",
                  marginTop: 2,
                }}
                onClick={() => checkLogin(() => navigate(Router.game))}
              />
            )}
          </>
        );
        break;
      case keyComponent.ProductList:
        if (item) {
          com = (
            <Box key={index} px={2} py={1}>
              <ProductListGridSlice
                title={item?.style?.title}
                item={item}
                onNavigateToProduction={onClickSeeAllNewProduct}
              />
            </Box>
          );
        }
        break;
      case keyComponent.ProductList2:
        if (item) {
          com = (
            <Box key={index} px={2} pt={2}>
              <ProductListGridSliceV2
                title={item?.style?.title}
                item={item}
                onNavigateToProduction={onClickSeeAllNewProduct}
              />
            </Box>
          );
        }
        break;
      case keyComponent.ListNews:
        if (item.style?.category == "grid") {
          const key = "default_news";
          com = (
            <Box key={index} px={2} pb={2} bgcolor={"#ffffff00"}>
              <ListItem
                title={item?.style?.title || ""}
                seeAll={() => onNavigateToPost(item?.style?.title)}
                btnAction={
                  <BtnAction
                    text={item?.style?.moreText || "Xem tất cả"}
                    styleOverride={{ color: appConfig?.color?.primary, fontSize: 15 }}
                    icon={<ArrowRightIcon fillColor={appConfig?.color?.primary} />}
                    eventAction={() => {
                      onNavigateToPost(item?.style?.title);
                    }}
                  />
                }
                titleColor={appConfig?.color?.primary}
              >
                {isLoadingMap[key] ? (
                  renderNewsSkeletonLoading()
                ) : Array.isArray(listArticleMap[key]) && listArticleMap[key].length > 0 ? (
                  listArticleMap[key].map((news: INews, idx) => (
                    <NewsItem news={news} key={String(idx)} titleFromParent={item?.style?.title} />
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListItem>
            </Box>
          );
        } else if (item.style?.category == "slice") {
          const key = "default_news";
          com = (
            <Box key={index} my={1} mx={2}>
              <ListSlider
                title={item?.style?.title}
                seeAll={() => onNavigateToPost(item?.style?.title)}
                btnAction={
                  <BtnAction
                    text={item?.style?.moreText || "Xem tất cả"}
                    styleOverride={{ color: appConfig?.color?.primary, fontSize: 15 }}
                    icon={<ArrowRightIcon fillColor={appConfig?.color?.primary} />}
                    eventAction={() => {
                      onNavigateToPost(item?.style?.title);
                    }}
                  />
                }
                sliceConfig={{
                  dots: false,
                  infinite: false,
                  slidesToShow: 1.4,
                  autoplay: false,
                  arrows: false,
                }}
              >
                {isLoadingMap[key] ? (
                  renderNewsSkeletonLoading(true)
                ) : Array.isArray(listArticleMap[key]) && listArticleMap[key].length > 0 ? (
                  listArticleMap[key].map((news: INews, idx: number) => (
                    <Box key={idx} sx={{ paddingRight: "16px" }}>
                      <NewsItemHorizontalV2
                        news={news}
                        titleFromParent={item?.style?.title}
                        containerStyles={{
                          backgroundColor: COLORS.white,
                        }}
                      />
                    </Box>
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListSlider>
            </Box>
          );
        }
        break;
      case keyComponent.ListNews2: {
        const cateId = item.articleCategoryId?.trim();
        const key = cateId || `default_${index}`;
        if (item.style?.category == "grid") {
          com = (
            <Box key={index} px={2} py={1}>
              <ListItem
                title={item?.style?.title || ""}
                seeAll={() => {
                  onNavigateToPost(item?.style?.title);
                }}
                btnAction={
                  <BtnAction
                    text={item?.style?.moreText || "Xem tất cả"}
                    styleOverride={{ color: appConfig?.color?.primary, fontSize: 15 }}
                    icon={<ArrowRightIcon fillColor={appConfig?.color?.primary} />}
                    eventAction={() => {
                      onNavigateToPost(item?.style?.title);
                    }}
                  />
                }
              >
                {isLoadingMap[key] ? (
                  renderNewsSkeletonLoading()
                ) : Array.isArray(listArticleMap[key]) && listArticleMap[key].length > 0 ? (
                  listArticleMap[key].map((news: INews, idx) => (
                    <NewsItem
                      news={news}
                      key={news.articleId || idx}
                      titleFromParent={item?.style?.title}
                    />
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListItem>
            </Box>
          );
        } else if (item.style?.category == "slice") {
          com = (
            <Box key={index} py={1} px={2}>
              <ListSlider
                className={"list-news-slider"}
                title={item?.style?.title}
                titleStyle={{ color: appConfig?.color?.primary }}
                seeAll={() => {
                  onNavigateToPost(item?.style?.title);
                }}
                btnAction={
                  <BtnAction
                    text={item?.style?.moreText || "Xem tất cả"}
                    styleOverride={{ color: appConfig?.color?.primary, fontSize: 15 }}
                    icon={<ArrowRightIcon fillColor={appConfig?.color?.primary} />}
                    eventAction={() => {
                      onNavigateToPost(item?.style?.title);
                    }}
                  />
                }
                sliceConfig={{
                  dots: false,
                  infinite: false,
                  slidesToShow: 1.5,
                  autoplay: false,
                  arrows: false,
                }}
              >
                {isLoadingMap[key] ? (
                  renderNewsSkeletonLoading(true)
                ) : Array.isArray(listArticleMap[key]) && listArticleMap[key].length > 0 ? (
                  listArticleMap[key].map((news: INews, idx: number) => (
                    <NewsItemHorizontalV2
                      key={idx}
                      news={news}
                      titleFromParent={item?.style?.title}
                      containerStyles={{
                        backgroundColor: COLORS.white,
                      }}
                    />
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListSlider>
            </Box>
          );
        } else if (item.style?.category == "slice2") {
          com = (
            <Box key={index} px={2}>
              <ListSlider
                className={"list-news-slider"}
                title={item?.style?.title}
                titleStyle={{ color: appConfig?.color?.primary }}
                seeAll={() => {
                  onNavigateToPost(item?.style?.title);
                }}
                btnAction={
                  <BtnAction
                    text={item?.style?.moreText || "Xem tất cả"}
                    styleOverride={{ color: appConfig?.color?.primary, fontSize: 15 }}
                    icon={<ArrowRightIcon fillColor={appConfig?.color?.primary} />}
                    eventAction={() => {
                      onNavigateToPost(item?.style?.title);
                    }}
                  />
                }
                sliceConfig={{
                  dots: false,
                  infinite: false,
                  slidesToShow: 1.5,
                  autoplay: false,
                  arrows: false,
                }}
              >
                {isLoadingMap[key] ? (
                  renderNewsSkeletonLoading(true)
                ) : Array.isArray(listArticleMap[key]) && listArticleMap[key].length > 0 ? (
                  listArticleMap[key].map((news: INews, idx: number) => (
                    <Box key={idx}>
                      <NewsItem
                        news={news}
                        key={news.articleId || idx}
                        titleFromParent={item?.style?.title}
                      />
                    </Box>
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListSlider>
            </Box>
          );
        }
        break;
      }
      case keyComponent.PointBox:
        com = (
          <Box key={index} px={2} borderRadius={5}>
            <PointBox />
          </Box>
        );
        break;
      case keyComponent.Voucher:
        com = <VoucherHome key={index} item={item} />;
        break;
    }
    return com || null;
  };

  return (
    <>
      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
      <LayoutHomePage title={<HeaderBar />}>
        <Box sx={{ overflowX: "hidden", position: "relative" }}>
          {!isLoading && advertisements.length > 0 && (
            <PopupAdvertise advertisements={advertisements} />
          )}
        </Box>
        <Container style={containerStyle}>
          {home?.map((item, index) => renderComponent(item, index))}
        </Container>
      </LayoutHomePage>
    </>
  );
};

export default HomePage;
