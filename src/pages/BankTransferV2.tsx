import React, { useEffect, useState } from "react";
import { Box, Button, Grid, IconButton, Skeleton, Stack, Typography } from "@mui/material";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import { IOrder } from "../types/order";
import { Router } from "../constants/Route";
import { useLocation } from "react-router-dom";
import { useNavigate } from "../utils/component-util";
import { copy } from "../utils/common";
import { useCart } from "@/hooks/useCart";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { IPaymentMethod } from "@/types/paymentMethod";
import { formatPrice } from "@/utils/formatPrice";
import FrameContainer from "@/components/layout/Container";
import {
  DataGetListPayment,
  getPaymentMethodList,
} from "@/redux/slices/paymentMethod/paymentMethodSlice";
import { Platform } from "@/config";

//TODO: Update for Gia tin
const Receiver = {
  name: "",
  accountNumber: "",
  bank: "",
};

export default function BankTransferV2() {
  const location = useLocation();
  const order = location.state.order as IOrder;
  const link = location.state.link;
  const { listPaymentMethod } = useSelector((state: RootState) => state.paymentMethod);
  const paymentTransfer: IPaymentMethod | undefined = listPaymentMethod?.find(
    (item) => item.typePay === "Transfer"
  );
  const { shopId, shopInfo } = useSelector((state: RootState) => state.appInfo);
  const dispatch = useDispatch<AppDispatch>();
  const [loaded, setLoaded] = useState(false);
  var qrUrl = `https://img.vietqr.io/image/${paymentTransfer?.bankShortCode}-${
    paymentTransfer?.bankAccountNumber
  }-compact2.jpg?amount=${order.price}&addInfo=${encodeURIComponent(
    `Thanh toan ${order?.orderNo}`
  )}&accountName=${encodeURIComponent(paymentTransfer?.customerBankName ?? "")}`;
  const navigate = useNavigate();

  const renderCopy = (text: string) => {
    return (
      <IconButton onClick={() => copy(text)}>
        <FileCopyIcon />
      </IconButton>
    );
  };

  useEffect(() => {
    if (shopId) {
      if (listPaymentMethod.length == 0) {
        const data: DataGetListPayment = {
          shopId: shopId || "",
          platform: "ZaloMiniApp",
        };
        dispatch(getPaymentMethodList(data));
      }
    }
  }, [shopId]);

  return (
    <FrameContainer title="Chuyển khoản">
      <Stack alignItems={"center"} marginTop={2}>
        <Typography variant="h5" fontWeight={"bold"}>
          Thông tin thanh toán
        </Typography>
        <Typography>
          Vui lòng chuyển khoản vào tài khoản với nội dung bên dưới. <br />
          {/* Sau khoảng 1, 2 phút, đơn hàng sẽ được chuyển thành trạng thái <b>Đã thanh toán</b>. */}
        </Typography>
        {!loaded && <Skeleton animation="wave" variant="rectangular" width={300} height={300} />}
        <img
          src={qrUrl}
          width={300}
          height="auto"
          style={{ marginTop: 20 }}
          onLoad={() => setLoaded(true)}
        />
        <Grid
          container
          justifyContent="center"
          padding={1}
          marginBottom={1}
          direction="column"
          alignItems="center"
        >
          <Grid item container justifyContent="center" alignItems="center">
            <Typography>Số tài khoản:</Typography>
            <Typography fontWeight="bold" marginLeft={1}>
              {paymentTransfer?.bankAccountNumber}
            </Typography>
            {renderCopy(paymentTransfer?.bankAccountNumber || "")}
          </Grid>
          <Grid item container justifyContent="center" alignItems="center">
            <Typography>Tên người nhận:</Typography>
            <Typography fontWeight="bold" marginLeft={1}>
              {paymentTransfer?.customerBankName}
            </Typography>
            {renderCopy(paymentTransfer?.customerBankName || "")}
          </Grid>
          <Grid item container justifyContent="center" alignItems="center">
            <Typography>Nội dung: </Typography>
            <Box display="flex" alignItems="center">
              <Typography fontWeight="bold" marginLeft={1}>
                {`Thanh toan ${order?.orderNo}`}
              </Typography>
              {renderCopy(`Thanh toan ${order?.orderNo}`)}
            </Box>
          </Grid>
          <Grid item container justifyContent="center" alignItems="center">
            <Typography>Số tiền: </Typography>
            <Typography fontWeight="bold" marginLeft={1}>
              {formatPrice(order?.totalAfterTax || 0)}
            </Typography>
          </Grid>
        </Grid>
        <Button
          variant="contained"
          onClick={() =>
            navigate(link ? link : `${Router.checkoutResult}?orderId=${order?.orderId}`)
          }
        >
          Tôi đã chuyển
        </Button>
      </Stack>
    </FrameContainer>
  );
}
