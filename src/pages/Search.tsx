import { FormControl, InputAdornment, Stack, TextField } from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import React, { useEffect, useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import { debounce } from "lodash";
import { COLORS } from "../constants/themes";
import { SearchNormal } from "../constants/IconSvg";

export type TSearchProps = {
  keyword: string;
  setKeyword: (search: string) => void;
  startIcon: boolean;
  textPlaceholder: string;
  size: "small" | "medium";
};

export const Search: React.FC<TSearchProps> = (props) => {
  const { keyword, setKeyword, startIcon, textPlaceholder, size } = props;
  const [showClearIcon, setShowClearIcon] = useState("none");
  const [key, setKey] = useState(keyword);

  useEffect(() => {
    setShowClearIcon(key === "" ? "none" : "flex");
  }, [key]);

  const handleChangeSearch = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setKey(event.target.value);
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    blurSearch(event);
  };

  const blurSearch = debounce(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      setKeyword(event.target.value);
    },
    500
  );

  const handleClick = (): void => {
    setKeyword("");
    setKey("");
  };

  return (
    <Stack>
      <FormControl>
        <TextField
          placeholder={textPlaceholder}
          style={{
            width: "100%",
            backgroundColor: COLORS.white,
            borderRadius: "10px",
            height: "50px",
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              borderRadius: "10px",
              border: `0.5px solid ${COLORS.neutral14}`,
              height: "50px",
            },
            "& .MuiInputBase-input": {
              color: COLORS.neutral1,
            },
          }}
          size={size}
          variant="outlined"
          value={key}
          onChange={handleChangeSearch}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end" onClick={handleClick}>
                {/* <ClearIcon style={{ display: showClearIcon }} /> */}
                <SearchNormal />
              </InputAdornment>
            ),
          }}
        />
      </FormControl>
    </Stack>
  );
};
