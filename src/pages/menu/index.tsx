import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import MenuRetail from "./MenuRetail";
import MenuFnB from "./MenuFnB";

const componentMap = {
  FnB: MenuRetail,
  Retail: MenuRetail,
};

export default function Menu(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FnB");
  const Component = componentMap[businessType] || componentMap.FnB;
  return <Component {...props} />;
}
