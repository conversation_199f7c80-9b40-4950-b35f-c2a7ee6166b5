import { useCallback, useEffect, useRef, useState } from "react";
import { useDebounce } from "./useDebounce";

interface UseInfiniteScrollOptions<T> {
  fetchFunction: (params: any) => Promise<{ result: T[]; total: number; pageIndex?: number }>;
  pageSize?: number;
  initialParams?: any;
  debounceMs?: number;
  threshold?: number;
  rootMargin?: string;
  enabled?: boolean;
  onError?: (error: Error) => void;
  onSuccess?: (data: T[], isFirstPage: boolean) => void;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
  loadMore: () => Promise<void>;
  reset: () => void;
  observerRef: React.RefObject<HTMLDivElement>;
  updateParams: (newParams: any) => void;
}

export function useInfiniteScroll<T = any>({
  fetchFunction,
  pageSize = 20,
  initialParams = {},
  debounceMs = 300,
  threshold = 0.1,
  rootMargin = "100px",
  enabled = true,
  onError,
  onSuccess,
}: UseInfiniteScrollOptions<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [params, setParams] = useState(initialParams);
  const [total, setTotal] = useState(0);

  const observerRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);
  const debouncedParams = useDebounce(params, debounceMs);

  // Reset function
  const reset = useCallback(() => {
    setData([]);
    setCurrentPage(0);
    setHasMore(true);
    setError(null);
    setTotal(0);
  }, []);

  // Update params function
  const updateParams = useCallback(
    (newParams: any) => {
      setParams(newParams);
      reset();
    },
    [reset]
  );

  // Fetch data function
  const fetchData = useCallback(
    async (page: number, isRefresh = false) => {
      if (isLoadingRef.current || !enabled) return;

      isLoadingRef.current = true;
      setError(null);

      const isFirstPage = page === 0;
      if (isFirstPage) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      try {
        const response = await fetchFunction({
          ...debouncedParams,
          PageIndex: page,
          PageSize: pageSize,
        });

        const newData = response.result || [];
        const newTotal = response.total || 0;

        setTotal(newTotal);

        if (isFirstPage || isRefresh) {
          setData(newData);
        } else {
          setData((prev) => [...prev, ...newData]);
        }

        // Calculate hasMore
        const totalLoaded = isFirstPage ? newData.length : data.length + newData.length;
        setHasMore(totalLoaded < newTotal);
        setCurrentPage(page);

        onSuccess?.(newData, isFirstPage);
      } catch (err) {
        const error = err instanceof Error ? err : new Error("Unknown error");
        setError(error);
        onError?.(error);
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
        isLoadingRef.current = false;
      }
    },
    [debouncedParams, pageSize, enabled, fetchFunction, data.length, onError, onSuccess]
  );

  // Refresh function
  const refresh = useCallback(async () => {
    reset();
    await fetchData(0, true);
  }, [fetchData, reset]);

  // Load more function
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingRef.current) return;
    await fetchData(currentPage + 1);
  }, [hasMore, currentPage, fetchData]);

  // Effect for initial load and params change
  useEffect(() => {
    if (enabled) {
      fetchData(0, true);
    }
  }, [debouncedParams, enabled]);

  // Intersection Observer for auto load more
  useEffect(() => {
    if (!enabled || !hasMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !isLoadingRef.current) {
          loadMore();
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    const currentRef = observerRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, enabled, loadMore, threshold, rootMargin]);

  return {
    data,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    refresh,
    loadMore,
    reset,
    observerRef,
    updateParams,
  };
}
