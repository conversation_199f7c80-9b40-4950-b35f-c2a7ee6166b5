// import { useEffect } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import store, { AppDispatch, RootState } from "../redux/store";
// import {
//   getUser,
//   getUserZalo,
//   logout,
//   updateMe,
//   updateUser,
// } from "../redux/slices/authen/authSlice";
// import { getRouteParams } from "zmp-sdk";
// import { getProductCategoryList } from "../redux/slices/product/productSlice";
// import {
//   getBannerHome1,
//   getListArticleBannerBranchShopConfig,
//   getListArticleBannerHomeShopConfig,
//   getNewsList,
// } from "../redux/slices/news/newsListSlice";
// import { getProductList } from "../redux/slices/product/productListSlice";
// import { axiosInstance } from "../utils/request";
// import { getItem, setItem } from "../utils/storage";
// import { StorageKeys } from "../constants/storageKeys";
// import { Router } from "../constants/Route";
// import { useNavigate } from "../utils/component-util";
// import { Platform } from "../config";
// import { getShopId, getShopPolicy } from "../redux/slices/appInfo/appInfoSlice";
// import { setDeepLinkStartAppNavigated } from "../redux/slices/configSlice";
// import { events, Events } from "zmp-sdk/apis";
// import { getCart } from "@/redux/slices/cart/cartSlice";
// import { getAddressList } from "@/redux/slices/address/addressSlice";
// import { getBranchList } from "@/redux/slices/branch/branchSlice";
// import { useConfigApp } from "./useConfigApp";
// import {
//   getAffiliateReport,
//   getAffiliationConfigActiveStatus,
//   getRecruitmentPage,
//   updateShopVisit,
// } from "@/redux/slices/affiliation/affiliationSlice";
// import {
//   DataGetListPayment,
//   getPaymentMethodList,
// } from "@/redux/slices/paymentMethod/paymentMethodSlice";
// import { searchByPointsCode } from "@/redux/slices/voucher/voucherSlice";
// const MiniAppId = import.meta.env.VITE_ZMP_APP_ID;

// export default function initData() {
//   const dispatch = useDispatch<AppDispatch>();
//   const navigate = useNavigate();
//   const { user, userZalo } = useSelector((state: RootState) => state.auth);
//   const { listPaymentMethod } = useSelector((state: RootState) => state.paymentMethod);
//   const { home } = useConfigApp();

//   const { shopId, shopInfo } = useSelector((state: RootState) => state.appInfo);

//   // Handle click refer link
//   const { refCode, targetType, page, id } = getRouteParams();
//   const { deepLinkStartAppNavigated } = useSelector((state: RootState) => state.config);

//   useEffect(() => {
//     init();

//     events.on(Events.OpenApp, (data) => {
//       if (data?.path) {
//         const openPage = new URLSearchParams(data.path).get("page");
//         const openId = new URLSearchParams(data.path).get("id");
//         console.log("openApp data", data, openPage, openId);
//         if (!openPage || !openId) return;
//         navigate(openPage + `/${openId}`);
//       }
//     });
//     return () => {
//       events.off(Events.OpenApp, (data) => {
//         console.log("openApp data events off: ", data);
//       });
//     };
//   }, []);

//   useEffect(() => {
//     if (shopId && home && home.length > 0) {
//       const articleIdsBannerBranch = home
//         .filter((item) => item.type === "BannerWithBranch" && Array.isArray(item.articleIds))
//         .reduce((acc, item) => acc.concat(item.articleIds || []), [] as string[]);
//       if (articleIdsBannerBranch.length > 0) {
//         dispatch(
//           getListArticleBannerBranchShopConfig({
//             articleIds: articleIdsBannerBranch,
//             shopId,
//           })
//         );
//       }

//       const articleIdsBannerHome = home
//         .filter((item) => item.type === "BannerHome" && Array.isArray(item.articleIds))
//         .reduce((acc, item) => acc.concat(item.articleIds || []), [] as string[]);

//       if (articleIdsBannerHome.length > 0) {
//         dispatch(
//           getListArticleBannerHomeShopConfig({
//             articleIds: articleIdsBannerHome,
//             shopId,
//           })
//         );
//       }
//     }
//   }, [home, shopId]);

//   const init = async () => {
//     const token = await getItem(StorageKeys.AccessToken);

//     if (token) {
//       axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
//       await dispatch(getUser());

//       if (Platform === "zalo") {
//         await dispatch(getUserZalo());
//       }
//       store.dispatch(getAddressList());
//       store.dispatch(getCart());
//     } else {
//       dispatch(logout());
//     }

//     fetchData();
//   };

//   useEffect(() => {
//     const fetchUser = async () => {
//       const token = await getItem(StorageKeys.AccessToken);
//       if (token && shopId) {
//         await dispatch(getUser());
//       }
//     };
//     fetchUser();
//   }, [shopId]);

//   useEffect(() => {
//     shopInfo.shopName && (document.title = shopInfo.shopName);
//     const favicon = document.querySelector("link[rel*='icon']");
//     if (favicon) {
//       (favicon as HTMLLinkElement).href = shopInfo.shopLogo || "/images/logo.png";
//     }
//   }, [shopInfo.shopName, shopInfo.shopLogo]);

//   const fetchData = async () => {
//     // updateName();
//     // store.dispatch(getConfig());
//     let shopId = "";
//     const domain = window.location.hostname;
//     if (Platform !== "zalo" && !domain.includes("localhost") && !domain.includes("127.0.01")) {
//       const info = await store.dispatch(getShopId({ domain }));
//       shopId = info?.payload?.shopId;
//     } else if (MiniAppId) {
//       const info = await store.dispatch(getShopId({ miniAppId: MiniAppId }));
//       shopId = info?.payload?.shopId;
//     } else {
//       console.log("Please set MiniAppId in .env file");
//       return;
//     }
//     if (shopId) {
//       // axiosInstance.defaults.data = {
//       //   shopId: info.payload.shopId,
//       // };
//       axiosInstance.interceptors.request.use((config) => {
//         if (config.method === "get") {
//           config.params = {
//             ...config.params,
//             shopId,
//           };
//         } else {
//           config.data = { ...config.data, shopId };
//         }
//         return config;
//       });
//     }
//     store.dispatch(getProductCategoryList());
//     store.dispatch(getNewsList());
//     store.dispatch(getBannerHome1());
//     store.dispatch(getProductList());
//     store.dispatch(getBranchList());
//     store.dispatch(getShopPolicy({ shopId }));
//     store.dispatch(getRecruitmentPage());
//     // store.dispatch(getHotProductList());
//   };

//   useEffect(() => {
//     if (userZalo && user) {
//       updateName();
//     }
//   }, [userZalo, user]);

//   useEffect(() => {
//     if (user && shopId) {
//       dispatch(updateShopVisit({ shopId }));
//     }
//     if (shopId) {
//       dispatch(getAffiliationConfigActiveStatus({ shopId }));
//     }
//   }, [shopId, user]);

//   // //Update name
//   const updateName = async () => {
//     const objToUpdate: any = {};
//     // userZalo?.name && user?.name !== userZalo?.name && (objToUpdate.name = userZalo?.name);
//     // userZalo?.avatar &&
//     //   user?.avatarUrl !== userZalo?.avatar &&
//     //   (objToUpdate.avatarUrl = userZalo?.avatar);
//     userZalo?.id && user?.zaloId !== userZalo?.id && (objToUpdate.zaloId = userZalo?.id);

//     userZalo?.idByOA &&
//       user?.zaloIdByOA !== userZalo?.idByOA &&
//       (objToUpdate.zaloIdByOA = userZalo?.idByOA);
//     if (Object.keys(objToUpdate).length > 0 && user?.id) {
//       dispatch(updateMe(objToUpdate));
//     } else {
//       console.log("No update needed, user name is already the same.");
//     }
//   };

//   useEffect(() => {
//     if (targetType && targetType === "REFER") {
//       navigate(Router.refer.index + "?refCode=" + refCode);
//     }
//   }, [targetType]);

//   useEffect(() => {
//     const promoCode = new URLSearchParams(window.location.search).get("promo-code");

//     if (promoCode) {
//       navigate(Router.voucher.detail.replace(":id", promoCode));
//     }
//   }, []);

//   useEffect(() => {
//     if (!deepLinkStartAppNavigated) {
//       if (refCode) {
//         setItem(StorageKeys.RefCode, refCode);
//       }
//       if (page) {
//         navigate(page + `/${id}`);
//       }
//       dispatch(setDeepLinkStartAppNavigated(true));
//     }
//   }, [refCode, page, deepLinkStartAppNavigated]);
// }

import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import store, { AppDispatch, RootState } from "../redux/store";
import {
  getUser,
  getUserZalo,
  logout,
  updateMe,
  updateUser,
} from "../redux/slices/authen/authSlice";
import { getRouteParams } from "zmp-sdk";
import { getProductCategoryList } from "../redux/slices/product/productSlice";
import {
  getBannerHome1,
  getListArticleBannerBranchShopConfig,
  getListArticleBannerHomeShopConfig,
  getNewsList,
} from "../redux/slices/news/newsListSlice";
import { getProductList, getProductListByCategory } from "../redux/slices/product/productListSlice";
import { axiosInstance } from "../utils/request";
import { getItem, setItem } from "../utils/storage";
import { StorageKeys } from "../constants/storageKeys";
import { Router } from "../constants/Route";
import { useNavigate } from "../utils/component-util";
import { Platform } from "../config";
import { getShopId, getShopPolicy } from "../redux/slices/appInfo/appInfoSlice";
import { setDeepLinkStartAppNavigated } from "../redux/slices/configSlice";
import { events, Events } from "zmp-sdk/apis";
import { getCart } from "@/redux/slices/cart/cartSlice";
import { getAddressList } from "@/redux/slices/address/addressSlice";
import { getBranchList } from "@/redux/slices/branch/branchSlice";
import { useConfigApp } from "./useConfigApp";
import {
  getAffiliateReport,
  getAffiliationConfigActiveStatus,
  getRecruitmentPage,
  updateShopVisit,
} from "@/redux/slices/affiliation/affiliationSlice";
import {
  DataGetListPayment,
  getPaymentMethodList,
} from "@/redux/slices/paymentMethod/paymentMethodSlice";
import { searchByPointsCode } from "@/redux/slices/voucher/voucherSlice";
import { fetchGamificationData, getCampaign } from "@/redux/slices/gamification/gamificationSlice";
const MiniAppId = import.meta.env.VITE_ZMP_APP_ID;

export default function initData() {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user, userZalo } = useSelector((state: RootState) => state.auth);
  const { listPaymentMethod } = useSelector((state: RootState) => state.paymentMethod);
  const { home } = useConfigApp();

  const { shopId, shopInfo } = useSelector((state: RootState) => state.appInfo);

  // Handle click refer link
  const { refCode, targetType, page, id } = getRouteParams();
  const { deepLinkStartAppNavigated } = useSelector((state: RootState) => state.config);

  useEffect(() => {
    events.on(Events.OpenApp, (data) => {
      if (data?.path) {
        const openPage = new URLSearchParams(data.path).get("page");
        const openId = new URLSearchParams(data.path).get("id");
        console.log("openApp data", data, openPage, openId);
        if (!openPage || !openId) return;
        navigate(openPage + `/${openId}`);
      }
    });
    return () => {
      events.off(Events.OpenApp, (data) => {
        console.log("openApp data events off: ", data);
      });
    };
  }, []);

  useEffect(() => {
    if (shopId && home && home.length > 0) {
      const articleIdsBannerBranch = home
        .filter((item) => item.type === "BannerWithBranch" && Array.isArray(item.articleIds))
        .reduce((acc, item) => acc.concat(item.articleIds || []), [] as string[]);
      if (articleIdsBannerBranch.length > 0) {
        dispatch(
          getListArticleBannerBranchShopConfig({
            articleIds: articleIdsBannerBranch,
            shopId,
          })
        );
      }

      const articleIdsBannerHome = home
        .filter((item) => item.type === "BannerHome" && Array.isArray(item.articleIds))
        .reduce((acc, item) => acc.concat(item.articleIds || []), [] as string[]);

      if (articleIdsBannerHome.length > 0) {
        dispatch(
          getListArticleBannerHomeShopConfig({
            articleIds: articleIdsBannerHome,
            shopId,
          })
        );
      }
    }
  }, [home, shopId]);

  useEffect(() => {
    if (shopId) {
      const init = async () => {
        const token = await getItem(StorageKeys.AccessToken);

        if (token) {
          axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
          await dispatch(getUser());

          if (Platform === "zalo") {
            await dispatch(getUserZalo());
          }
          store.dispatch(getAddressList());
          store.dispatch(getCart());
        } else {
          dispatch(logout());
        }
      };
      init();
    }
  }, [shopId]);

  useEffect(() => {
    shopInfo.shopName && (document.title = shopInfo.shopName);
    const favicon = document.querySelector("link[rel*='icon']");
    if (favicon) {
      (favicon as HTMLLinkElement).href = shopInfo.shopLogo?.link || "/images/logo.png";
    }
  }, [shopInfo.shopName, shopInfo.shopLogo?.link]);

  // const fetchData = async () => {
  //   // updateName();
  //   // store.dispatch(getConfig());
  //   let shopId = "";
  //   const domain = window.location.hostname;
  //   if (Platform !== "zalo" && !domain.includes("localhost") && !domain.includes("127.0.01")) {
  //     const info = await store.dispatch(getShopId({ domain }));
  //     shopId = info?.payload?.shopId;
  //   } else if (MiniAppId) {
  //     const info = await store.dispatch(getShopId({ miniAppId: MiniAppId }));
  //     shopId = info?.payload?.shopId;
  //   } else {
  //     console.log("Please set MiniAppId in .env file");
  //     return;
  //   }
  //   if (shopId) {
  //     // axiosInstance.defaults.data = {
  //     //   shopId: info.payload.shopId,
  //     // };
  //     axiosInstance.interceptors.request.use((config) => {
  //       if (config.method === "get") {
  //         config.params = {
  //           ...config.params,
  //           shopId,
  //         };
  //       } else {
  //         config.data = { ...config.data, shopId };
  //       }
  //       return config;
  //     });
  //   }
  //   store.dispatch(getProductCategoryList());
  //   store.dispatch(getNewsList());
  //   store.dispatch(getBannerHome1());
  //   store.dispatch(getProductList());
  //   store.dispatch(getBranchList());
  //   store.dispatch(getShopPolicy({ shopId }));
  //   store.dispatch(getRecruitmentPage());
  //   // store.dispatch(getHotProductList());
  // };

  useEffect(() => {
    const initShopId = async () => {
      let shopId = "";
      const domain = window.location.hostname;

      if (Platform !== "zalo" && !domain.includes("localhost") && !domain.includes("127.0.01")) {
        const info = await store.dispatch(getShopId({ domain }));
        shopId = info?.payload?.shopId;
      } else if (MiniAppId) {
        const info = await store.dispatch(getShopId({ miniAppId: MiniAppId }));
        shopId = info?.payload?.shopId;
      }
    };

    initShopId();
  }, []);

  // Gọi các API khác khi đã có shopId
  useEffect(() => {
    if (shopId) {
      // Setup interceptor
      axiosInstance.interceptors.request.use((config) => {
        if (config.method === "get") {
          config.params = { ...config.params, shopId };
        } else {
          config.data = { ...config.data, shopId };
        }
        return config;
      });

      // Gọi các API
      store.dispatch(getProductCategoryList());
      store.dispatch(getNewsList());
      store.dispatch(getBannerHome1());
      store.dispatch(getProductListByCategory({ forceReload: true }));
      store.dispatch(getBranchList());
      store.dispatch(getShopPolicy({ shopId }));
      store.dispatch(getRecruitmentPage());
      store.dispatch(getCampaign());
    }
  }, [shopId]);

  useEffect(() => {
    if (userZalo && user) {
      updateName();
    }
  }, [userZalo, user]);

  useEffect(() => {
    if (user && shopId) {
      dispatch(updateShopVisit({ shopId }));
    }
    if (shopId) {
      dispatch(getAffiliationConfigActiveStatus({ shopId }));
    }
  }, [shopId, user]);

  // //Update name
  const updateName = async () => {
    const objToUpdate: any = {};
    // userZalo?.name && user?.name !== userZalo?.name && (objToUpdate.name = userZalo?.name);
    // userZalo?.avatar &&
    //   user?.avatarUrl !== userZalo?.avatar &&
    //   (objToUpdate.avatarUrl = userZalo?.avatar);
    userZalo?.id && user?.zaloId !== userZalo?.id && (objToUpdate.zaloId = userZalo?.id);

    userZalo?.idByOA &&
      user?.zaloIdByOA !== userZalo?.idByOA &&
      (objToUpdate.zaloIdByOA = userZalo?.idByOA);
    if (Object.keys(objToUpdate).length > 0 && user?.id) {
      dispatch(updateMe(objToUpdate));
    } else {
      console.log("No update needed, user name is already the same.");
    }
  };

  useEffect(() => {
    if (targetType && targetType === "REFER") {
      navigate(Router.refer.index + "?refCode=" + refCode);
    }
  }, [targetType]);

  useEffect(() => {
    const promoCode = new URLSearchParams(window.location.search).get("promo-code");

    if (promoCode) {
      navigate(Router.voucher.detail.replace(":code", promoCode));
    }
  }, [shopId]);

  useEffect(() => {
    if (!deepLinkStartAppNavigated) {
      if (refCode) {
        setItem(StorageKeys.RefCode, refCode);
      }
      if (page) {
        navigate(page + `/${id}`);
      }
      dispatch(setDeepLinkStartAppNavigated(true));
    }
  }, [refCode, page, deepLinkStartAppNavigated]);

  useEffect(() => {
    const fetchUser = async () => {
      const token = await getItem(StorageKeys.AccessToken);
      if (token && shopId) {
        axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
        await dispatch(getUser());
        if (Platform === "zalo") {
          await dispatch(getUserZalo());
        }
        store.dispatch(getAddressList());
        store.dispatch(getCart());
      } else if (!token) {
        dispatch(logout());
      }
    };
    fetchUser();
  }, [shopId]);
}
