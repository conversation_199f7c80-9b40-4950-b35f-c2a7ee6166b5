import { MenuActionProduct } from "./../components/icon/MenuActionIcon";
import { MenuActionShip } from "./../components/icon/MenuActionIcon";
import { AppEnv, Platform } from "../config";
import { Icon } from "./Assets";
import { Router } from "./Route";
import {
  Address,
  Policy,
  Question,
  Setting,
  Agency,
  LogoutIcon,
  MoneySVG,
  Voucher,
  AppIcon,
  Map,
  Wallet,
  Star,
  Car,
  Box,
} from "./IconSvg";

export const policy = [
  {
    slug: "dieu-khoan-su-dung",
    title: "Điều khoản sử dụng",
    content: `A.GIỚI THIỆU`,
    subTitle: `<PERSON><PERSON><PERSON>c điều khoản chung`,
    decs1: `1. Chào mừng đến với Droppii – <PERSON><PERSON><PERSON> thương mại điện tử kết hợp tư vấn, chăm sóc 24/7 đầu tiên tại Việt Nam. Chúng tôi là Công Ty TNHH Dandelion Việt Nam có địa chỉ trụ sở t<PERSON>i <PERSON>, <PERSON><PERSON><PERSON>, 123 <PERSON><PERSON><PERSON><PERSON>, ph<PERSON><PERSON><PERSON> <PERSON><PERSON>u, Quận 3, Thành phố Hồ Chí Minh, Việt Nam theo Giấy chứng nhận đăng ký doanh nghiệp số 0317251228 do Sở Kế hoạch và Đầu tư Thành phố Hồ Chí <PERSON> cấp ngày 15/04/2022 (sau đây gọi tắt là “Chúng tôi”).`,
    decs2: `2. Bằng việc đăng ký tài khoản sử dụng dịch vụ của Droppii, Người dùng đã chấp nhận và đồng ý tuân thủ với các điều khoản sử dụng, các chính sách được đề cập hoặc những thông báo gửi (gọi chung là “Điều khoản và chính sách”). Chúng tôi có quyền thay đổi, chỉnh sửa, thêm hoặc lược bỏ bất kỳ phần nào trong Điều khoản sử dụng này, vào bất cứ lúc nào. Khi tiếp tục sử dụng sau khi các thay đổi về Điều khoản này được đăng tải, có nghĩa là người dùng đã chấp nhận với những thay đổi đó.`,
    decs3: `3. Vui lòng kiểm tra thường xuyên để cập nhật những thay đổi của Nền tảng Droppii. Chúng tôi không chịu trách nhiệm về chất lượng đường truyền Internet ảnh hưởng đến tốc độ truy cập của người dùng vào Nền tảng.`,
    decs4: `4. Người dùng có thể tự do lựa chọn, xem tất cả thông tin và sản phẩm có trong Droppii. Tuy nhiên, nếu không được Chúng tôi cho phép bằng văn bản, vui lòng KHÔNG sử dụng bất kỳ phần nào của Nền tảng Droppii này với mục đích thương mại.`,
  },

  {
    slug: "chinh-sach-thanh-toan",
    title: "Chính sách thanh toán",
    content: `A.GIỚI THIỆU`,
    subTitle: `I.Các điều khoản chung`,
    decs1: `1. Chào mừng đến với Droppii – Kênh thương mại điện tử kết hợp tư vấn, chăm sóc 24/7 đầu tiên tại Việt Nam. Chúng tôi là Công Ty TNHH Dandelion Việt Nam có địa chỉ trụ sở tại Tầng 05, Tòa nhà Pax Sky, 123 Nguyễn Đình Chiểu, phường Võ Thị Sáu, Quận 3, Thành phố Hồ Chí Minh, Việt Nam theo Giấy chứng nhận đăng ký doanh nghiệp số 0317251228 do Sở Kế hoạch và Đầu tư Thành phố Hồ Chí Minh cấp ngày 15/04/2022 (sau đây gọi tắt là “Chúng tôi”).`,
    decs2: `2. Bằng việc đăng ký tài khoản sử dụng dịch vụ của Droppii, Người dùng đã chấp nhận và đồng ý tuân thủ với các điều khoản sử dụng, các chính sách được đề cập hoặc những thông báo gửi (gọi chung là “Điều khoản và chính sách”). Chúng tôi có quyền thay đổi, chỉnh sửa, thêm hoặc lược bỏ bất kỳ phần nào trong Điều khoản sử dụng này, vào bất cứ lúc nào. Khi tiếp tục sử dụng sau khi các thay đổi về Điều khoản này được đăng tải, có nghĩa là người dùng đã chấp nhận với những thay đổi đó.`,
    decs3: `3. Vui lòng kiểm tra thường xuyên để cập nhật những thay đổi của Nền tảng Droppii. Chúng tôi không chịu trách nhiệm về chất lượng đường truyền Internet ảnh hưởng đến tốc độ truy cập của người dùng vào Nền tảng.`,
    decs4: `4. Người dùng có thể tự do lựa chọn, xem tất cả thông tin và sản phẩm có trong Droppii. Tuy nhiên, nếu không được Chúng tôi cho phép bằng văn bản, vui lòng KHÔNG sử dụng bất kỳ phần nào của Nền tảng Droppii này với mục đích thương mại.`,
  },
  {
    slug: "chinh-sach-van-chuyen-va-giao-nhan",
    title: "Chính sách vận chuyển và giao nhận",
    content: `A.GIỚI THIỆU`,
    subTitle: `I.Các điều khoản chung`,
    decs1: `1. Chào mừng đến với Droppii – Kênh thương mại điện tử kết hợp tư vấn, chăm sóc 24/7 đầu tiên tại Việt Nam. Chúng tôi là Công Ty TNHH Dandelion Việt Nam có địa chỉ trụ sở tại Tầng 05, Tòa nhà Pax Sky, 123 Nguyễn Đình Chiểu, phường Võ Thị Sáu, Quận 3, Thành phố Hồ Chí Minh, Việt Nam theo Giấy chứng nhận đăng ký doanh nghiệp số 0317251228 do Sở Kế hoạch và Đầu tư Thành phố Hồ Chí Minh cấp ngày 15/04/2022 (sau đây gọi tắt là “Chúng tôi”).`,
    decs2: `2. Bằng việc đăng ký tài khoản sử dụng dịch vụ của Droppii, Người dùng đã chấp nhận và đồng ý tuân thủ với các điều khoản sử dụng, các chính sách được đề cập hoặc những thông báo gửi (gọi chung là “Điều khoản và chính sách”). Chúng tôi có quyền thay đổi, chỉnh sửa, thêm hoặc lược bỏ bất kỳ phần nào trong Điều khoản sử dụng này, vào bất cứ lúc nào. Khi tiếp tục sử dụng sau khi các thay đổi về Điều khoản này được đăng tải, có nghĩa là người dùng đã chấp nhận với những thay đổi đó.`,
    decs3: `3. Vui lòng kiểm tra thường xuyên để cập nhật những thay đổi của Nền tảng Droppii. Chúng tôi không chịu trách nhiệm về chất lượng đường truyền Internet ảnh hưởng đến tốc độ truy cập của người dùng vào Nền tảng.`,
    decs4: `4. Người dùng có thể tự do lựa chọn, xem tất cả thông tin và sản phẩm có trong Droppii. Tuy nhiên, nếu không được Chúng tôi cho phép bằng văn bản, vui lòng KHÔNG sử dụng bất kỳ phần nào của Nền tảng Droppii này với mục đích thương mại.`,
  },
  {
    slug: "chinh-sach-bao-mat",
    title: "Chính sách bảo mật",
    content: `A.GIỚI THIỆU`,
    subTitle: `I.Các điều khoản chung`,
    decs1: `1. Chào mừng đến với Droppii – Kênh thương mại điện tử kết hợp tư vấn, chăm sóc 24/7 đầu tiên tại Việt Nam. Chúng tôi là Công Ty TNHH Dandelion Việt Nam có địa chỉ trụ sở tại Tầng 05, Tòa nhà Pax Sky, 123 Nguyễn Đình Chiểu, phường Võ Thị Sáu, Quận 3, Thành phố Hồ Chí Minh, Việt Nam theo Giấy chứng nhận đăng ký doanh nghiệp số 0317251228 do Sở Kế hoạch và Đầu tư Thành phố Hồ Chí Minh cấp ngày 15/04/2022 (sau đây gọi tắt là “Chúng tôi”).`,
    decs2: `2. Bằng việc đăng ký tài khoản sử dụng dịch vụ của Droppii, Người dùng đã chấp nhận và đồng ý tuân thủ với các điều khoản sử dụng, các chính sách được đề cập hoặc những thông báo gửi (gọi chung là “Điều khoản và chính sách”). Chúng tôi có quyền thay đổi, chỉnh sửa, thêm hoặc lược bỏ bất kỳ phần nào trong Điều khoản sử dụng này, vào bất cứ lúc nào. Khi tiếp tục sử dụng sau khi các thay đổi về Điều khoản này được đăng tải, có nghĩa là người dùng đã chấp nhận với những thay đổi đó.`,
    decs3: `3. Vui lòng kiểm tra thường xuyên để cập nhật những thay đổi của Nền tảng Droppii. Chúng tôi không chịu trách nhiệm về chất lượng đường truyền Internet ảnh hưởng đến tốc độ truy cập của người dùng vào Nền tảng.`,
    decs4: `4. Người dùng có thể tự do lựa chọn, xem tất cả thông tin và sản phẩm có trong Droppii. Tuy nhiên, nếu không được Chúng tôi cho phép bằng văn bản, vui lòng KHÔNG sử dụng bất kỳ phần nào của Nền tảng Droppii này với mục đích thương mại.`,
  },
  {
    slug: "chinh-sach-giai-quyet-khieu-nai",
    title: "Chính sách giải quyết khiếu nại",
    content: `A.GIỚI THIỆU`,
    subTitle: `I.Các điều khoản chung`,
    decs1: `1. Chào mừng đến với Droppii – Kênh thương mại điện tử kết hợp tư vấn, chăm sóc 24/7 đầu tiên tại Việt Nam. Chúng tôi là Công Ty TNHH Dandelion Việt Nam có địa chỉ trụ sở tại Tầng 05, Tòa nhà Pax Sky, 123 Nguyễn Đình Chiểu, phường Võ Thị Sáu, Quận 3, Thành phố Hồ Chí Minh, Việt Nam theo Giấy chứng nhận đăng ký doanh nghiệp số 0317251228 do Sở Kế hoạch và Đầu tư Thành phố Hồ Chí Minh cấp ngày 15/04/2022 (sau đây gọi tắt là “Chúng tôi”).`,
    decs2: `2. Bằng việc đăng ký tài khoản sử dụng dịch vụ của Droppii, Người dùng đã chấp nhận và đồng ý tuân thủ với các điều khoản sử dụng, các chính sách được đề cập hoặc những thông báo gửi (gọi chung là “Điều khoản và chính sách”). Chúng tôi có quyền thay đổi, chỉnh sửa, thêm hoặc lược bỏ bất kỳ phần nào trong Điều khoản sử dụng này, vào bất cứ lúc nào. Khi tiếp tục sử dụng sau khi các thay đổi về Điều khoản này được đăng tải, có nghĩa là người dùng đã chấp nhận với những thay đổi đó.`,
    decs3: `3. Vui lòng kiểm tra thường xuyên để cập nhật những thay đổi của Nền tảng Droppii. Chúng tôi không chịu trách nhiệm về chất lượng đường truyền Internet ảnh hưởng đến tốc độ truy cập của người dùng vào Nền tảng.`,
    decs4: `4. Người dùng có thể tự do lựa chọn, xem tất cả thông tin và sản phẩm có trong Droppii. Tuy nhiên, nếu không được Chúng tôi cho phép bằng văn bản, vui lòng KHÔNG sử dụng bất kỳ phần nào của Nền tảng Droppii này với mục đích thương mại.`,
  },
  {
    slug: "chinh-sach-doi-tra",
    title: "Chính sách đổi trả",
    content: `A.GIỚI THIỆU`,
    subTitle: `I.Các điều khoản chung`,
    decs1: `Chào mừng đến với Droppii – Kênh thương mại điện tử kết hợp tư vấn, chăm sóc 24/7 đầu tiên tại Việt Nam. Chúng tôi là Công Ty TNHH Dandelion Việt Nam có địa chỉ trụ sở tại Tầng 05, Tòa nhà Pax Sky, 123 Nguyễn Đình Chiểu, phường Võ Thị Sáu, Quận 3, Thành phố Hồ Chí Minh, Việt Nam theo Giấy chứng nhận đăng ký doanh nghiệp số 0317251228 do Sở Kế hoạch và Đầu tư Thành phố Hồ Chí Minh cấp ngày 15/04/2022 (sau đây gọi tắt là “Chúng tôi”).
        Bằng việc đăng ký tài khoản sử dụng dịch vụ của Droppii, Người dùng đã chấp nhận và đồng ý tuân thủ với các điều khoản sử dụng, các chính sách được đề cập hoặc những thông báo gửi (gọi chung là “Điều khoản và chính sách”). Chúng tôi có quyền thay đổi, chỉnh sửa, thêm hoặc lược bỏ bất kỳ phần nào trong Điều khoản sử dụng này, vào bất cứ lúc nào. Khi tiếp tục sử dụng sau khi các thay đổi về Điều khoản này được đăng tải, có nghĩa là người dùng đã chấp nhận với những thay đổi đó.
        Vui lòng kiểm tra thường xuyên để cập nhật những thay đổi của Nền tảng Droppii. Chúng tôi không chịu trách nhiệm về chất lượng đường truyền Internet ảnh hưởng đến tốc độ truy cập của người dùng vào Nền tảng.
        Người dùng có thể tự do lựa chọn, xem tất cả thông tin và sản phẩm có trong Droppii. Tuy nhiên, nếu không được Chúng tôi cho phép bằng văn bản, vui lòng KHÔNG sử dụng bất kỳ phần nào của Nền tảng Droppii này với mục đích thương mại.`,
    decs2: "",
    decs3: "",
    decs4: "",
  },
];

export const HEADER_HEIGHT = "50px";

export const Env = {
  BackendUrl: import.meta.env.VITE_API_URL,
};

export const OrderPrefix = "FFE";

export const MockPhone = "0969468213";
export const MockZaloId = "loginWithZaloId";

export const OrderStatus = {
  Pending: 1,
  Paid: 2,
  Shipping: 3,
  Cancelled: 4,
  Success: 5,
  OutOfStock: 6,
};

// export const OrderStatusText = {
//   [OrderStatus.Pending]: "Chờ xác nhận",
//   [OrderStatus.Paid]: "Đã thanh toán",
//   [OrderStatus.Shipping]: "Đang giao hàng",
//   [OrderStatus.Cancelled]: "Đã hủy",
//   [OrderStatus.Success]: "Hoàn thành",
//   [OrderStatus.OutOfStock]: "Hết hàng",
// };

export const PaymentMethods = {
  Cod: 1,
  VNPay: 2,
  Bank: 3,
  ZaloPay: 4,
};

export const MethodText = {
  [PaymentMethods.Cod]: "Thanh toán khi nhận hàng",
  [PaymentMethods.VNPay]: "VNPay",
  [PaymentMethods.Bank]: "Chuyển khoản ngân hàng",
  [PaymentMethods.ZaloPay]: "Thanh toán qua ZaloPay",
};

export const MethodValue = {
  [PaymentMethods.Cod]: "COD",
  [PaymentMethods.VNPay]: "VNPAY",
  [PaymentMethods.Bank]: "BANK",
  [PaymentMethods.ZaloPay]: "ZALOPAY",
};

export const DefaultPayment = Platform === "web" ? PaymentMethods.Bank : PaymentMethods.Cod;

export const PaymentMethodText = [
  {
    value: PaymentMethods.ZaloPay,
    label: "Thanh toán qua ZaloPay",
    iconPath: Icon.zaloPay,
    active: PaymentMethods.ZaloPay === DefaultPayment,
  },
  {
    value: PaymentMethods.Bank,
    label: "Chuyển khoản ngân hàng",
    iconPath: Icon.transfer,
    active: PaymentMethods.Bank === DefaultPayment,
  },
  {
    value: PaymentMethods.Cod,
    label: "Thanh toán khi nhận hàng",
    iconPath: Icon.banker,
    active: PaymentMethods.Cod === DefaultPayment,
  },
];

export const ERROR_MESSAGE = {
  "Product not found": "Không tìm thấy sản phẩm, vui lòng xoá giỏ hàng và thử lại!",
  "Product price mismatch":
    "Giá sản phẩm chưa đúng, vui lòng xoá giỏ hàng, tải lại trang và thử lại!",
  "Final price mismatch": "Có lỗi xảy ra, vui lòng xoá giỏ hàng, tải lại trang và thử lại!",
  "The quantity of vouchers has been depleted":
    "Voucher đã hết lượt sử dụng, vui lòng kiểm tra lại!",
  "Insufficient stock": "Sản phẩm không còn đủ hàng, vui lòng kiểm tra lại!",
  "Phone is already taken": "Số điện thoại đã được sử dụng",
  "Refer code is invalid": "Mã giới thiệu không hợp lệ",
  "This voucher has expired": "Voucher đã quá hạn sử dụng",
  "You don't have enough points": "Bạn không có đủ điểm để đổi lấy voucher này",
  "Voucher existed": "Voucher đã tồn tại trong kho của bạn",
  "You don't have permission to call this api": "Bạn chưa cấp quyền lấy số điện thoại.",
  "User deny request permission!":
    "Bạn đã từ chối cấp quyền cho app lấy thông tin. Vui lòng thử lại!",
};
export const SomethingWrong = "Có lỗi xảy ra, vui lòng thử lại sau!";

export const DISCOUNT_TYPES = {
  discountPrice: "discountPrice",
  discountPercent: "discountPercent",
  freeship: "freeship",
};

export const PhoneRegex = /^0[35789]\d{8}$/;
export const Phone84Regex = /^(?:\+84|0)[35789]\d{8}$/;
export const DefaultFilter = {
  id: 0,
  name: "Tất cả",
};

export const Level = {
  NewUser: 0,
  GoldenMember: 1,
  PlatinumMember: 2,
  DiamondMember: 3,
};

export const LevelName = {
  [Level.NewUser]: "Thành viên mới",
  [Level.GoldenMember]: "Thành Viên Vàng",
  [Level.PlatinumMember]: "Thành Viên Bạch Kim",
  [Level.DiamondMember]: "Thành Viên Kim cương",
};

export const LevelIcon = {
  [Level.NewUser]: Icon.verify,
  [Level.GoldenMember]: Icon.rank_golden,
  [Level.PlatinumMember]: Icon.rank_silver,
  [Level.DiamondMember]: Icon.rank_diamond,
};

export const LevelPolicy = (appName?: string) => ({
  [Level.NewUser]: {
    condition: `Khách hàng sử dụng ứng dụng ${appName}`,
    gift: "Chưa có khuyến mại tại hạng này.",
  },
  [Level.GoldenMember]: {
    condition: `Khách hàng có phát sinh đơn hàng bất kỳ tại ${appName}`,
    gift: "Tích điểm 3% trên mỗi đơn hàng.",
  },
  [Level.PlatinumMember]: {
    condition: "Tổng giá trị mua hàng từ 10 đến 30 triệu đồng trở lên (tính theo chi tiêu lũy kế).",
    gift: "Tích điểm 5% trên mỗi đơn hàng.",
  },
  [Level.DiamondMember]: {
    condition: "Tổng giá trị mua hàng từ 30 triệu đồng trở lên (tính theo chi tiêu lũy kế).",
    gift: `Tích điểm 8% trên mỗi đơn hàng.\nTham gia các chương trình khách hàng thân thiết và sự kiện đặc biệt của công ty.\nNhận thông tin về các chương trình khuyến mãi đặc biệt và sản phẩm mới trước khi công bố.`,
  },
});

export const NO_BOTTOM_NAVIGATION_PAGES = [
  Router.cartPayment,
  Router.productDetail,
  Router.bankTransfer.index,
  Router.login,
  Router.register,
  Router.otp,
  Router.productDetail,
];

export const AppLink = `https://zalo.me/app/link/zapps/${import.meta.env.VITE_ZMP_APP_ID}`;

export const ReviewerNumbers = ["**********", "**********", "**********"];

export type IHomeTab = {
  id: string;
  icon: string;
  title: string;
  subTitle?: string;
  link?: string;
};
export const HomeTabType = {
  Gift: "gift",
  Production: "production",
  Store: "store",
  Cart: "cart",
  Membership: "membership",
  AccumulatePoint: "point",
  OrderHistory: "order",
  Contact: "contact",
  Intro: "intro",
};
const HomeTabText = {
  [HomeTabType.Gift]: "Ưu đãi",
  [HomeTabType.Production]: "Sản phẩm",
  [HomeTabType.Store]: "Cửa hàng",
  [HomeTabType.Cart]: "Giỏ hàng",
  [HomeTabType.Membership]: "Thành viên",
  [HomeTabType.AccumulatePoint]: "Tích điểm",
  [HomeTabType.OrderHistory]: "Đơn hàng",
  [HomeTabType.Contact]: "Liên hệ",
  [HomeTabType.Intro]: "Giới thiệu",
};
export const HomeTabs: Record<string, Array<IHomeTab>> = {
  First: [
    {
      id: HomeTabType.AccumulatePoint,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.AccumulatePoint],
    },
    {
      id: HomeTabType.Production,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Production],
    },

    {
      id: HomeTabType.Cart,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Cart],
    },
    {
      id: HomeTabType.Gift,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Gift],
    },
    {
      id: HomeTabType.OrderHistory,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.OrderHistory],
    },
    {
      id: HomeTabType.Intro,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Intro],
    },
    {
      id: HomeTabType.Contact,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Contact],
    },
  ],
  Second: [
    {
      id: HomeTabType.Gift,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Gift],
    },
    {
      id: HomeTabType.Store,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Store],
    },
    {
      id: HomeTabType.Membership,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.Membership],
    },
    {
      id: HomeTabType.AccumulatePoint,
      icon: Icon.icon_demo,
      title: HomeTabText[HomeTabType.AccumulatePoint],
    },
  ],
};

export const HomeTabActions = [
  {
    id: "1",
    title: "Gọi món tại bàn",
    subTitle: "Tiện lợi nhanh chóng",
    link: Router.menu,
    statusDelivery: "InShop",
    icon: MenuActionProduct,
  },
  {
    id: "2",
    title: "Giao hàng tận nơi",
    subTitle: "Giao nhanh 30 phút",
    link: Router.menu,
    statusDelivery: "ExpressDelivery",
    icon: MenuActionShip,
  },
];

// export const paymentInfo: Array<IHomeTab> = [
//   {
//     id: 1,
//     icon: Icon.icon_payment_store,
//     title: "Tại cửa hàng",
//   },
//   {
//     id: 2,
//     icon: Icon.icon_payment_transfer,
//     title: "Chuyển khoản",
//   },
//   {
//     id: 3,
//     icon: Icon.icon_payment_cod,
//     title: "COD",
//   },
//   {
//     id: 4,
//     icon: Icon.icon_payment_visa,
//     title: "VISA/Master",
//   },
// ];

export type IAccountItem = {
  id: number;
  icon: () => React.JSX.Element;
  title: string;
  subTitle?: string;
  isFirst?: boolean;
  quantity?: number;
};
export const AccountItemType = {
  Payment: 1,
  Address: 2,
  Voucher: 3,
  AppIcon: 4,
  Collab: 5,
  ReferCode: 6,
  UserInfo: 7,
  Policy: 8,
  Support: 9,
  Branch: 10,
};
const AccountItemText = {
  [AccountItemType.Payment]: {
    title: "Thông tin thanh toán",
    subTitle: "Tài khoản nhận tiền hoa hồng",
  },
  [AccountItemType.Address]: {
    title: "Sổ địa chỉ",
    subTitle: "Địa chỉ nhận hàng",
  },
  [AccountItemType.Voucher]: {
    title: "Kho Voucher",
    subTitle: "Các voucher khuyến mại",
  },
  [AccountItemType.AppIcon]: {
    title: "Tạo icon app trên màn hình chính",
    subTitle: "Dễ dàng truy cập miniapp hơn",
  },
  [AccountItemType.Collab]: {
    title: "Đội nhóm của tôi",
    subTitle: "Danh sách thành viên đã giới thiêụ",
  },
  [AccountItemType.ReferCode]: {
    title: "Mã giới thiệu",
    subTitle: "Cập nhật mã giới thiệu",
  },
  [AccountItemType.UserInfo]: {
    title: "Thông tin tài khoản",
    subTitle: "Cập nhật thông tin định danh",
  },
  [AccountItemType.Policy]: {
    title: "Về chúng tôi",
    subTitle: "Cập nhật chính sách, điều khoản và giới thiệu về chúng tôi",
  },
  [AccountItemType.Support]: {
    title: "Hỗ trợ và hỏi đáp",
    subTitle: "Gặp trực tiếp đội ngũ tư vấn viên",
  },
  [AccountItemType.Branch]: {
    title: "Danh sách cửa hàng",
    subTitle: "Vị trí và thông tin cửa hàng",
  },
};
export const AccountItems: Record<string, Array<IAccountItem>> = {
  First: [
    // {
    //   id: AccountItemType.Payment,
    //   icon: MoneySVG,
    //   title: AccountItemText[AccountItemType.Payment].title,
    //   subTitle: AccountItemText[AccountItemType.Payment].subTitle,
    // },
    {
      id: AccountItemType.Address,
      icon: Address,
      title: AccountItemText[AccountItemType.Address].title,
      subTitle: AccountItemText[AccountItemType.Address].subTitle,
      isFirst: true,
    },
    {
      id: AccountItemType.Voucher,
      icon: Voucher,
      title: AccountItemText[AccountItemType.Voucher].title,
      subTitle: AccountItemText[AccountItemType.Voucher].subTitle,
      isFirst: true,
    },
    // {
    //   id: AccountItemType.Collab,
    //   icon: Agency,
    //   title: AccountItemText[AccountItemType.Collab].title,
    //   subTitle: AccountItemText[AccountItemType.Collab].subTitle,
    // },
    // {
    //   id: AccountItemType.ReferCode,
    //   icon: Agency,
    //   title: AccountItemText[AccountItemType.ReferCode].title,
    //   subTitle: AccountItemText[AccountItemType.ReferCode].subTitle,
    // },
  ],
  Second: [
    {
      id: AccountItemType.UserInfo,
      icon: Setting,
      title: AccountItemText[AccountItemType.UserInfo].title,
      subTitle: AccountItemText[AccountItemType.UserInfo].subTitle,
    },
    {
      id: AccountItemType.Branch,
      icon: Map,
      title: AccountItemText[AccountItemType.Branch].title,
      subTitle: AccountItemText[AccountItemType.Branch].subTitle,
    },
    {
      id: AccountItemType.AppIcon,
      icon: AppIcon,
      title: AccountItemText[AccountItemType.AppIcon].title,
      subTitle: AccountItemText[AccountItemType.AppIcon].subTitle,
    },
    {
      id: AccountItemType.Policy,
      icon: Policy,
      title: AccountItemText[AccountItemType.Policy].title,
      subTitle: AccountItemText[AccountItemType.Policy].subTitle,
    },
    {
      id: AccountItemType.Support,
      icon: Question,
      title: AccountItemText[AccountItemType.Support].title,
      subTitle: AccountItemText[AccountItemType.Support].subTitle,
    },
  ],
};

export const MyOrderItemType = {
  WaitingConfirm: 1,
  WaitingForDelivery: 2,
  Delivering: 3,
  Review: 4,
};
const MyOrderItemText = {
  [MyOrderItemType.WaitingConfirm]: {
    title: "Chờ xác nhận",
  },
  [MyOrderItemType.WaitingForDelivery]: {
    title: "Chờ giao hàng",
  },
  [MyOrderItemType.Delivering]: {
    title: "Đang giao hàng",
  },
  [MyOrderItemType.Review]: {
    title: "Đánh giá",
  },
};

export const MyOrderItemItems: Array<IAccountItem> = [
  {
    id: MyOrderItemType.WaitingConfirm,
    icon: Wallet,
    title: MyOrderItemText[MyOrderItemType.WaitingConfirm].title,
  },
  {
    id: MyOrderItemType.WaitingForDelivery,
    icon: Box,
    title: MyOrderItemText[MyOrderItemType.WaitingForDelivery].title,
  },
  {
    id: MyOrderItemType.Delivering,
    icon: Car,
    title: MyOrderItemText[MyOrderItemType.Delivering].title,
  },
  {
    id: MyOrderItemType.Review,
    icon: Star,
    title: MyOrderItemText[MyOrderItemType.Review].title,
  },
];

export const MyInfoActionItemType = {
  ChangePass: 1,
};
const MyInfoActionItemText = {
  [MyInfoActionItemType.ChangePass]: {
    title: "Lấy mật khẩu",
    subTitle: "Lấy mật khẩu cho tài khoản của bạn để đăng nhập vào nền tảng web của chúng tôi",
  },
};
export const MyInfoActionItems: Array<IAccountItem> = [
  {
    id: MyInfoActionItemType.ChangePass,
    icon: Setting,
    title: MyInfoActionItemText[MyInfoActionItemType.ChangePass].title,
    subTitle: MyInfoActionItemText[MyInfoActionItemType.ChangePass].subTitle,
  },
];

export const VoucherType = {
  Discount: "Promotion",
  Transport: "Transport",
  Custom: "Custom",
};

export const RewardType = {
  Point: "Point",
  Product: "Product",
};

export const ButtonHandleVoucherType = {
  Save: "Lưu",
  Exchange: "Đổi voucher",
  Use: "Dùng ngay",
};

export const DiscountType = {
  Percent: "Percent",
};

export const ShippingDiscountType = {
  Fixed: "Fixed",
  Free: "Free",
};
export const ReleaseType = {
  Free: "Free",
  ExchangePoints: "ExchangePoints",
};
export const VoucherText = {
  [VoucherType.Discount]: "Mã giảm giá",
  [VoucherType.Transport]: "Mã freeship",
};

export const AppCategoryValue = {
  FB: "FB",
  Retail: "Retail",
};
export const NewPosition = {
  BannerHome1: "BannerHome1",
  BannerProduct: "BannerProduct",
  BannerAffiliate: "BannerAffiliate",
  BannerAccount: "BannerAccount",
  Other: "Other",
};

export const OrderStatusType = {
  All: "All",
  Pending: "Pending",
  Verified: "Verified",
  Paid: "Paid",
  WaitingForDelivery: "WaitingForDelivery",
  Delivering: "Delivering",
  Success: "Success",
  Failed: "Failed",
  Refund: "Refund",
};

export const TransportStatus = {
  Created: "Created",
  Verified: "Verified",
  WaitingForDeliver: "WaitingForDeliver",
  Delivered: "Delivered",
  Transporting: "Transporting",
  Delivering: "Delivering",
  Success: "Success",
  Waiting: "Waiting",
  Refunding: "Refunding",
  Refunded: "Refunded",
  Cancel: "Cancel",
};

export const StatusPay = {
  NotPaid: "NotPaid",
  Paid: "Paid",
  Refund: "Refund",
};

export const OrderStatusText = {
  [OrderStatusType.All]: "Tất cả",
  [OrderStatusType.Pending]: "Chờ xác nhận",
  [OrderStatusType.WaitingForDelivery]: "Chờ giao hàng",
  [OrderStatusType.Delivering]: "Đang giao hàng",
  [OrderStatusType.Success]: "Thành công",
  [OrderStatusType.Failed]: "Đã hủy",
  [OrderStatusType.Refund]: "Hoàn lại",
};

export const StatusDelivery = {
  InShop: "InShop",
  InHome: "ExpressDelivery",
};

export const TransportService = {
  LCOD: "LCOD",
  NCOD: "NCOD",
  VHT: "VHT",
  PHS: "PHS",
  PTN: "PTN",
  VCN: "VCN",
  VTK: "VTK",
  AHAMOVE: "AHAMOVE",
};

export const TypePay = {
  COD: "COD",
  VNPAY: "Vnpay",
  MOMO: "Momo",
  ZALO: "Zalo",
  TRANSFER: "Transfer",
  OTHER: "Other",
};

export const TypePayText = {
  [TypePay.COD.toLocaleLowerCase()]: "Thanh toán khi nhận hàng",
  [TypePay.VNPAY.toLocaleLowerCase()]: "Thanh toán qua VNPAY",
  [TypePay.MOMO.toLocaleLowerCase()]: "Thanh toán qua Momo",
  [TypePay.ZALO.toLocaleLowerCase()]: "Thanh toán qua Zalo",
  [TypePay.TRANSFER.toLocaleLowerCase()]: "Thanh toán qua chuyển khoản",
};

export const PaymentMethodList = [
  {
    name: "Thanh toán khi nhận hàng",
    image: "",
    uid: TypePay.COD,
    disabled: false,
  },
  {
    name: "VNPAY",
    image: "",
    uid: TypePay.VNPAY,
    disabled: false,
  },
];

export const PAGE_SIZE = 10;
