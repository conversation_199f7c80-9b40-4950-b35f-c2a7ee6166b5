import { useConfigApp } from "@/hooks/useConfigApp";
import { createTheme } from "@mui/material";

export const defaultTheme = createTheme({
  typography: {
    allVariants: {
      fontSize: 14,
    },
    button: {
      textTransform: "none",
      fontWeight: 700,
    },
    fontFamily: "'Google Sans', 'Roboto', 'Helvetica Neue', sans-serif;", // Đặt font chữ tùy chỉnh
  },
  palette: {
    primary: {
      main: "#90A541",
    },
    secondary: {
      main: "#AF4C23",
    },
    error: {
      main: "#dc1f18",
    },
    warning: {
      main: "#FFB42A",
    },
    info: {
      main: "#555555",
    },
    success: {
      main: "#29BB9C",
    },
    text: {
      primary: "#333333",
      secondary: "#454545",
    },
    divider: "#E0E0E0",
    background: {
      default: "#F5F5F5",
      paper: "#F5F5F5",
    },
  },
});

export const useAppTheme = () => {
  const { color } = useConfigApp();

  return createTheme({
    ...defaultTheme,
    palette: {
      ...defaultTheme.palette,
      primary: {
        main: color.primary,
      },
      secondary: {
        main: color.secondary,
      },
    },
  });
};

export const COLOR = {
  linear_gradient: "linear-gradient(180deg, #FFFFFF 0%, #FFECED 10%, #FBFBFB 90%, #FFFFFF 100%)",
  text: {
    title_primary: "#454545",

    color1: "#969696",
    color2: "#929292",

    product_item: "#333333",
    product_price: "#B8B8B8",
    product_rate: "#747474",
    product_sold: "#B5B5B5",
    product_user: "#343434",
    product_comment: "#1D1D1D",

    seeall: "#878787",

    voucher_code: "#1531AD",
    voucher_info: "#828282",

    divider1: "#EEEEEE",
    white: "#FFFFFF",
  },
  bg: {
    primary: "#F5F5F5",
    product_discount: "#E14337",
    product_item: "#EFEFEF",
    banner: "#EEEEEE",
    new_item: "#F6F6F6",
    voucher: "#F6F6F6",
    product_discount2: "rgba(188, 188, 188, 0.2)",
  },
};

export const COLORS = {
  primary: "#90A541",
  second: "#219346",
  text_second: "#929292",
  primary1: "#0368F7",
  primary2: "#02DAFE",
  primary3: "#e79686",
  accent1: "#EE4D2D",
  accent2: "#FEF6E9",
  accent3: "#FFF9ED",
  accent4: "#EDF7FE",
  accent5: "#143374",
  neutral1: "#1D1D1D",
  neutral2: "#454545",
  neutral3: "#646464",
  neutral4: "#787878",
  neutral5: "#8C8C8C",
  neutral6: "#A0A0A0",
  neutral7: "#B3B3B3",
  neutral8: "#C6C6C6",
  neutral9: "#DCDCDC",
  neutral10: "#F9F9F9",
  neutral11: "#D9D9D9",
  neutral12: "#A6A6A6",
  neutral13: "#777777",
  neutral14: "#929292",
  neutral15: "#5E5E5E",
  neutral16: "#686868",
  neutral17: "#FCFCFC",
  white: "#FFFFFF",
  black: "#000000",
  bgColor: {
    primary: "#FFFFFF",
    secondary: "#F6F6F6",
    thirty: "#F6F6F6",
    fourth: "#FFFEF1",
  },
  textColor: {
    primary: "#454545",
    link: "#333333",
  },
};

export const commonStyle: Record<string, React.CSSProperties> = {
  shadowBorder: {
    borderRadius: "5px",
    backgroundColor: "#ffffff",
    boxShadow: "0px 0px 10px 0px #0000001A",
  },
  headline8: {
    fontSize: 8,
    fontWeight: 700,
  },
  headline12: {
    fontSize: 12,
    fontWeight: 700,
  },
  headline13: {
    fontSize: 13,
    fontWeight: 700,
  },
  headline14: {
    fontSize: 14,
    fontWeight: 700,
  },
  headline15: {
    fontSize: 15,
    fontWeight: 700,
  },
  headline16: {
    fontSize: 16,
    fontWeight: 700,
  },
  headline17: {
    fontSize: 17,
    fontWeight: 700,
  },
  headline18: {
    fontSize: 18,
    fontWeight: 700,
  },
  headline20: {
    fontSize: 20,
    fontWeight: 700,
  },
  headline28: {
    fontSize: 28,
    fontWeight: 700,
  },
};
