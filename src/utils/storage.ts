import { nativeStorage } from "zmp-sdk/apis";
import { showUpdateZaloPopup } from "../components/AppProvider";
import { Platform } from "../config";
import { parseSafe } from "./common";
import { StorageKeys } from "../constants/storageKeys";

const HIDE_AD_KEY = "hide_ad";

export const setItem = (key: string, value: any) => {
  return Platform === "web" ? setItemWeb(key, value) : setItemZalo(key, value);
};

export const getItem = async (key: string) => {
  return Platform === "web" ? getItemWeb(key) : getItemZalo(key);
};

export const clearItem = async (key?: string) => {
  return Platform === "web" ? clearItemWeb(key) : clearItemZalo(key);
};

///////////////
const setItemZalo = (key: string, value: any) => {
  return nativeStorage.setItem(key, value);
};

const getItemZalo = async (key: string) => {
  try {
    const res = await nativeStorage.getItem(key);
    return typeof res === "string" ? res : "";
  } catch (err: any) {
    console.log("getItemZalo error:", err);
    if (err?.code === -1404 && showUpdateZaloPopup) {
      showUpdateZaloPopup();
    }
    return "";
  }
};

const clearItemZalo = async (key?: string) => {
  if (key) {
    nativeStorage.removeItem(key);
  } else {
    const hideAd = nativeStorage.getItem(HIDE_AD_KEY);
    nativeStorage.clear();
    if (hideAd) {
      nativeStorage.setItem(HIDE_AD_KEY, hideAd);
    }
  }
};

////////////////
const setItemWeb = (key: string, value: any) => {
  return localStorage.setItem(key, typeof value === "object" ? JSON.stringify(value) : value);
};

const getItemWeb = async (key: string) => {
  return parseSafe(localStorage.getItem(key));
};

const clearItemWeb = async (key?: string) => {
  if (key) {
    localStorage.removeItem(key);
  } else {
    const keys = Object.keys(localStorage);
    keys.forEach((key) => {
      if (key !== HIDE_AD_KEY && key !== StorageKeys.RefCode) {
        localStorage.removeItem(key);
      }
    });
  }
};
