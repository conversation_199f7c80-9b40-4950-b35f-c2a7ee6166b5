import { followOA } from "zmp-sdk/apis";
import { showToast } from "./common";
import { RootState } from "../redux/store";
import { useSelector } from "react-redux";

export const handleFollowOA = async (
  oaId: string,
  successCallback?: () => void
) => {
  try {
    await followOA({
      id: oaId,
      success: successCallback,
      fail: (error) => {
        if (error.code !== -201) {
          //code: -201 - User deny process action!
          showToast({
            content:
              error.message || "Thao tác không thành công, vui lòng thử lại",
            type: "error",
          });
        }
      },
    });
  } catch (error) {
    showToast({
      content: "Thao tác không thành công, vui lòng thử lại",
      type: "error",
    });
  }
};
