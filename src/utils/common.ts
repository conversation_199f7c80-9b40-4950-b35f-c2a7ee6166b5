import toast from "react-hot-toast";
import { AppEnv, Platform } from "../config";
import { AppLink, Env, ERROR_MESSAGE, SomethingWrong } from "../constants/Const";
import styles from "../css/styles.module.css";
import { request } from "./request";

export function numberWithCommas(x: number) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

/**
 * @description this function was based on react-host-toast
 * if u need more props => add on to the props of this function
 * with the optional variables
 */
export function showToast({
  content,
  type,
  duration,
}: {
  content: string;
  type?: "success" | "error" | "loading" | "blank" | "custom";
  duration?: number;
}) {
  toast(content, {
    // @ts-ignore
    type: type ?? "blank",
    duration: duration ?? 1500,
    className: styles.toast,
  });
}

export const parseSafe = (value: any) => {
  try {
    return JSON.parse(value);
  } catch (e) {
    return value;
  }
};

export function isMobile() {
  let check = false;
  (function (a) {
    if (
      /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(
        a
      ) ||
      /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
        a.substr(0, 4)
      )
    )
      check = true;
    // @ts-ignore
  })(navigator.userAgent || navigator.vendor || window.opera);
  return check;
}

export const copy = (s: string, title?: string) => {
  if (Platform === "web") {
    navigator.clipboard.writeText(s);
  } else {
    const tempInput = document.createElement("textarea");
    tempInput.value = s;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);
  }
  showToast({
    content: `Đã sao chép${title ? ` ${title}` : ""}!`,
    type: "success",
  });
};

export const getReferLink = (code?: string) => {
  if (Platform === "zalo") {
    return AppEnv === "production"
      ? `${AppLink}?targetType=REFER&refCode=${code}`
      : `${AppLink}?env=TESTING&targetType=REFER&refCode=${code}`;
  } else {
    return `${window.location.origin}/register?referCode=${code}`;
  }
};

export const mapError = (message: string) => {
  return ERROR_MESSAGE[message] || SomethingWrong;
};

export const trackError = (name: string, data: any) => {
  request("post", "/api/trackings", {
    data: {
      data,
      name,
      type: "error",
    },
  });
};

export const trackEvent = (name: string, data: any) => {
  request("post", "/api/trackings", {
    data: {
      data,
      name,
      type: "event",
    },
  });
};

export const removeMark = (alias: any) => {
  if (typeof alias === "string") {
    let str = alias;
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
    str = str.replace(/Đ/g, "D");
    // eslint-disable-next-line no-control-regex
    return str.replace(/[^\x00-\x7F]/g, "");
  }
  return "";
};

export const formatNumber = (number) => {
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

export const getThumbImage = (img) => {
  if (!img) {
    return;
  }

  return img.small || img.medium || img.url;
};

export const getImageObj = (attributes) => ({
  url: `${Env.BackendUrl}${attributes.url}`,
  small: `${Env.BackendUrl}${attributes.formats?.small?.url || attributes.url}`,
  medium: `${Env.BackendUrl}${attributes.formats?.medium?.url || attributes.url}`,
  thumbnail: `${Env.BackendUrl}${
    attributes.formats?.thumbnail?.url || attributes.formats?.small?.url || attributes.url
  }`,

  name: attributes.name,
  alternativeText: attributes.alternativeText,
  caption: attributes.caption,
  width: attributes.width,
  height: attributes.height,
  formats: attributes.formats,
  hash: attributes.hash,
  ext: attributes.ext,
  mime: attributes.mime,
  size: attributes.size,
  previewUrl: attributes.previewUrl,
  provider: attributes.provider,
  provider_metadata: attributes.provider_metadata,
  createdAt: attributes.createdAt,
  updatedAt: attributes.updatedAt,
});

export const getUrlBeImage = (url) => {
  return `${import.meta.env.VITE_API_URL}${url}`;
};

export const formatPhoneNumber = (phone) => {
  if (typeof phone !== "string" || phone.length < 10) {
    throw new Error("Phone invalid");
  }
  return phone.startsWith("0") ? "+84" + phone.slice(1) : phone;
};

export function hexWithOpacityToHex(hexColor, opacity) {
  if (hexColor.startsWith("#")) {
    hexColor = hexColor.slice(1);
  }

  const r = parseInt(hexColor.slice(0, 2), 16);
  const g = parseInt(hexColor.slice(2, 4), 16);
  const b = parseInt(hexColor.slice(4, 6), 16);

  const blendedR = Math.round(r * opacity + 255 * (1 - opacity));
  const blendedG = Math.round(g * opacity + 255 * (1 - opacity));
  const blendedB = Math.round(b * opacity + 255 * (1 - opacity));

  const toHex = (value) => value.toString(16).padStart(2, "0");
  return `#${toHex(blendedR)}${toHex(blendedG)}${toHex(blendedB)}`;
}

export const formatNumberToK = (number: number): string => {
  if (number >= 1000) {
    const value = (number / 1000).toFixed(1);
    return `${value.endsWith(".0") ? value.slice(0, -2) : value}k`;
  }
  return number.toString();
};
