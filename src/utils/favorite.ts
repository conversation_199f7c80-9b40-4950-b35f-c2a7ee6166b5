import { favoriteApp as favoriteAppZalo } from "zmp-sdk/apis";
import { Platform } from "../config";
import { showToast } from "./common";

/**
 * Thêm ứng dụng vào danh sách ưa thích của người dùng ở Zalo Mini Apps Store
 */
export const addToFavorite = async () => {
  if (Platform === "zalo") {
    try {
      await favoriteAppZalo();
      showToast({
        content: "Đã thêm ứng dụng vào danh sách yêu thích",
        type: "success"
      });
      return true;
    } catch (error) {
      console.error("Lỗi khi thêm ứng dụng vào yêu thích:", error);
      showToast({
        content: "Không thể thêm vào yêu thích. Vui lòng thử lại.",
        type: "error"
      });
      return false;
    }
  } else {
    // Xử lý cho phiên bản web (có thể hiển thị thông báo hoặc thực hiện hành động khác)
    showToast({
      content: "Tính năng này chỉ khả dụng trong Zalo Mini App",
      type: "blank"
    });
    return false;
  }
}; 