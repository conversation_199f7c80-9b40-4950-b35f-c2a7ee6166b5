import React from "react";
import { useSelector } from "react-redux";

import { RootState } from "../redux/store";
import { Box } from "@mui/material";
import { Avatar } from "zmp-ui";
import { useTheme } from "@mui/material/styles";
import { COLORS, commonStyle } from "../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

const UserCard: React.FunctionComponent = () => {
  const { user, userZalo } = useSelector((state: RootState) => state.auth);
  const appConfig = useConfigApp();

  return (
    <Box
      sx={{ display: "flex", gap: 1 }}
      alignItems="start"
      justifyContent="flex-end"
    >
      <Box style={{ ...commonStyle.headline12, color: COLORS.black }}>
        <Box>
          Xin chào,
          <Box style={{ color: appConfig.color.secondary }}>
            {userZalo?.name || user?.username}
          </Box>
        </Box>
      </Box>
      <Box
        component="section"
        display="flex"
        alignItems="center"
        sx={{
          // p: 0.5,
          borderRadius: "50%",
          background: "white",
        }}
      >
        <Avatar
          // story="default"
          // online
          style={styles.sizeAvatar}
          src={userZalo?.avatar || user?.avatarUrl}
        />
      </Box>
    </Box>
  );
};

export default UserCard;

const styles: Record<string, React.CSSProperties> = {
  sizeAvatar: {
    width: '40px',
    height: '40px'
  },
};
