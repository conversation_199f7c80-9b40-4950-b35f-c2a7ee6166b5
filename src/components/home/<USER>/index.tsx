import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import ProductListGridSliceFnB from "./ProductListGridSliceFnB";
import ProductListGridSliceRetail from "./ProductListGridSliceRetail";

const componentMap = {
  FnB: ProductListGridSliceFnB,
  Retail: ProductListGridSliceRetail,
};

export default function ProductListGridSlice(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FnB");
  const Component = componentMap[businessType] || componentMap.FnB;
  return <Component {...props} />;
}
