import { default as React, useEffect, useState } from "react";
import { Box, Button, Stack, Typography } from "@mui/material";
import { useNavigate } from "../../utils/component-util";
import { RootState } from "../../redux/store";
import { useSelector } from "react-redux";
import { Collapse } from "@mui/material";
import { useConfigApp } from "@/hooks/useConfigApp";
import { commonStyle } from "@/constants/themes";

export default function Policy({ style }) {
  const navigate = useNavigate();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);
  const [shopPolicy, setShopPolicy] = useState<any>();
  const [isOpen, setIsOpen] = useState(true);
  const appConfig = useConfigApp();

  const fetchShopPolicy = async () => {
    if (shopId) {
      const listPolicyString = shopInfo?.shopPolicy;
      const listPolicy = JSON.parse(listPolicyString);
      setShopPolicy(listPolicy);
    }
  };

  useEffect(() => {
    if (shopInfo && shopInfo.shopPolicy) {
      fetchShopPolicy();
    }
  }, [shopId, shopInfo]);

  return (
    <Box
      style={{
        background: style?.backgroundColor || appConfig?.container?.backgroundColor || "#000",
      }}
    >
      <Box
        style={{
          padding: "5px 20px",
          borderRadius: "10px",
          width: "100%",
        }}
      >
        <Typography
          style={{
            ...commonStyle.headline20,
            textAlign: "left",
            color: style?.color || appConfig?.color?.primary || "#fff",
            marginBottom: "10px",
            fontWeight: "700",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={() => setIsOpen(!isOpen)}
        >
          {style?.title}
          <svg
            width="14"
            height="9"
            viewBox="0 0 14 9"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              transform: isOpen ? "rotate(0deg)" : "rotate(-90deg)",
              transition: "transform 0.3s ease",
            }}
          >
            <path
              d="M11.992 0.94808L13.052 2.00908L7.27497 7.78808C7.1824 7.88124 7.07233 7.95516 6.95108 8.00561C6.82983 8.05606 6.6998 8.08203 6.56847 8.08203C6.43714 8.08203 6.30711 8.05606 6.18586 8.00561C6.06461 7.95516 5.95454 7.88124 5.86197 7.78808L0.0819702 2.00908L1.14197 0.94908L6.56697 6.37308L11.992 0.94808Z"
              fill={style?.color || appConfig?.color?.primary || "white"}
            />
          </svg>
        </Typography>

        <Collapse in={isOpen} timeout={300}>
          <Stack
            style={{
              flexDirection: "row",
              alignItems: "center",
              flexWrap: "wrap",
              justifyContent: "space-between",
            }}
          >
            {shopPolicy?.map((policy) => (
              <Stack
                style={{
                  justifyContent: "left",
                  width: "100%",
                  alignItems: "left",
                }}
                key={policy.id}
              >
                <Button
                  sx={{ padding: 0, marginBottom: "5px", justifyContent: "left" }}
                  onClick={() => {
                    navigate(`/profile/policy/${policy.id}`, { state: { policy } });
                  }}
                >
                  <Typography
                    style={{
                      color: style?.textColor || "#000",
                      textAlign: "left",
                      fontSize: style?.fontSize || 12,
                      lineHeight: style?.lineHeight || 1.2,
                      fontWeight: style?.fontWeight || 400,
                    }}
                  >
                    {policy.title}
                  </Typography>
                </Button>
              </Stack>
            ))}
          </Stack>
        </Collapse>
      </Box>
    </Box>
  );
}
