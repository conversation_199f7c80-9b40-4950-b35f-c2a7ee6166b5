import React from "react";
import { <PERSON>, Grid, Stack, Typography } from "@mui/material";
import { COLORS, commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useNavigate } from "@/utils/component-util";
import { useCart } from "@/hooks/useCart";

export default function MenuActionItem({ homeTabItem }: { homeTabItem: any }) {
  const appConfig = useConfigApp();
  const { cartPayment, setCartAndSave } = useCart();
  const navigate = useNavigate();
  
  return (
    <Box
      p={1}
      bgcolor={COLORS.white}
      borderRadius={2}
      onClick={() => {
        if (homeTabItem.link) {
          navigate(homeTabItem.link, { state: { statusDelivery: homeTabItem.statusDelivery } });

          setCartAndSave({
            ...cartPayment,
            statusDelivery: homeTabItem.statusDelivery,
          });
        }
      }}
      height={"100%"}
    >
      <Stack direction={"row"} spacing={2} height={"100%"}>
        <Box width={"20%"} height={"100%"} display={"flex"} alignItems={"center"}>
          {typeof homeTabItem.icon === 'function' ? (
            homeTabItem.icon()
          ) : (
            <span
              dangerouslySetInnerHTML={{
                __html: homeTabItem.icon
                  ?.replace(/\{ ?appConfig\.color\.primary ?\}/g, appConfig.color.primary)
                  ?.replace('<svg', '<svg width="36" height="36"')
              }}
            />
          )}
        </Box>
        <Box width={"80%"} height={"100%"}>
          <Stack
            direction={"column"}
            alignItems={"start"}
            justifyContent={"center"}
            height={"100%"}
          >
            <Typography fontSize={12} fontWeight={500}>
              {homeTabItem.title}
            </Typography>
            <Typography fontSize={10} color={"#999999"}>
              {homeTabItem.subTitle}
            </Typography>
          </Stack>
        </Box>
      </Stack>
    </Box>
  );
}
