import { <PERSON>, Button, Divider, <PERSON>rid, <PERSON>ack, Typography, useTheme } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { COLOR, COLORS, commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

interface Props {
  title: string;
  btnAction?: ReactNode;
  seeAll?: () => void;
  children?: ReactNode;
  overrideStyle?: React.CSSProperties;
  btnActionBottom?: string;
  divider?: boolean;
  titleOverrideStyle?: React.CSSProperties;
  titleColor?: string;
}

export default function ListItem(input: Props) {
  const theme = useTheme();
  const appConfig = useConfigApp();
  const { title, children, btnAction, seeAll, btnActionBottom, divider, titleOverrideStyle, titleColor } =
    input;

  return (
    <Box style={styles.container} bgcolor={"#ffffff00"} border={"none"}>
      {title && (
        <Box sx={styles.productionTitleContainer}>
          <Typography
           
            color={appConfig.color.primary || COLOR.text.title_primary}
           
            style={{ ...commonStyle.headline20, ...titleOverrideStyle }}
          
          >
            {title}
          </Typography>
          {btnAction}
        </Box>
      )}
      <Grid container spacing={2}>
        {children}
      </Grid>
      {btnActionBottom && (
        <Box marginBlock={2}>
          <Button
            variant="contained"
            style={{
              ...styles.seeMoreBtnFooter,
              color: COLOR.text.white,
              backgroundColor: appConfig.color.primary,
            }}
            fullWidth
            onClick={seeAll}
          >
            {btnActionBottom}
          </Button>
        </Box>
      )}
      {divider && <Divider style={{ color: COLOR.text.divider1 }} />}
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    position: "relative",
  },
  productionTitleContainer: {
    display: "flex",
    paddingBottom: 1,
    marginTop: 0,
    justifyContent: "space-between",
    alignItems: "center",
  },
  homeTabContainerTop: {
    display: "flex",
    paddingTop: 3,
    alignItems: "flex-start",
  },
  btnContainerTop: {
    width: "calc(calc(100vw - 32px) / 4)",
    padding: 0,
  },
  seeMoreBtnFooter: {
    height: 50,
    fontSize: 17,
    boxShadow: "none",
  },
};
