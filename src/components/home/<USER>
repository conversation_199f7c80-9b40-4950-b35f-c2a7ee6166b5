import React from "react";
import { Grid } from "@mui/material";
import { IHomeTab } from "@/constants/Const";
import { useConfigApp } from "@/hooks/useConfigApp";
import MenuActionItem from "./MenuActionItem";

export default function MenuAction({ homeTabs }: { homeTabs: any[] }) {
  const appConfig = useConfigApp();
  const home = appConfig.home as any;

  const menuAction = home
    .filter((item) => item.type === "MenuAction" && Array.isArray(item.actions))
    .reduce((acc, item) => acc.concat(item.actions || []), [] as string[]);

  const displayItems = menuAction.length > 0 ? menuAction : homeTabs;

  return (
    <Grid container spacing={2}>
      {displayItems?.map((item, index) => (
        <Grid key={index} item xs={6} style={{ height: "auto" }}>
          <MenuActionItem key={index} homeTabItem={item} />
        </Grid>
      ))}
    </Grid>
  );
}
