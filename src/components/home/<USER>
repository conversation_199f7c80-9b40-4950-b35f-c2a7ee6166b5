import React, { ReactNode } from "react";
import Slider from "react-slick";
import { IProduct } from "../../types/product";
import { Box, Typography } from "@mui/material";
import { COLORS, commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

interface Props {
  title?: string;
  className?: string;
  titleStyle?: object;
  btnAction?: ReactNode;
  seeAll?: () => void;
  children?: ReactNode;
  overrideStyle?: React.CSSProperties;
  sliceConfig: any;
}

export default function ListSlider(input: Props) {
  const { title, children, btnAction, seeAll, titleStyle, className } = input;
  const { color } = useConfigApp();

  return (
    <Box style={styles.container} border={"none"} className={className}>
      {title && (
        <Box sx={styles.productionTitleContainer}>
          <Typography style={{ ...commonStyle.headline20, color: color.primary, ...titleStyle }}>
            {title}
          </Typography>
          {btnAction}
        </Box>
      )}
      <Slider {...input.sliceConfig} style={{ ...input.overrideStyle }}>
        {children}
      </Slider>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    position: "relative",
  },
  productionTitleContainer: {
    display: "flex",
    paddingBottom: 1,
    marginTop: 0,
    justifyContent: "space-between",
    alignItems: "center",
  },
  homeTabContainerTop: {
    display: "flex",
    paddingTop: 3,
    alignItems: "flex-start",
  },
  btnContainerTop: {
    width: "calc(calc(100vw - 32px) / 4)",
    padding: 0,
  },
  seeMoreBtnFooter: {
    height: 50,
  },
};
