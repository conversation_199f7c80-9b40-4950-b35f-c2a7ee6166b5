import React from "react";
import { Box } from "@mui/material";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";

const ShopInfo = ({ style }) => {
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);

  if (!shopInfo.shopInfo) {
    return null;
  }

  return (
    <Box
      sx={{
        padding: "5px 20px 0 20px",
        background:
          style?.backgroundColor || shopInfo?.template?.container?.backgroundColor || "#000",
      }}
    >
      <Box
        sx={{
          background: "#EFEFEF",
          height: "1px",
          width: "100%",
        }}
      ></Box>
      <Box
        sx={{
          mt: "10px",
          "& strong, & b": {
            color: style?.textColor || "#000",
            fontSize: style?.fontSize || 12,
            lineHeight: style?.lineHeight || 1.2,
            fontWeight: style?.fontWeight || 400,
          },
          "& p": {
            color: style?.textColor || "#000",
            fontSize: style?.fontSize || 12,
            lineHeight: style?.lineHeight || 1.2,
            fontWeight: style?.fontWeight || 400,
          },
          "& a": {
            color: style?.textColor || "#000",
            fontSize: style?.fontSize || 12,
            lineHeight: style?.lineHeight || 1.2,
            fontWeight: style?.fontWeight || 400,
            textDecoration: "none",
          },
          textAlign: "justify",
        }}
        dangerouslySetInnerHTML={{
          __html: shopInfo?.shopInfo,
        }}
      ></Box>
    </Box>
  );
};

export default ShopInfo;
