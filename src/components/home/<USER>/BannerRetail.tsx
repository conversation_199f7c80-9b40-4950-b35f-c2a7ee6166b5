import { ObjectHome } from "@/redux/slices/appInfo/appInfoSlice";
import { getListArticleUserByArticleIds } from "@/redux/slices/news/newsListSlice";
import { Box } from "@mui/material";
import React, { memo, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Slider from "react-slick";
import { AppDispatch, RootState } from "../../../redux/store";
import { useNavigate } from "../../../utils/component-util";

const BannerRetail = ({
  item,
  styleBanner,
  slickSliderOptions,
  titleFromParent,
}: {
  item: ObjectHome;
  styleBanner?: object;
  slickSliderOptions?: { dots: boolean; dotsClass?: string };
  titleFromParent?: string;
}) => {
  const navigate = useNavigate();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const dispatch = useDispatch<AppDispatch>();
  const [listBanner, setListBanner] = useState<any>([]);
  const settings = {
    infinite: listBanner.length > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    arrows: false,
    dots: slickSliderOptions?.dots ?? false,
    dotsClass: `slick-dots ${slickSliderOptions?.dotsClass || ""}`,
  };

  const fetchListArticleByArticleIds = async () => {
    if (item?.type === "Banner2" || item?.type === "BannerWithBranch") {
      const data = {
        shopId: shopId,
        articleIds: item?.articleIds,
      };
      const res = await dispatch(getListArticleUserByArticleIds(data));
      if (res?.payload?.data) {
        setListBanner(res.payload.data);
      }
    }
  };
  useEffect(() => {
    fetchListArticleByArticleIds();
  }, []);

  return Array.isArray(listBanner) && listBanner?.length > 0 ? (
    <Box m={2}>
      <Slider {...settings}>
        {listBanner?.map((item, index) => (
          <Box
            key={index}
            style={{ ...styles.imageContainer, ...styleBanner, borderRadius: "10px" }}
            onClick={() => {
              navigate(`/posts/${item.articleId}`, {
                state: { title: titleFromParent, news: item },
              });
            }}
          >
            <img
              style={{
                ...styles.imageContainer,
                ...styleBanner,
                borderRadius: "10px",
              }}
              src={item?.images[0]?.link}
            />
          </Box>
        ))}
      </Slider>
    </Box>
  ) : null;
};

export default memo(BannerRetail);

const styles: Record<string, React.CSSProperties> = {
  imageContainer: {
    display: "flex",
    overflow: "hidden",
    aspectRatio: 16 / 9,
    width: "100%",
    backgroundSize: "cover",
    backgroundPosition: "center",
    objectFit: "cover",
  },
};
