import { getProductByCategory } from "@/redux/slices/product/productListSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { IProduct } from "@/types/product";
import { Box, Grid } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import BtnAction from "../../common/BtnAction";
import ArrowRightIcon from "../../icon/ArrowRightIcon";
import ProductItem from "../../products/item/ProductItemFnB";
import ProductItemSkeleton from "../../products/ProductItemSkeleton";
import NoDataView from "../../UI/NoDataView";
import ListItem from "../ListItemShow";
import ListSlider from "../SliceItemShow";

const ProductListGridSlice = ({ title, item, onNavigateToProduction }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const [listProduct, setListProduct] = useState<any>();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const handleButtonClick = () => {
    if (item.button?.type === "detail") {
      navigate("/menu", { state: { categoryId: item.categoryId } });
    } else {
      navigate("/menu");
    }
  };

  const data = {
    shopId: shopId,
    categoryId: item.categoryId,
  };
  
  const limit = item?.limit || 4;
  
  const fetchProductList = async () => {
    setIsLoading(true);
    try {
      const res = await dispatch(getProductByCategory(data));
      setListProduct(res?.payload?.data);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchProductList();
  }, []);

  const renderSkeletonLoading = (isGrid = false) => {
    if (isGrid) {
      return (
        <Grid pt={2} pl={2} container spacing={2}>
          {[...Array(4)].map((_, index) => (
            <Grid item xs={6} key={index}>
              <ProductItemSkeleton />
            </Grid>
          ))}
        </Grid>
      );
    }
    return (
      <Box sx={{ display: "flex", gap: 1.5, overflow: "hidden" }}>
        {[...Array(4)].map((_, index) => (
          <Box key={index} sx={{ minWidth: "50%", flexShrink: 0 }}>
            <ProductItemSkeleton />
          </Box>
        ))}
      </Box>
    );
  };

  return item.style?.category === "grid" ? (
    <ListItem
      title={title}
      seeAll={handleButtonClick}
      btnActionBottom={item.button?.text || "Tất cả"}
      divider={true}
    >
      {isLoading ? (
        renderSkeletonLoading(true)
      ) : Array.isArray(listProduct) && listProduct.length > 0 ? (
        listProduct.slice(0, limit).map((item: IProduct) => (
          <Grid item xs={6} key={item.itemsCode}>
            <ProductItem item={item} />
          </Grid>
        ))
      ) : (
        <NoDataView content="Không có sản phẩm nào" />
      )}
    </ListItem>
  ) : (
    <ListSlider
      title={title}
      btnAction={
        <BtnAction
          text={`Tất cả `}
          icon={<ArrowRightIcon fillColor={"#878787"} />}
          eventAction={onNavigateToProduction}
        />
      }
      seeAll={onNavigateToProduction}
      sliceConfig={{
        dots: false,
        infinite: false,
        slidesToShow: 2.2,
        autoplay: false,
        arrows: false,
      }}
      overrideStyle={{ marginInline: -8 }}
    >
      {isLoading ? (
        renderSkeletonLoading()
      ) : Array.isArray(listProduct) && listProduct.length > 0 ? (
        listProduct.map((item: IProduct) => (
          <Box key={item.itemsCode} className="box-product-item">
            <ProductItem item={item} />
          </Box>
        ))
      ) : (
        <NoDataView content="Không có sản phẩm nào" />
      )}
    </ListSlider>
  );
};

export default ProductListGridSlice;
