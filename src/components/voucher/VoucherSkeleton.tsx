import React from "react";
import { <PERSON>, Card, CardContent, Skeleton, Stack, useTheme, alpha } from "@mui/material";

interface VoucherSkeletonProps {
  count?: number;
}

const VoucherItemSkeleton: React.FC = () => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        borderRadius: 1,
        overflow: "hidden",
        mb: 2,
        backgroundColor: "white",
        boxShadow: "none",
      }}
    >
      <Box sx={{ display: "flex", minHeight: 140 }}>
        {/* Image Section Skeleton */}
        <Box
          sx={{
            width: { xs: 100, sm: 120 },
            flexShrink: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Skeleton variant="rectangular" width="80%" height="80%" sx={{ borderRadius: 1 }} />
        </Box>

        {/* Content Section Skeleton */}
        <CardContent sx={{ flex: 1, p: 2.5 }}>
          {/* Header Skeleton */}
          <Box sx={{ mb: 2 }}>
            <Skeleton
              variant="rectangular"
              width={100}
              height={24}
              sx={{ borderRadius: 1, mb: 1.5 }}
            />

            <Skeleton variant="text" width="90%" height={20} sx={{ mb: 0.5 }} />

            <Skeleton variant="rectangular" width={120} height={20} sx={{ borderRadius: 1 }} />
          </Box>

          {/* Footer Skeleton */}
          <Box sx={{ mt: "auto" }}>
            <Skeleton variant="text" width="60%" height={14} sx={{ mb: 1.5 }} />

            <Skeleton variant="rectangular" width={80} height={20} sx={{ borderRadius: 1 }} />
          </Box>
        </CardContent>
      </Box>
    </Card>
  );
};

const VoucherSkeleton: React.FC<VoucherSkeletonProps> = ({ count = 3 }) => {
  return (
    <Stack spacing={2} sx={{ px: 0, pb: 2 }}>
      {Array.from({ length: count }).map((_, index) => (
        <VoucherItemSkeleton key={index} />
      ))}
    </Stack>
  );
};

export default VoucherSkeleton;
