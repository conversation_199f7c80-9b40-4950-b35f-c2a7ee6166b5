import React from "react";
import { formatPrice } from "../../utils/formatPrice";
import { COLORS, commonStyle } from "../../constants/themes";
import { Typography } from "@mui/material";
import {
  DiscountType,
  ReleaseType,
  ShippingDiscountType,
  VoucherText,
  VoucherType,
} from "../../constants/Const";
import { useConfigApp } from "@/hooks/useConfigApp";
import { VoucherDto } from "@/redux/slices/voucher/voucherSlice";

const styles: Record<string, React.CSSProperties> = {
  titleOneLine: {
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 1,
    cursor: "pointer",
  },
};

const VoucherTypeLabel = (item: VoucherDto) => {
  const { voucherType } = item;
  const { color, bgColor, textColor } = useConfigApp();
  return (
    <Typography fontSize={12} color={color.accent}>
      {VoucherText[voucherType]}
    </Typography>
  );
};

const SelectedVoucher = (item: VoucherDto) => {
  const {
    voucherType,
    discountType,
    percentDiscount,
    minOrder,
    maxDiscount,
    releaseType,
    exchangePoints,
    moneyDiscount,
    shippingDiscountType,
    isFixedRewardPoint,
    rewardType,
    rewardPoint,
    rewardPointMax,
    rewardPointMin,
  } = item;

  switch (voucherType) {
    case VoucherType.Transport:
      switch (shippingDiscountType) {
        case ShippingDiscountType.Free:
          return "Miễn phí giao hàng";
        default:
          if (minOrder > 0) {
            return `Giảm ${formatPrice(
              moneyDiscount
            )} phí vận chuyển với đơn tối thiểu ${formatPrice(minOrder)}`;
          } else {
            return `Giảm ${formatPrice(moneyDiscount)} phí vận chuyển với đơn bất kì`;
          }
      }
    case VoucherType.Custom:
      if (isFixedRewardPoint) {
        return `Nhận ${rewardPoint} điểm`;
      } else {
        return `Nhận điểm ngẫu nhiên từ ${rewardPointMin} đến ${rewardPointMax} điểm`;
      }
    default:
      switch (discountType) {
        case DiscountType.Percent:
          return `Giảm ${percentDiscount}% tối đa ${formatPrice(
            maxDiscount
          )} với đơn tối thiểu ${formatPrice(minOrder)}`;
        default:
          return `Giảm ${formatPrice(moneyDiscount)} với đơn tối thiểu ${formatPrice(minOrder)}`;
      }
  }
};

export { VoucherTypeLabel, SelectedVoucher };
