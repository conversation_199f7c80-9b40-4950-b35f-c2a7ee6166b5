import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  FormControlLabel,
  Grid,
  Radio,
  Snackbar,
  Stack,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { SelectedVoucher, VoucherTypeLabel } from "@/components/voucher/CheckVoucherType";
import { COLOR, COLORS } from "@/constants/themes";
import { formatPrice } from "@/utils/formatPrice";
import dayjs from "dayjs";
import { Router } from "@/constants/Route";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  collectVoucher,
  getListVoucherByShop,
  GetVoucherByShopParams,
  setCurVoucher,
  VoucherDto,
  incrementUserVoucherCount,
} from "@/redux/slices/voucher/voucherSlice";
import SmallCheckIcon from "../icon/SmallCheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import VoucherAction, { BtnCategory } from "@/components/voucher/ActionVoucher";
import { ButtonHandleVoucherType, ReleaseType, VoucherType } from "@/constants/Const";
import toast from "react-hot-toast";
import { getUser } from "@/redux/slices/authen/authSlice";
import LoginPopup from "@/components/LoginPopup";

export enum VoucherItemCategory {
  MYLIST,
  MYDETAIL,
  LIST,
  DETAIL,
  SELECT,
  DIALOG,
}

interface VoucherItemProps {
  item: VoucherDto;
  category?: VoucherItemCategory;
  onSelectVoucher?: (item: VoucherDto) => void;
  isShowMatchPoint?: boolean;
  onNavigateToDetail?: () => void;
  isChecked?: boolean;
  onApplyClick?: (item: VoucherDto) => void;
  myVoucherList?: VoucherDto[];
  isDisabled?: boolean;
  fetchVouchers?: (reset?: boolean) => void;
}

function QuantityProgressBar({ item }: { item: { quantity: number; quantityUsed: number } }) {
  const { quantity = 0, quantityUsed = 0 } = item;
  const rawPercentage = quantity > 0 ? (quantityUsed / quantity) * 100 : 0;
  const progressPercentage = parseFloat(rawPercentage.toFixed(2));

  return (
    <Box
      sx={{
        width: { xs: "100%", sm: "70%" },
        display: "flex",
        flexDirection: "column",
        gap: 0.5,
        mt: 1,
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "6px",
          backgroundColor: "#E0E0E0",
          borderRadius: "3px",
          overflow: "hidden",
          position: "relative",
        }}
      >
        <Box
          sx={{
            width: `${Math.min(progressPercentage, 100)}%`,
            height: "100%",
            backgroundColor:
              progressPercentage >= 100
                ? "#F44336"
                : progressPercentage >= 80
                ? "#FF9800"
                : "#4CAF50",
            borderRadius: "3px",
            transition: "width 0.3s ease",
          }}
        />
      </Box>
      <Typography
        variant="caption"
        sx={{
          fontSize: { xs: "0.7rem", sm: "0.75rem" },
          color: "#666",
        }}
      >
        Đã sử dụng {progressPercentage}%
      </Typography>
    </Box>
  );
}

export default function VoucherItem({
  item,
  category,
  onSelectVoucher,
  isShowMatchPoint,
  onNavigateToDetail,
  isChecked,
  onApplyClick,
  myVoucherList,
  isDisabled,
  fetchVouchers,
}: VoucherItemProps) {
  const { color, bgColor, textColor, shopLogo } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();

  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const [isItemChecked, setItemChecked] = useState(isChecked || false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  useEffect(() => {
    setItemChecked(isChecked || false);
  }, [isChecked]);

  const onItemAction = (item) => {
    if (onNavigateToDetail) {
      onNavigateToDetail();
    } else if (onSelectVoucher) {
      if (category === VoucherItemCategory.SELECT) {
        onSelectVoucher(item);
        return;
      }
      switch (item.releaseType) {
        case ReleaseType.Free:
          handleSaveVoucher();
          break;
        case ReleaseType.ExchangePoints:
          handleExchangeVoucher();
          break;
        default:
          onSelectVoucher(item);
      }
    }
  };

  const isHaveInMyVoucher = myVoucherList?.find((voucher) => voucher.voucherId === item.voucherId);

  let btnAction: BtnCategory = BtnCategory.NONE;

  let btnActionLabel = "";
  if (category === VoucherItemCategory.LIST) {
    btnActionLabel = item.exchangePoints
      ? `Đổi ${item.exchangePoints} điểm`
      : ButtonHandleVoucherType.Save;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.MYLIST) {
    btnActionLabel = ButtonHandleVoucherType.Use;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.SELECT) {
    btnAction = BtnCategory.RADIO;
  }

  const handleSaveVoucher = async () => {
    checkLogin(async () => {
      try {
        const res = await dispatch(collectVoucher(item?.voucherDetails?.[0]?.voucherCode ?? ""));
        if (res?.payload?.status === 200) {
          toast.error(res?.payload?.detail);
        } else {
          if (shopId) {
            if (fetchVouchers) {
              fetchVouchers(true);
            }
            // Increment user voucher count when successfully collected
            dispatch(incrementUserVoucherCount());
            toast.success("Lưu voucher thành công");
          }
        }
      } catch (error) {
        console.error("Error saving voucher:", error);
      }
    });
  };

  const handleExchangeVoucher = async () => {
    if (item.releaseType === ReleaseType.Free) return;

    checkLogin(() => {
      if (!user?.point) {
        toast.error("Người dùng chưa có điểm khuyến mại");
        return;
      }
      if (user.point < item.exchangePoints) {
        toast.error("Điểm của người dùng không đủ");
        return;
      }

      setOpenConfirmDialog(true);
    });
  };

  const handleConfirmExchange = async () => {
    try {
      const res = await dispatch(collectVoucher(item?.voucherDetails?.[0]?.voucherCode ?? ""));
      if (res) {
        toast.success("Đổi voucher thành công");
        if (fetchVouchers) {
          fetchVouchers(true);
        }
        // Increment user voucher count when successfully exchanged
        dispatch(incrementUserVoucherCount());
        await dispatch(getUser());
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi đổi voucher");
    } finally {
      setOpenConfirmDialog(false);
    }
  };

  const handleCloseConfirmDialog = () => {
    setOpenConfirmDialog(false);
  };

  const onClickVoucher = (item: VoucherDto, event: React.MouseEvent) => {
    event.stopPropagation();
    if (category === VoucherItemCategory.SELECT || category === VoucherItemCategory.MYLIST) {
      onSelectVoucher?.(item);
      return;
    }
    switch (item.releaseType) {
      case ReleaseType.Free:
        handleSaveVoucher();
        break;
      case ReleaseType.ExchangePoints:
        handleExchangeVoucher();
        break;
      default:
        onSelectVoucher?.(item);
    }
  };

  const onNavigate = () => {
    const voucherCode = item?.voucherDetails?.[0]?.voucherCode?.toString() ?? "";
    navigate(`${Router.voucher.detail.replace(":code", voucherCode)}`, {
      state: { voucher: item },
    });
  };

  const handleItemClick = () => {
    if (isDisabled) {
      // onNavigate();
    } else {
      onItemAction(item);
    }
  };

  return (
    <>
      <Box
        className="wrap"
        key={item.voucherId}
        onClick={handleItemClick}
        sx={{
          cursor: isDisabled ? "not-allowed" : "pointer",
          width: "100%",
          maxWidth: "100%",
        }}
      >
        <Box
          className="coupon"
          sx={{
            border: `1px solid #e0e0e0`,
            display: "flex",
            borderRadius: 2,
            overflow: "hidden",
            width: "100%",
            height: "180px",
          }}
          bgcolor={COLORS.white}
        >
          <Stack className="coupon-left" sx={styles.leftContainer} p={1}>
            <Stack
              className="content"
              sx={{
                ...styles.leftContent,
                height: { xs: "80px", sm: "100px" },
                width: { xs: "80px", sm: "100px" },
                minWidth: { xs: "80px", sm: "100px" },
                flexShrink: 0,
              }}
              borderRadius="2px"
            >
              <img
                width={"100%"}
                height={"100%"}
                src={
                  item.image?.link && item.image.link.trim() !== ""
                    ? item.image.link
                    : typeof shopLogo === "string"
                    ? shopLogo
                    : shopLogo?.link || ""
                }
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src =
                    typeof shopLogo === "string" ? shopLogo : shopLogo?.link || "";
                }}
                style={{
                  objectFit: "cover",
                  filter: isDisabled ? "grayscale(1)" : "none",
                }}
              />
            </Stack>
          </Stack>
          <Box
            sx={{
              width: "1px",
              backgroundImage: "linear-gradient(to bottom, #e0e0e0 50%, transparent 50%)",
              backgroundSize: "1px 8px",
              marginY: 2,
            }}
          />
          <Stack
            className="coupon-con"
            direction="row"
            px={1}
            sx={{
              width: "100%",
              flex: 1,
              minWidth: 0, // Allow flex item to shrink below content size
            }}
          >
            <Stack
              sx={{
                ...styles.rightTop,
                height: "100%", // Take full height
              }}
              position={"relative"}
              direction="column"
              py={1}
            >
              {item?.voucherDetails?.[0]?.numUse !== null && (
                <>
                  <Typography
                    position={"absolute"}
                    top={"4px"}
                    right={{ xs: "-10px", sm: "-8px" }}
                    fontSize={{ xs: 12, sm: 14 }}
                    fontWeight={700}
                    lineHeight={"22px"}
                    color={COLOR.text.white}
                    bgcolor={isDisabled ? COLORS.neutral5 : color.primary}
                    width={{ xs: 35, sm: 50 }}
                    height={22}
                    align="center"
                    borderRadius={"10px 4px 4px 10px"}
                    sx={{
                      zIndex: 0,
                      opacity: 0.7,
                      "&::after": {
                        content: '""',
                        position: "absolute",
                        bottom: "-6px",
                        right: "0px",
                        width: 0,
                        height: 0,
                        borderRight: "8px solid transparent",
                        borderTop: `8px solid ${isDisabled ? COLORS.neutral5 : color.primary}`,
                        zIndex: -1,
                        opacity: 1,
                      },
                    }}
                  >
                    x{item?.voucherDetails?.[0]?.numUse}
                  </Typography>
                </>
              )}

              <Box>
                <Button
                  variant="outlined"
                  sx={styles.btnTopLeft}
                  style={{
                    borderColor: !isDisabled ? color.primary : COLORS.neutral5,
                    color: COLOR.text.white,
                    borderRadius: 4,
                    fontSize: 10,
                    backgroundColor: !isDisabled ? color.primary : COLORS.neutral5,
                  }}
                >
                  {item?.voucherType === VoucherType.Transport ? "Mã vận chuyển" : "Mã giảm giá"}
                </Button>
                {/* {isShowMatchPoint && (
                  <Button
                    variant="outlined"
                    sx={styles.btnTopLeft}
                    style={{
                      borderColor: color.primary,
                      color: color.primary,
                      marginLeft: 3,
                      borderRadius: 2,
                      fontSize: 12,
                      backgroundColor: color.accent,
                    }}
                  >
                    {item.exchangePoints} điểm
                  </Button>
                )} */}
              </Box>
              {/* Main content area with flex layout */}
              <Box
                sx={{
                  width: "100%",
                  flex: 1,
                  display: "flex",
                  flexDirection: "column",
                  height: "100%",
                }}
              >
                {/* Title and code section */}
                <Box>
                  <Typography
                    sx={{
                      fontSize: { xs: 13, sm: 14 },
                      lineHeight: "18px",
                      fontWeight: 600,
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      minHeight: "36px",
                    }}
                  >
                    {item && item && SelectedVoucher(item)}
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: { xs: 11, sm: 12 },
                      lineHeight: "16px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      maxWidth: "100%",
                      display: "block",
                      color: COLORS.neutral6,
                      mt: 0.5,
                    }}
                  >
                    {item?.voucherDetails?.[0]?.voucherCode}
                  </Typography>
                </Box>

                {/* Progress bar section */}
                <QuantityProgressBar
                  item={{ quantity: item.quantity, quantityUsed: item.quantityUsed }}
                />

                {/* Bottom section - pushed to bottom */}
                <Box
                  sx={{
                    width: "100%",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    flexDirection: "row",
                    mt: "auto", // Push to bottom
                  }}
                >
                  <Box sx={{ width: "100%", flex: 1 }}>
                    <Typography
                      color={COLOR.text.voucher_info}
                      fontSize={{ xs: 10, sm: 11 }}
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        lineHeight: "14px",
                      }}
                    >
                      HSD:{" "}
                      {item?.isLongTerm !== true
                        ? `${dayjs(item?.startDate).format("DD/MM/YYYY")} - ${dayjs(
                            item?.endDate
                          ).format("DD/MM/YYYY")}`
                        : "Không giới hạn"}
                    </Typography>
                  </Box>

                  {!isDisabled && (
                    <Box sx={{ flexShrink: 0, ml: 1 }}>
                      <VoucherAction
                        item={item}
                        category={btnAction}
                        value={btnActionLabel}
                        isChecked={isItemChecked}
                        isMyVoucher={isHaveInMyVoucher ? true : false}
                        eventAction={(event) => onClickVoucher(item, event)}
                        isDisabled={isDisabled}
                      />
                    </Box>
                  )}
                </Box>
              </Box>
            </Stack>
          </Stack>
        </Box>
      </Box>

      <Dialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        aria-labelledby="confirm-exchange-dialog"
      >
        <DialogTitle id="confirm-exchange-dialog">Xác nhận đổi voucher</DialogTitle>
        <DialogContent>
          <Typography>
            Bạn có chắc chắn muốn đổi {item.exchangePoints} điểm để lấy voucher này không?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmDialog} color="primary" variant="outlined">
            Hủy
          </Button>
          <Button
            onClick={handleConfirmExchange}
            color="primary"
            variant="contained"
            sx={{ color: "white" }}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>

      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  leftContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  leftContent: {
    textAlign: "center",
    justifyContent: "center",
    alignItems: "center",
    color: COLORS.white,
    gap: 1,
    fontSize: 10,
    height: "100%",
    width: "100%",
  },
  zaloText: {
    width: 50,
    height: 50,
    background: COLORS.white,
    display: "flex",
    alignItems: "center",
    borderRadius: "50%",
    justifyContent: "center",
    margin: "0px auto",
    fontSize: 14,
    fontWeight: 700,
  },
  rightContainer: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    fontSize: 12,
  },
  rightTop: {
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  typeText: {
    padding: "4px 8px",
    background: COLORS.primary2,
    textAlign: "center",
    borderRadius: 4,
    fontSize: 12,
    marginBottom: 5,
  },
  btnTopLeft: {
    paddingInline: 1,
    paddingBlock: 0,
    fontWeight: 400,
    marginBottom: 0.5,
  },
};
