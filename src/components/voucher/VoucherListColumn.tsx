import { PAGE_SIZE, StatusDelivery, VoucherType } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { useCart } from "@/hooks/useCart";

import {
  getListVoucherByShop,
  getListVoucherByShopPublic,
  VoucherDto,
  GetVoucherByShopParams,
} from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { showToast } from "@/utils/common";
import { useNavigate } from "@/utils/component-util";
import { Box, Stack, Typography, useTheme, Button } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import VoucherItem, { VoucherItemCategory } from "./VourcherItem";
import VoucherSkeleton from "./VoucherSkeleton";

interface VoucherListColumnProps {
  tabIndex?: number;
  config?: {
    type: string;
    show: boolean;
    style?: {
      title?: string;
    };
  };
  limit?: number;
}

const VoucherListColumn: React.FC<VoucherListColumnProps> = ({ tabIndex = 0, config, limit }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { user } = useSelector((state: RootState) => state.auth);
  const { cartPayment, setCartAndSave } = useCart();
  const navigate = useNavigate();
  const theme = useTheme();

  const [vouchers, setVouchers] = useState<VoucherDto[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageIndex, setPageIndex] = useState(0);

  const observerTarget = useRef(null);

  const fetchVouchers = async (reset = false) => {
    setIsLoading(true);
    let currentPage = reset ? 0 : pageIndex;
    const params: GetVoucherByShopParams = {
      PageSize: PAGE_SIZE,
      PageIndex: currentPage,
      shopId,
      Search: "",
    };

    try {
      // Use public API if user is not logged in, otherwise use authenticated API
      const response = user
        ? await dispatch(getListVoucherByShop(params))
        : await dispatch(getListVoucherByShopPublic(params));

      if (response?.payload) {
        const newVouchers = response?.payload?.result || [];
        setVouchers(reset ? newVouchers : [...(reset ? [] : vouchers), ...newVouchers]);
        setHasMore((reset ? 0 : vouchers.length) + newVouchers.length < response?.payload?.total);
        setPageIndex(currentPage + 1);
      }
    } catch (error) {
      console.error("Error fetching vouchers:", error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (shopId) {
      fetchVouchers(true);
    }
    // eslint-disable-next-line
  }, [shopId, user]);

  const handleSelectVoucher = async (voucher: VoucherDto) => {
    try {
      if (cartPayment.listItems.length > 0) {
        if (voucher.voucherType === VoucherType.Transport) {
          if (cartPayment.statusDelivery === StatusDelivery.InHome) {
            await setCartAndSave({
              ...cartPayment,
              voucherTransport: [voucher],
            });
          } else {
            await setCartAndSave({
              ...cartPayment,
              statusDelivery: StatusDelivery.InHome,
              voucherTransport: [voucher],
            });
          }
        } else {
          await setCartAndSave({
            ...cartPayment,
            voucherPromotion: [voucher],
          });
        }
        navigate(Router.cartPayment);
      } else {
        showToast({
          content: "Vui lòng chọn sản phẩm trước khi áp dụng mã giảm giá",
          type: "error",
        });
      }
    } catch (error) {
      if ((error as any).status === 400) {
        navigate(Router.menu);
      }
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading) {
          fetchVouchers(false);
        }
      },
      { threshold: 0.1 }
    );
    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }
    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [hasMore, isLoading, vouchers.length, pageIndex]);

  const title = config?.style?.title || "Ưu đãi";

  if (isLoading && !vouchers.length) {
    return (
      <Box sx={{ background: "#ffffff00", borderRadius: 3, my: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
          <Typography
            variant="h6"
            fontWeight={600}
            fontSize={16}
            sx={{ color: theme.palette.primary.main }}
          >
            {title}
          </Typography>
          <Button
            sx={{
              fontSize: 14,
              fontWeight: 500,
              textTransform: "none",
              padding: 0,
              cursor: "pointer",
              color: theme.palette.primary.main,
            }}
            disabled
          >
            Xem thêm
            <ChevronRightIcon />
          </Button>
        </Stack>

        {/* Use the existing VoucherSkeleton component */}
        <VoucherSkeleton count={limit || 3} />
      </Box>
    );
  }

  if (!vouchers.length) {
    return (
      <Box sx={{ background: "#ffffff00", borderRadius: 3, my: 2, px: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
          <Typography
            variant="h6"
            fontWeight={600}
            fontSize={18}
            sx={{ color: theme.palette.primary.main }}
          >
            {title}
          </Typography>
        </Stack>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            py: 4,
            px: 2,
            bgcolor: "rgba(255, 255, 255, 0.8)",
            borderRadius: 2,
            border: "2px dashed",
            borderColor: "grey.300",
          }}
        >
          <img
            src="/icons/voucher.png"
            alt="No vouchers"
            style={{ width: 48, height: 48, opacity: 0.5, marginBottom: 16 }}
          />
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Chưa có ưu đãi nào
          </Typography>
          <Typography variant="caption" color="text.secondary" textAlign="center">
            Hãy quay lại sau để xem các ưu đãi mới
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ background: "#ffffff00", borderRadius: 3, my: 2 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
        <Typography
          variant="h6"
          fontWeight={600}
          fontSize={16}
          sx={{ color: theme.palette.primary.main }}
        >
          {title}
        </Typography>
        <Button
          onClick={() => navigate(Router.voucher.index + "?tab=1")}
          sx={{
            fontSize: 14,
            fontWeight: 500,
            textTransform: "none",
            padding: 0,
            cursor: "pointer",
            color: theme.palette.primary.main,
          }}
        >
          Xem thêm
          <ChevronRightIcon />
        </Button>
      </Stack>

      {/* Voucher Items in Column Layout */}
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        {vouchers.slice(0, limit).map((voucher) => (
          <Box
            key={voucher.voucherId}
            sx={{
              height: "180px", // Fixed height to match other voucher items
              display: "flex",
              alignItems: "stretch",
            }}
          >
            <VoucherItem
              item={voucher}
              category={tabIndex === 0 ? VoucherItemCategory.LIST : VoucherItemCategory.MYLIST}
              onNavigateToDetail={() => {
                const voucherCode = voucher?.voucherDetails?.[0]?.voucherCode?.toString() ?? "";
                navigate(`${Router.voucher.detail.replace(":code", voucherCode)}`, {
                  state: { voucher },
                });
              }}
              isDisabled={false}
              onSelectVoucher={() => handleSelectVoucher(voucher)}
              fetchVouchers={() => fetchVouchers(true)}
            />
          </Box>
        ))}
      </Box>

      {/* Infinite scroll trigger */}
      {/* {hasMore && (
        <Box
          ref={observerTarget}
          sx={{ height: 20, display: "flex", justifyContent: "center", mt: 2 }}
        >
          {isLoading && <CircularProgress size={20} />}
        </Box>
      )} */}
    </Box>
  );
};

export default VoucherListColumn;
