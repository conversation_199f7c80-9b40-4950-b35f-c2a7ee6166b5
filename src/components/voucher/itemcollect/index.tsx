import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import VourcherItemCollectFnB from "./VourcherItemCollectFnB";
import VourcherItemCollectRetail from "./VourcherItemCollectRetail";

export { VoucherItemCategory } from "./VourcherItemCollectFnB";

const componentMap = {
  FnB: VourcherItemCollectFnB,
  Retail: VourcherItemCollectRetail,
};

export default function VoucherItemCollect(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FnB");
  const Component = componentMap[businessType] || componentMap.FnB;
  return <Component {...props} />;
}
