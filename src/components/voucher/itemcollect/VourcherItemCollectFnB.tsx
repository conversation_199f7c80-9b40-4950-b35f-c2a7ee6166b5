import VoucherAction, { BtnCategory } from "@/components/voucher/ActionVoucher";
import { SelectedVoucher } from "@/components/voucher/CheckVoucherType";
import { ButtonHandleVoucherType, ReleaseType, VoucherType } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { COLOR, COLORS } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { collectVoucher, VoucherDto } from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { IDataCreateVoucherUser } from "@/types/voucher";
import { formatPrice } from "@/utils/formatPrice";
import { Box, Button, Stack, Typography } from "@mui/material";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

export enum VoucherItemCategory {
  MYLIST,
  MYDETAIL,
  LIST,
  DETAIL,
  SELECT,
  DIALOG,
}

export default function VoucherItemCollectFnB({
  item,
  category,
  isShowMatchPoint,
  onSelectVoucher,
  onNavigateToDetail,
  onApplyClick,
  isChecked,
  listVoucherByShop,
  isDisabled,
}: {
  item: VoucherDto;
  category?: VoucherItemCategory;
  onSelectVoucher?: (item: VoucherDto) => void;
  isShowMatchPoint?: boolean;
  onNavigateToDetail?: () => void;
  isChecked?: boolean;
  onApplyClick?: (item) => void;
  listVoucherByShop?: VoucherDto[];
  isDisabled?: boolean;
}) {
  const { color, bgColor, textColor, shopLogo } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const appConfig = useConfigApp();

  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const [isItemChecked, setItemChecked] = useState(isChecked || false);

  useEffect(() => {
    setItemChecked(isChecked || false);
  }, [isChecked]);

  const onItemAction = (item) => {
    if (onNavigateToDetail) {
      onNavigateToDetail();
    } else if (onSelectVoucher) {
      onSelectVoucher(item);
    }
  };

  const isHaveInMyVoucher = listVoucherByShop?.find(
    (voucher) => voucher.voucherId === item.voucherId
  );

  let btnAction: BtnCategory = BtnCategory.NONE;

  let btnActionLabel = "";
  if (category === VoucherItemCategory.LIST) {
    btnActionLabel = item.exchangePoints
      ? ButtonHandleVoucherType.Exchange
      : ButtonHandleVoucherType.Save;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.MYLIST) {
    btnActionLabel = ButtonHandleVoucherType.Use;
    btnAction = BtnCategory.BTN;
  } else if (category === VoucherItemCategory.SELECT) {
    btnAction = BtnCategory.RADIO;
  }

  const handleSaveVoucher = async () => {
    try {
      const userId = user?.userId || user?.idFor;
      if (!userId) return;
      const voucherData: IDataCreateVoucherUser = {
        voucherId: item?.voucherId,
        shopId: item?.shopId,
      };

      const res = await dispatch(collectVoucher(item?.voucherDetails?.[0]?.voucherCode ?? ""));
      if (res?.payload?.status === 400) {
        toast.error(res?.payload?.detail);
      } else {
        toast.success("Lưu voucher thành công");
        navigate(Router.voucher.index);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  const handleExchangeVoucher = async () => {
    if (item.releaseType === ReleaseType.Free) return;
    if (!user?.point) {
      toast.error("Người dùng chưa có điểm khuyến mại");
      return;
    }
    if (user.point < item.exchangePoints) {
      toast.error("Điểm của người dùng không đủ");
      return;
    }

    const voucherData: IDataCreateVoucherUser = {
      voucherId: item?.voucherId,
      shopId: item?.shopId,
    };

    const res = await dispatch(collectVoucher(item?.voucherDetails?.[0]?.voucherCode ?? ""));
    if (res?.payload?.status === 400) {
      toast.error(res?.payload?.detail);
    } else {
      toast.success("Đổi voucher thành công");
      navigate(Router.voucher.index);
    }
  };

  const onClickVoucher = (event: React.MouseEvent) => {
    event.stopPropagation();

    switch (btnActionLabel) {
      case ButtonHandleVoucherType.Save:
        handleSaveVoucher();
        break;
      case ButtonHandleVoucherType.Exchange:
        handleExchangeVoucher();
        break;
      default:
        onSelectVoucher?.(item);
    }
  };
  const onNavigate = () => {
    const voucherCode = item?.voucherDetails?.[0]?.voucherCode?.toString() ?? "";

    navigate(`${Router.voucher.detail.replace(":code", voucherCode)}`, {
      state: { voucher: item },
    });
  };

  return (
    <Box
      className="wrap"
      key={item.voucherId}
      onClick={() => {
        if (isDisabled) {
          onNavigate();
        } else {
          onItemAction(item);
        }
      }}
      bgcolor={COLOR.bg.primary}
      sx={{
        opacity: isDisabled ? 0.5 : 1,
        cursor: isDisabled ? "not-allowed" : "pointer",
      }}
    >
      <Box
        className="coupon"
        style={{ border: `1px soild #000000`, display: "flex" }}
        bgcolor={COLOR.bg.primary}
      >
        <Stack className="coupon-left" sx={styles.leftContainer} p={1}>
          <Stack
            className="content"
            sx={{
              ...styles.leftContent,
              background: color.primary,
              height: "100px",
              width: "100px",
            }}
            borderRadius="2px"
          >
            <img
              width={"100%"}
              height={"100%"}
              src={item.image?.link || shopLogo}
              style={{ objectFit: "cover" }}
            />
          </Stack>
        </Stack>
        <Box
          sx={{
            width: "1px",
            backgroundImage: "linear-gradient(to bottom, #e0e0e0 50%, transparent 50%)",
            backgroundSize: "1px 8px",
            marginY: 2,
            position: "relative",
          }}
        />
        <Stack className="coupon-con" direction="row" px={1}>
          <Stack sx={styles.rightTop} position={"relative"} direction="column" py={1}>
            <Typography
              position={"absolute"}
              top={0}
              right={0}
              fontSize={14}
              fontWeight={700}
              lineHeight={"24px"}
              color={COLOR.text.white}
              bgcolor={color.primary}
              width={40}
              height={24}
              align="center"
              borderRadius={"2px 2px 0 0"}
            >
              {btnAction === BtnCategory.RADIO
                ? `x${item?.quantity}`
                : btnActionLabel !== ButtonHandleVoucherType.Use
                ? `x${item?.quantity}`
                : `x${item.quantity}`}
            </Typography>
            <Box>
              <Button
                variant="outlined"
                sx={styles.btnTopLeft}
                style={{
                  borderColor: color.primary,
                  color: color.primary,
                  borderRadius: 2,
                  fontSize: 12,
                  backgroundColor: color.primary,
                }}
              >
                {item?.voucherType === VoucherType.Transport ? "Mã vận chuyển" : "Mã giảm giá"}
              </Button>
              {isShowMatchPoint && (
                <Button
                  variant="outlined"
                  sx={styles.btnTopLeft}
                  style={{
                    borderColor: color.primary,
                    color: color.primary,
                    marginLeft: 3,
                    borderRadius: 2,
                    fontSize: 12,
                  }}
                >
                  {item.exchangePoints} Star
                </Button>
              )}
            </Box>
            <Box sx={{ mb: 1 }}>
              <Typography sx={{ fontSize: 14, lineHeight: "16px" }}>
                {item && item && SelectedVoucher(item)}
              </Typography>
            </Box>

            {/* <Typography color={COLOR.text.voucher_code} fontSize={12} fontWeight={500} mt={0.5}>
              #{item?.voucherDetails?.[0]?.voucherCode}
            </Typography> */}
            <Typography color={COLOR.text.voucher_info} fontSize={11}>
              {item?.minOrder ? "Đơn tối thiểu: " + formatPrice(item?.minOrder) : ""}
            </Typography>
            <Typography color={COLOR.text.voucher_info} fontSize={11}>
              HSD:{" "}
              {item.isLongTerm !== true
                ? `${dayjs(item?.startDate).format("DD/MM/YYYY")} - ${dayjs(item?.endDate).format(
                    "DD/MM/YYYY"
                  )}`
                : "Không giới hạn "}
            </Typography>
            <Box position={"absolute"} bottom={10} right={10}>
              <VoucherAction
                item={item}
                category={btnAction}
                value={btnActionLabel}
                isChecked={isItemChecked}
                isMyVoucher={isHaveInMyVoucher ? true : false}
                eventAction={onClickVoucher}
                isDisabled={isDisabled}
              />
            </Box>
          </Stack>
        </Stack>
      </Box>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  leftContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  leftContent: {
    textAlign: "center",
    justifyContent: "center",
    alignItems: "center",
    color: COLORS.white,
    gap: 1,
    fontSize: 10,
    height: "100%",
    width: "100%",
  },
  zaloText: {
    width: 50,
    height: 50,
    background: COLORS.white,
    display: "flex",
    alignItems: "center",
    borderRadius: "50%",
    justifyContent: "center",
    margin: "0px auto",
    fontSize: 14,
    fontWeight: 700,
  },
  rightContainer: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    fontSize: 12,
  },
  rightTop: {
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  typeText: {
    padding: "4px 8px",
    background: COLORS.primary2,
    textAlign: "center",
    borderRadius: 4,
    fontSize: 12,
    marginBottom: 5,
  },
  btnTopLeft: {
    fontSize: 11,
    paddingInline: 1,
    paddingBlock: 0.3,
    fontWeight: 400,
    marginBottom: 0.5,
  },
};
