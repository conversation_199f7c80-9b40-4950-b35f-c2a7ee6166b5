import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const BellIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      className={className}
      width="30"
      height="35"
      viewBox="0 0 30 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.83169 2.08973C6.0297 1.83664 6.12715 1.51049 6.10314 1.1813C6.07912 0.852116 5.93555 0.546135 5.70325 0.329046C5.47094 0.111957 5.16842 0.00106835 4.86063 0.020184C4.55284 0.0392997 4.26431 0.186896 4.05699 0.431288L2.02787 2.92064C0.877277 4.33278 0.226853 6.1332 0.190358 8.00699L0.100838 12.5803C0.0943816 12.9149 0.212289 13.2385 0.428622 13.48C0.644955 13.7214 0.941992 13.861 1.25439 13.8679C1.56679 13.8748 1.86895 13.7485 2.09442 13.5168C2.31988 13.2852 2.45017 12.967 2.45663 12.6325L2.54458 8.06081C2.56977 6.77867 3.01507 5.54684 3.80257 4.58077L5.83169 2.08973Z"
        fill={fillColor}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.5293 10.9829C5.63477 9.18337 6.37663 7.49455 7.60384 6.26023C8.83106 5.02591 10.4514 4.33888 12.1349 4.339H13.0097V3.0775C13.0097 2.63141 13.1752 2.20359 13.4697 1.88815C13.7642 1.57272 14.1637 1.39551 14.5802 1.39551C14.9968 1.39551 15.3962 1.57272 15.6908 1.88815C15.9853 2.20359 16.1508 2.63141 16.1508 3.0775V4.339H17.0256C18.7091 4.33888 20.3294 5.02591 21.5566 6.26023C22.7839 7.49455 23.5257 9.18337 23.6312 10.9829L23.9783 16.927C24.1105 19.1935 24.8211 21.3775 26.0325 23.2412C26.2828 23.6265 26.4357 24.0746 26.4764 24.5419C26.5171 25.0093 26.4442 25.4799 26.2648 25.9081C26.0853 26.3364 25.8055 26.7075 25.4524 26.9856C25.0994 27.2637 24.6851 27.4392 24.25 27.495L18.8992 28.1813V29.9894C18.8992 31.2161 18.4442 32.3927 17.6342 33.2601C16.8242 34.1276 15.7257 34.6149 14.5802 34.6149C13.4348 34.6149 12.3362 34.1276 11.5263 33.2601C10.7163 32.3927 10.2613 31.2161 10.2613 29.9894V28.1813L4.91051 27.4933C4.47562 27.4373 4.06165 27.2618 3.70881 26.9839C3.35598 26.7059 3.07627 26.3349 2.89686 25.907C2.71746 25.479 2.64445 25.0086 2.68495 24.5415C2.72544 24.0744 2.87806 23.6265 3.12796 23.2412C4.33931 21.3775 5.04986 19.1934 5.18221 16.927L5.5293 10.9846V10.9829ZM12.6171 29.9894C12.6171 30.547 12.8239 31.0818 13.1921 31.4761C13.5602 31.8704 14.0596 32.0919 14.5802 32.0919C15.1009 32.0919 15.6002 31.8704 15.9684 31.4761C16.3366 31.0818 16.5434 30.547 16.5434 29.9894V28.7279H12.6171V29.9894Z"
        fill={fillColor}
      />
      <path
        d="M23.4424 0.310814C23.2071 0.530774 23.063 0.841804 23.0418 1.17551C23.0206 1.50922 23.124 1.83829 23.3293 2.09036L25.3584 4.57971C26.1457 5.54646 26.5904 6.77893 26.6148 8.06144L26.7043 12.6314C26.7075 12.7971 26.7412 12.9604 26.8033 13.1122C26.8655 13.2639 26.9549 13.4011 27.0666 13.5158C27.1782 13.6305 27.3098 13.7206 27.454 13.7808C27.5981 13.841 27.7519 13.8703 27.9066 13.8668C28.0613 13.8634 28.2138 13.8274 28.3555 13.7608C28.4972 13.6943 28.6252 13.5985 28.7323 13.4789C28.8395 13.3594 28.9235 13.2184 28.9798 13.064C29.036 12.9096 29.0633 12.7449 29.0601 12.5793L28.9706 8.00762C28.9341 6.13382 28.2837 4.33341 27.1331 2.92127L25.104 0.431917C24.8986 0.179935 24.6082 0.0256234 24.2966 0.00291345C23.985 -0.0197965 23.6777 0.0909542 23.4424 0.310814Z"
        fill={fillColor}
      />
    </svg>
  );
};

export default BellIcon;
