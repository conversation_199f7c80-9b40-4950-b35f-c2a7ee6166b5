import * as React from "react";

type StarBannerProps = React.SVGProps<SVGSVGElement> & {
  fill?: string;
};

const StarBanner = ({ fill = "#1531AD", ...props }: StarBannerProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 390 120"
    preserveAspectRatio="xMidYMid slice"
    style={{ width: "100%", height: "100%", display: "block" }}
    {...props}
  >
    <g clipPath="url(#clip0)">
      <rect width="390" height="120" fill={fill} rx="15" />
      <path
        fill="none"
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeOpacity="0.1"
        strokeWidth="3"
        d="m359.064 70.752 12.679 43.589c1.707 6.057 8.041 12.75 14.038 14.932l35.428 12.873c22.654 8.244 25.006 26.01 5.053 39.344l-33.697 22.685c-5.706 3.842-10.066 12.502-9.81 19.339l1.603 36.237c1.247 28.678-15.536 36.521-37.472 17.527l-30.498-26.459c-5.507-4.782-15.706-6.702-22.69-4.271l-38.03 13.559c-27.226 9.74-40.102-3.803-28.535-29.952l14.669-33.173c2.724-6.276 1.812-15.929-2.108-21.583l-23.142-33.385c-13.624-19.654-5.089-35.371 19.012-34.814l37.684.89c6.265.126 14.599-3.806 18.392-8.828l27.66-35.995c15.065-19.52 32.942-16.156 39.764 7.485"
      />
      <path
        fill="none"
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeOpacity="0.1"
        strokeWidth="2"
        d="m90.674 58.912 10.056 34.573c1.354 4.804 6.378 10.113 11.135 11.844l28.1 10.21c17.968 6.539 19.833 20.63 4.008 31.206l-26.728 17.993c-4.526 3.048-7.984 9.917-7.781 15.34l1.272 28.741c.989 22.746-12.323 28.967-29.722 13.902l-24.19-20.987c-4.368-3.793-12.457-5.315-17.996-3.387L8.663 209.101c-21.595 7.726-31.807-3.016-22.633-23.756l11.635-26.312c2.16-4.978 1.437-12.635-1.671-17.119l-18.356-26.48c-10.806-15.588-4.036-28.054 15.08-27.613l29.89.706c4.968.1 11.579-3.02 14.587-7.002l21.939-28.55c11.95-15.483 26.129-12.814 31.54 5.937M131.215-45.665 151.59-35.31c2.813 1.459 7.443 1.44 10.298-.01l16.858-8.567c10.785-5.473 18.126-.22 16.204 11.691l-3.184 20.174c-.54 3.416 1.155 7.997 3.758 10.257l13.88 11.887c10.977 9.416 8.12 18.274-6.374 19.783l-20.17 2.08c-3.644.375-7.84 3.456-9.336 6.855l-8.025 18.622c-5.732 13.342-15.121 13.404-20.783.158l-7.185-16.803c-1.377-3.16-5.241-6.149-8.662-6.657l-20.201-3.003c-11.893-1.768-14.755-10.31-6.303-18.961l13.222-13.52c2.19-2.255 3.596-6.667 3.046-9.787l-3.778-22.541c-2.027-12.248 5.328-17.649 16.36-12.013"
      />
    </g>
    <defs>
      <clipPath id="clip0">
        <rect width="390" height="120" fill="#fff" rx="15" />
      </clipPath>
    </defs>
  </svg>
);

export default StarBanner;
