import React from "react";

export interface IconCustomProps {
  fillColor?: string;
  className?: string;
}

const NttShareIcon: React.FC<IconCustomProps> = ({ fillColor }) => {
  return (
    <svg width="24" height="19" viewBox="0 0 24 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M23.25 9L14.5 0.25V5.25C5.75 6.5 2 12.75 0.75 19C3.875 14.625 8.25 12.625 14.5 12.625V17.75L23.25 9Z"
        fill={fillColor}
      />
    </svg>
  );
};

export default NttShareIcon;
