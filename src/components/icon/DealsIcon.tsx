import * as React from "react";

const DealsIcon: React.FC<React.SVGProps<SVGSVGElement> & { fill?: string }> = ({
  fill = "#1531AD",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="66"
    height="66"
    fill="none"
    viewBox="0 0 66 66"
    {...props}
  >
    <rect width="66" height="66" fill={fill} fillOpacity="0.08" rx="33"></rect>
    <path
      stroke={fill}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M43.625 33.708a3.543 3.543 0 0 1 3.542-3.541V28.75c0-5.667-1.417-7.083-7.084-7.083H25.917c-5.667 0-7.084 1.416-7.084 7.083v.708a3.543 3.543 0 0 1 0 7.084v.708c0 5.667 1.417 7.083 7.084 7.083h14.166c5.667 0 7.084-1.416 7.084-7.083a3.543 3.543 0 0 1-3.542-3.542M28.75 36.896l8.5-8.5"
    ></path>
    <path
      stroke={fill}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M37.242 36.896h.013M28.742 29.104h.013"
    ></path>
  </svg>
);

export default DealsIcon;
