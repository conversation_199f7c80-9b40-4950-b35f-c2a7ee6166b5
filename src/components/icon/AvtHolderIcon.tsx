import * as React from "react";

type AvtHolderIconProps = React.SVGProps<SVGSVGElement> & {
  fill?: string;
};

const AvtHolderIcon: React.FC<AvtHolderIconProps> = ({ fill = "#1531AD", ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="52"
    height="52"
    fill="none"
    viewBox="0 0 52 52"
    {...props}
  >
    <path
      fill="#F4F4F4"
      stroke={fill}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M26 51c13.807 0 25-11.193 25-25S39.807 1 26 1 1 12.193 1 26s11.193 25 25 25"
    ></path>
    <path
      fill="#F4F4F4"
      stroke={fill}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M26.3 27.95a2.4 2.4 0 0 0-.6 0c-4.4-.15-7.9-3.75-7.9-8.176 0-4.525 3.65-8.2 8.2-8.2 4.525 0 8.2 3.675 8.2 8.2-.025 4.425-3.5 8.025-7.9 8.175M42.85 44.45A24.84 24.84 0 0 1 26 51c-6.5 0-12.4-2.475-16.85-6.55.25-2.35 1.75-4.65 4.425-6.45 6.85-4.55 18.05-4.55 24.85 0 2.675 1.8 4.175 4.1 4.425 6.45"
    ></path>
  </svg>
);

export default AvtHolderIcon;
