import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const QrIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.89212 9.93665C4.45808 9.93665 4.76265 9.62093 4.76265 9.04429V6.47679C4.76265 5.34486 5.37179 4.76822 6.46008 4.76822H9.09351C9.65947 4.76822 9.97472 4.45251 9.97472 3.88654C9.97472 3.33172 9.65901 3.01601 9.09351 3.01601H6.42758C4.16419 3.01601 3.01044 4.13679 3.01044 6.37836V9.04476C3.01044 9.6214 3.32615 9.93665 3.89212 9.93665ZM22.1079 9.93665C22.6845 9.93665 22.9896 9.62093 22.9896 9.04429V6.37836C22.9896 4.13679 21.8577 3.01601 19.5724 3.01601H16.9172C16.3405 3.01601 16.0253 3.33172 16.0253 3.88654C16.0253 4.45251 16.341 4.76822 16.9172 4.76822H19.5506C20.6171 4.76822 21.2374 5.34486 21.2374 6.47679V9.04429C21.2374 9.6214 21.5531 9.93665 22.1079 9.93665ZM12.532 12.0584V8.26104C12.532 8.00011 12.3254 7.78236 12.0533 7.78236H8.26662C7.99454 7.78236 7.78747 7.99965 7.78747 8.26104V12.0589C7.78747 12.3198 7.99454 12.5269 8.26662 12.5269H12.0533C12.3254 12.5269 12.532 12.3194 12.532 12.0584ZM14.4147 8.72951H17.2654V11.5802H14.4147V8.72951ZM16.439 10.7538V9.56708H15.2527V10.7533L16.439 10.7538ZM10.7584 10.7538V9.56708H9.56151V10.7533L10.7584 10.7538ZM8.73415 14.4207H11.5853V17.2714H8.73462L8.73415 14.4207ZM18.0709 14.7903V13.6045H16.8847V14.7908L18.0709 14.7903ZM14.7954 14.7903V13.6045H13.6092V14.7908L14.7954 14.7903ZM10.7584 16.4339V15.2471H9.56151V16.4329L10.7584 16.4339ZM16.4385 16.4339V15.2471H15.2416V16.4329L16.4385 16.4339ZM16.9172 22.9845H19.5729C21.8577 22.9845 22.9896 21.8525 22.9896 19.611V16.9557C22.9896 16.3786 22.6739 16.0634 22.1079 16.0634C21.5419 16.0634 21.2374 16.3791 21.2374 16.9557V19.5232C21.2374 20.6551 20.6171 21.2318 19.5506 21.2318H16.9172C16.3405 21.2318 16.0253 21.5475 16.0253 22.1135C16.0253 22.6683 16.341 22.9845 16.9172 22.9845ZM6.42758 22.984H9.09351C9.65947 22.984 9.97472 22.6683 9.97472 22.1135C9.97472 21.5475 9.65901 21.2323 9.09351 21.2323H6.46008C5.37179 21.2323 4.76265 20.6551 4.76265 19.5237V16.9557C4.76265 16.3786 4.44694 16.0634 3.89212 16.0634C3.31547 16.0634 3.01044 16.3791 3.01044 16.9557V19.6105C3.01044 21.8632 4.16419 22.984 6.42758 22.984ZM14.7954 18.0765V16.8903H13.6092V18.0765H14.7954ZM18.0709 18.0765V16.8903H16.8847V18.0765H18.0709ZM18.2125 12.0589V8.26104C18.2125 8.00011 18.0055 7.78236 17.7334 7.78236H13.9472C13.6751 7.78236 13.4685 7.99965 13.4685 8.26104V12.0589C13.4685 12.3198 13.6751 12.5269 13.9467 12.5269H17.7339C18.0059 12.5269 18.2125 12.3198 18.2125 12.0589ZM8.73415 8.72858H11.5853V11.5793H8.73462L8.73415 8.72858ZM12.532 17.7385V13.9406C12.532 13.6797 12.3254 13.4731 12.0533 13.4731H8.26662C7.99454 13.4731 7.78747 13.6797 7.78747 13.9406V17.7385C7.78747 17.9999 7.99454 18.2172 8.26662 18.2172H12.0533C12.3254 18.2172 12.532 17.9999 12.532 17.7385Z"
        fill={fillColor}
      />
    </svg>
  );
};

export default QrIcon;
