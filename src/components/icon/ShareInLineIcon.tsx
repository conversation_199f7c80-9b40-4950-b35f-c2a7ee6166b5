import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const ShareInLineIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      className={className}
      width="49"
      height="49"
      viewBox="0 0 49 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M33.9121 42.875C32.5591 42.875 31.411 42.402 30.4678 41.456C29.5259 40.5087 29.055 39.3586 29.055 38.0056C29.055 37.8015 29.1448 37.2863 29.3245 36.4601L14.6306 27.736C14.1896 28.2451 13.6499 28.6446 13.0115 28.9345C12.3732 29.2244 11.6879 29.3694 10.9556 29.3694C9.61353 29.3694 8.47292 28.8916 7.53375 27.9361C6.59458 26.9806 6.125 25.8353 6.125 24.5C6.125 23.1648 6.59458 22.0194 7.53375 21.0639C8.47292 20.1084 9.61353 19.6306 10.9556 19.6306C11.6865 19.6306 12.3718 19.7756 13.0115 20.0655C13.6513 20.3554 14.1909 20.7556 14.6306 21.266L29.3265 12.5787C29.2312 12.3147 29.1625 12.0526 29.1203 11.7927C29.0767 11.5313 29.055 11.2646 29.055 10.9923C29.055 9.64075 29.5293 8.49129 30.478 7.54396C31.4267 6.59799 32.5782 6.125 33.9325 6.125C35.2868 6.125 36.4356 6.59935 37.3788 7.54804C38.3221 8.49674 38.793 9.64824 38.7917 11.0025C38.7903 12.3568 38.3173 13.5056 37.3727 14.4489C36.4281 15.3921 35.278 15.8631 33.9223 15.8617C33.1846 15.8617 32.504 15.7106 31.8806 15.4085C31.2572 15.1063 30.7264 14.7 30.2881 14.1896L15.5902 22.9136C15.6854 23.1777 15.7542 23.4404 15.7964 23.7017C15.8399 23.9617 15.8617 24.2278 15.8617 24.5C15.8617 24.7722 15.8399 25.0383 15.7964 25.2983C15.7528 25.5583 15.6848 25.821 15.5922 26.0864L30.2881 34.8104C30.7278 34.3 31.2586 33.8937 31.8806 33.5915C32.504 33.2894 33.1846 33.1383 33.9223 33.1383C35.2752 33.1383 36.4254 33.612 37.3727 34.5593C38.3187 35.5093 38.7917 36.6615 38.7917 38.0158C38.7917 39.3701 38.3173 40.5189 37.3686 41.4622C36.4199 42.4054 35.2664 42.8764 33.9121 42.875Z"
        fill={fillColor}
      />
    </svg>
  );
};

export default ShareInLineIcon;
