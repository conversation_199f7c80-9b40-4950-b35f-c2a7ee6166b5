import * as React from "react";

const ShareFriendIcon: React.FC<React.SVGProps<SVGSVGElement> & { fill?: string }> = ({
  fill = "#1531AD",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="19"
    height="22"
    fill="none"
    viewBox="0 0 19 22"
    {...props}
  >
    <circle cx="15.5" cy="3.5" r="2.5" stroke={fill} strokeWidth="1.5"></circle>
    <circle cx="3.5" cy="10.5" r="2.5" stroke={fill} strokeWidth="1.5"></circle>
    <path
      stroke={fill}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
      d="M13 5 6 9M5.5 12.5 13 17"
    ></path>
    <circle cx="15.5" cy="18.5" r="2.5" stroke={fill} strokeWidth="1.5"></circle>
  </svg>
);

export default ShareFriendIcon;
