import * as React from "react";

const PointHistoryIcon: React.FC<React.SVGProps<SVGSVGElement> & { fill?: string }> = ({
  fill = "#1531AD",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="66"
    height="66"
    fill="none"
    viewBox="0 0 66 66"
    {...props}
  >
    <rect width="66" height="66" fill={fill} fillOpacity="0.08" rx="33"></rect>
    <path
      stroke={fill}
      strokeMiterlimit="10"
      strokeWidth="2"
      d="M33 20.25c-7.04 0-12.75 5.71-12.75 12.75S25.96 45.75 33 45.75 45.75 40.04 45.75 33 40.04 20.25 33 20.25Z"
    ></path>
    <path
      stroke={fill}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M33 24.5v9.563h6.375"
    ></path>
  </svg>
);

export default PointHistoryIcon;
