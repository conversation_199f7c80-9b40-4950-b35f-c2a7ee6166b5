import React from "react";
import { useConfigApp } from "@/hooks/useConfigApp";

export const MenuActionProduct = () => {
  const appConfig = useConfigApp();
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.7857 25.0714C14.7873 25.3615 14.8042 25.6508 14.8355 25.9393C14.842 26.0004 14.8476 26.0623 14.8556 26.1233C14.8902 26.3989 14.9368 26.6714 14.9971 26.9398C15.0107 26.9968 15.0268 27.0514 15.0404 27.1077C15.0919 27.3118 15.1529 27.5127 15.218 27.7112C15.2526 27.8132 15.2823 27.9153 15.3233 28.0157C15.3924 28.199 15.4704 28.3773 15.5523 28.5541C15.6343 28.7309 15.7259 28.9101 15.8223 29.0837C15.8713 29.1737 15.9212 29.2629 15.9734 29.3505C16.0578 29.4919 16.1462 29.6317 16.2386 29.7683C16.3029 29.8599 16.3671 29.9483 16.4354 30.0367C16.5729 30.2215 16.7167 30.3999 16.867 30.5719C16.9795 30.7005 17.0936 30.829 17.2149 30.9472C17.2872 31.0211 17.3612 31.0934 17.4367 31.1641C17.6384 31.353 17.8465 31.5378 18.0691 31.7089L18.1205 31.7467C18.1294 31.7523 18.1696 31.7813 18.1768 31.7869C19.6112 32.8573 21.3533 33.4326 23.1421 33.4286C26.6006 33.4149 29.6976 31.2823 30.9447 28.0559C31.1689 27.479 31.3272 26.8787 31.4164 26.2664C31.4711 25.8702 31.4984 25.4716 31.4992 25.0714V19.2857H14.7849V25.0714H14.7857Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M12.3188 0H8.25349C5.09305 0.00723214 2.31832 2.10455 1.44885 5.14286H19.1226C18.2531 2.10455 15.4784 0.00723214 12.3188 0Z"
        fill={ appConfig.color.primary }
      />
      <path d="M0 6.42856H20.5714V8.99999H0V6.42856Z" fill={ appConfig.color.primary } />
      <path
        d="M14.7857 36H31.5C32.318 36.0024 33.0477 35.4865 33.3185 34.7143H12.9664C13.2396 35.4841 13.9677 35.9992 14.7849 36H14.7857Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M12.2143 33.4286H18.3359C18.3022 33.4085 18.2716 33.3828 18.2363 33.3643C17.9518 33.198 17.677 33.0164 17.411 32.8219L17.3523 32.7785C17.3411 32.7713 17.2993 32.7407 17.2881 32.7327C17.0341 32.5358 16.7898 32.3261 16.5568 32.1043C16.47 32.0223 16.3856 31.9396 16.2997 31.8552C16.1614 31.7154 16.0289 31.5707 15.8995 31.4229C15.7195 31.2188 15.5515 31.0082 15.3916 30.7913C15.3193 30.694 15.2454 30.5984 15.1763 30.4972C15.0662 30.3364 14.9641 30.1757 14.8645 30.0062C14.8074 29.9114 14.7536 29.8133 14.6997 29.7169C14.5784 29.4959 14.4635 29.2725 14.3598 29.0435C14.2755 28.8579 14.1927 28.6698 14.1204 28.4786C14.0963 28.4143 14.0681 28.35 14.0456 28.2857H3.02466L3.79287 36H12.2143C12.195 35.9751 12.1814 35.9462 12.1637 35.9197C12.1267 35.8682 12.0946 35.8136 12.0616 35.7598C11.9925 35.6497 11.9314 35.5364 11.8768 35.419C11.8479 35.3548 11.8206 35.2953 11.7956 35.2326C11.7482 35.1065 11.7081 34.9779 11.6759 34.8469C11.6614 34.7866 11.6422 34.7296 11.6309 34.6685C11.5923 34.4716 11.5731 34.2723 11.5722 34.0714C11.5722 33.7163 11.8599 33.4286 12.2151 33.4286H12.2143Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M13.6977 27C13.6487 26.7653 13.6101 26.5299 13.5812 26.2928C13.5715 26.2189 13.5643 26.1458 13.557 26.0719C13.5209 25.7392 13.5016 25.4057 13.5 25.0714V24.1714C13.3706 24.4085 13.2107 24.6286 13.0259 24.8255C13.0187 24.8344 13.0114 24.8432 13.0042 24.852C12.9962 24.8601 12.9897 24.8713 12.9809 24.8802C11.7249 26.1329 9.69187 26.1329 8.43508 24.8802L6.6174 23.0625C5.39758 21.8354 5.36785 19.8643 6.5491 18.6003C6.55472 18.5914 6.56115 18.5842 6.56838 18.5761C6.58687 18.5569 6.59892 18.5352 6.6174 18.5167C7.87258 17.2615 9.90803 17.2615 11.1632 18.5167L12.9809 20.3344C13.1866 20.5449 13.361 20.7828 13.5 21.0415V18.642C13.5 18.2869 13.7877 17.9992 14.1428 17.9992H18.5786L18.7071 16.7135H1.86749L2.89606 26.9992H13.6977V27Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M34.0712 24.4284C34.0712 23.7181 33.496 23.1429 32.7857 23.1429V25.0711C32.7857 25.2832 32.7785 25.4977 32.764 25.7139H32.7857C33.496 25.7139 34.0712 25.1386 34.0712 24.4284Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M1.33875 10.2857L1.75017 15.4286H18.8204L19.2319 10.2857H1.33875Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M32.7857 21.2143V21.8572C34.2056 21.8572 35.3571 23.0087 35.3571 24.4286C35.3571 25.8485 34.2056 27 32.7857 27H32.5928C32.5639 27.139 32.5285 27.2764 32.4948 27.413C32.4835 27.4564 32.4747 27.5006 32.4619 27.5416C32.453 27.5738 32.4474 27.6059 32.4377 27.6396H32.7849C34.5584 27.6364 35.996 26.1988 35.9992 24.4254C35.9952 22.6519 34.5584 21.2167 32.7849 21.2143H32.7857Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M10.4962 22.8214L8.90597 22.1327C8.28481 21.8652 7.76489 21.4071 7.42097 20.8253L7.05936 20.2154C6.83998 20.8993 7.01998 21.649 7.52623 22.1585L9.34391 23.9761C9.97632 24.6053 10.9567 24.7186 11.7161 24.251L11.4517 23.7254C11.2484 23.322 10.9101 23.0022 10.4962 22.8214Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M12.5671 23.0914C12.7415 22.4325 12.5526 21.7293 12.0713 21.2464L10.2536 19.4287C9.63406 18.8059 8.66978 18.6846 7.91522 19.1362L8.52674 20.1648C8.73245 20.5143 9.04424 20.7892 9.4171 20.9499L11.0074 21.6401C11.6775 21.9326 12.2272 22.4453 12.5671 23.0922V23.0914Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M23.1429 30.8571C21.9046 30.8603 20.6992 30.461 19.7068 29.7209L19.6031 29.6445C18.8357 29.0507 18.2282 28.2736 17.8369 27.3857C17.6882 27.0715 17.317 26.9325 16.9988 27.0715C16.6806 27.2105 16.5303 27.5769 16.6597 27.8992C17.1418 28.992 17.8899 29.9467 18.8365 30.6747L18.9458 30.7543C20.1584 31.6591 21.6314 32.1452 23.1445 32.142C23.4997 32.142 23.7873 31.8544 23.7873 31.4992C23.7873 31.144 23.4997 30.8563 23.1445 30.8563L23.1429 30.8571Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M26.6143 13.6286C25.9272 14.1429 25.0714 14.7857 25.0714 16.0714C25.0714 16.4266 25.3591 16.7143 25.7143 16.7143C26.0694 16.7143 26.3571 16.4266 26.3571 16.0714C26.3571 15.4583 26.7284 15.1489 27.3857 14.6571C28.0727 14.1429 28.9286 13.5 28.9286 12.2143C28.9286 10.9286 28.0727 10.2857 27.3857 9.77143C26.7284 9.27804 26.3571 8.96866 26.3571 8.35715C26.3571 7.74563 26.7284 7.43625 27.3857 6.94286C28.0727 6.42858 28.9286 5.78572 28.9286 4.5C28.9286 4.14483 28.6409 3.85715 28.2857 3.85715C27.9305 3.85715 27.6428 4.14483 27.6428 4.5C27.6428 5.11233 27.2716 5.4209 26.6143 5.91429C25.9272 6.42858 25.0714 7.07143 25.0714 8.35715C25.0714 9.64286 25.9272 10.2857 26.6143 10.8C27.2716 11.2926 27.6428 11.602 27.6428 12.2143C27.6428 12.8266 27.2716 13.1352 26.6143 13.6286Z"
        fill={ appConfig.color.primary }
      />
      <path
        d="M30.4714 10.8C31.1287 11.2926 31.5 11.602 31.5 12.2143C31.5 12.8266 31.1287 13.136 30.4714 13.6286C29.7843 14.1429 28.9285 14.7857 28.9285 16.0714C28.9285 16.4266 29.2162 16.7143 29.5714 16.7143C29.9266 16.7143 30.2142 16.4266 30.2142 16.0714C30.2142 15.4583 30.5855 15.1489 31.2428 14.6571C31.9299 14.1429 32.7857 13.5 32.7857 12.2143C32.7857 10.9286 31.9299 10.2857 31.2428 9.77143C30.5855 9.27804 30.2142 8.96866 30.2142 8.35715C30.2142 7.74563 30.5855 7.43625 31.2428 6.94286C31.9299 6.42858 32.7857 5.78572 32.7857 4.5C32.7857 4.14483 32.498 3.85715 32.1428 3.85715C31.7876 3.85715 31.5 4.14483 31.5 4.5C31.5 5.11233 31.1287 5.4209 30.4714 5.91429C29.7843 6.42858 28.9285 7.07143 28.9285 8.35715C28.9285 9.64286 29.7843 10.2857 30.4714 10.8Z"
        fill={ appConfig.color.primary }
      />
    </svg>
  );
};

export const MenuActionShip = () => {
  const appConfig = useConfigApp();
  return (
    <svg
      width="74"
      height="59"
      viewBox="0 0 74 59"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M72.15 40.5625H70.3V28.1057C70.3 26.6422 69.7103 25.2363 68.6697 24.1992L57.1188 12.6873C56.0781 11.6502 54.6675 11.0625 53.1991 11.0625H48.1V5.53125C48.1 2.47754 45.6141 0 42.55 0H12.95C9.88594 0 7.4 2.47754 7.4 5.53125V11.0625H0.925C0.41625 11.0625 0 11.4773 0 11.9844V13.8281C0 14.3352 0.41625 14.75 0.925 14.75H32.375C32.8838 14.75 33.3 15.1648 33.3 15.6719V17.5156C33.3 18.0227 32.8838 18.4375 32.375 18.4375H4.625C4.11625 18.4375 3.7 18.8523 3.7 19.3594V21.2031C3.7 21.7102 4.11625 22.125 4.625 22.125H28.675C29.1838 22.125 29.6 22.5398 29.6 23.0469V24.8906C29.6 25.3977 29.1838 25.8125 28.675 25.8125H0.925C0.41625 25.8125 0 26.2273 0 26.7344V28.5781C0 29.0852 0.41625 29.5 0.925 29.5H24.975C25.4837 29.5 25.9 29.9148 25.9 30.4219V32.2656C25.9 32.7727 25.4837 33.1875 24.975 33.1875H7.4V47.9375C7.4 54.0449 12.3719 59 18.5 59C24.6281 59 29.6 54.0449 29.6 47.9375H44.4C44.4 54.0449 49.3719 59 55.5 59C61.6281 59 66.6 54.0449 66.6 47.9375H72.15C73.1675 47.9375 74 47.1078 74 46.0938V42.4062C74 41.3922 73.1675 40.5625 72.15 40.5625ZM18.5 53.4688C15.4359 53.4688 12.95 50.9912 12.95 47.9375C12.95 44.8838 15.4359 42.4062 18.5 42.4062C21.5641 42.4062 24.05 44.8838 24.05 47.9375C24.05 50.9912 21.5641 53.4688 18.5 53.4688ZM55.5 53.4688C52.4359 53.4688 49.95 50.9912 49.95 47.9375C49.95 44.8838 52.4359 42.4062 55.5 42.4062C58.5641 42.4062 61.05 44.8838 61.05 47.9375C61.05 50.9912 58.5641 53.4688 55.5 53.4688ZM64.75 29.5H48.1V16.5938H53.1991L64.75 28.1057V29.5Z"
        fill={appConfig.color.primary}
      />
    </svg>
  );
};
