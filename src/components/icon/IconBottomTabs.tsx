import React from "react";
import { IconCustomProps } from "@/components/icon/AddToCartIcon";
import { useConfigApp } from "@/hooks/useConfigApp";

export const HomeIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  const appConfig = useConfigApp();
  return (
    <svg
      className={className}
      width="22"
      height="22"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.275 3.54996L4.53747 8.79996C3.41247 9.67496 2.49997 11.5375 2.49997 12.95V22.2125C2.49997 25.1125 4.86247 27.4875 7.76247 27.4875H22.2375C25.1375 27.4875 27.5 25.1125 27.5 22.225V13.125C27.5 11.6125 26.4875 9.67496 25.25 8.81246L17.525 3.39996C15.775 2.17496 12.9625 2.23746 11.275 3.54996Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 22.4875V18.7375"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const VoucherIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  const appConfig = useConfigApp();
  return (
    <svg
      className={className}
      width="22"
      height="22"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24.375 15.625C24.375 13.9 25.775 12.5 27.5 12.5V11.25C27.5 6.25 26.25 5 21.25 5H8.74998C3.74998 5 2.49998 6.25 2.49998 11.25V11.875C4.22498 11.875 5.62498 13.275 5.62498 15C5.62498 16.725 4.22498 18.125 2.49998 18.125V18.75C2.49998 23.75 3.74998 25 8.74998 25H21.25C26.25 25 27.5 23.75 27.5 18.75C25.775 18.75 24.375 17.35 24.375 15.625Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 5L12.5 25"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeDasharray="5 5"
      />
    </svg>
  );
};

export const AffiliateIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  const appConfig = useConfigApp();
  return (
    <svg width="22" height="22" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M27.5 10.65V4.975C27.5 3.2125 26.7 2.5 24.7125 2.5H19.6625C17.675 2.5 16.875 3.2125 16.875 4.975V10.6375C16.875 12.4125 17.675 13.1125 19.6625 13.1125H24.7125C26.7 13.125 27.5 12.4125 27.5 10.65Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M27.5 24.7125V19.6625C27.5 17.675 26.7 16.875 24.7125 16.875H19.6625C17.675 16.875 16.875 17.675 16.875 19.6625V24.7125C16.875 26.7 17.675 27.5 19.6625 27.5H24.7125C26.7 27.5 27.5 26.7 27.5 24.7125Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.125 10.65V4.975C13.125 3.2125 12.325 2.5 10.3375 2.5H5.28748C3.29998 2.5 2.49998 3.2125 2.49998 4.975V10.6375C2.49998 12.4125 3.29998 13.1125 5.28748 13.1125H10.3375C12.325 13.125 13.125 12.4125 13.125 10.65Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.125 24.7125V19.6625C13.125 17.675 12.325 16.875 10.3375 16.875H5.28748C3.29998 16.875 2.49998 17.675 2.49998 19.6625V24.7125C2.49998 26.7 3.29998 27.5 5.28748 27.5H10.3375C12.325 27.5 13.125 26.7 13.125 24.7125Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CartIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  const appConfig = useConfigApp();
  return (
    <svg
      className={className}
      width="22"
      height="22"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.375 9.58739V8.37489C9.375 5.56239 11.6375 2.79989 14.45 2.53739C17.8 2.21239 20.625 4.84989 20.625 8.13739V9.86239"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 27.5H18.75C23.775 27.5 24.675 25.4875 24.9375 23.0375L25.875 15.5375C26.2125 12.4875 25.3375 10 20 10H10C4.6625 10 3.7875 12.4875 4.125 15.5375L5.0625 23.0375C5.325 25.4875 6.225 27.5 11.25 27.5Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.3694 15H19.3806"
        fill={fillColor ? "currentColor" : "none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6182 15H10.6294"
        fill={fillColor ? "currentColor" : "none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ProfileIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  const appConfig = useConfigApp();
  return (
    <svg width="22" height="22" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M15 27.5C21.9035 27.5 27.5 21.9036 27.5 15C27.5 8.09644 21.9035 2.5 15 2.5C8.09641 2.5 2.49997 8.09644 2.49997 15C2.49997 21.9036 8.09641 27.5 15 27.5Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
      />
      <path
        opacity="0.2"
        d="M15 28.25C22.3177 28.25 28.25 22.3178 28.25 15C28.25 7.68223 22.3177 1.75 15 1.75C7.6822 1.75 1.74997 7.68223 1.74997 15C1.74997 22.3178 7.6822 28.25 15 28.25Z"
        fill={"none"}
        stroke={fillColor ? appConfig.color.primary : "none"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.1499 15.9751C15.0624 15.9626 14.9499 15.9626 14.8499 15.9751C12.6499 15.9001 10.8999 14.1001 10.8999 11.8876C10.8999 9.6251 12.7249 7.7876 14.9999 7.7876C17.2624 7.7876 19.0999 9.6251 19.0999 11.8876C19.0874 14.1001 17.3499 15.9001 15.1499 15.9751Z"
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.2"
        d="M15.1499 15.9751C15.0624 15.9626 14.9499 15.9626 14.8499 15.9751C12.6499 15.9001 10.8999 14.1001 10.8999 11.8876C10.8999 9.6251 12.7249 7.7876 14.9999 7.7876C17.2624 7.7876 19.0999 9.6251 19.0999 11.8876C19.0874 14.1001 17.3499 15.9001 15.1499 15.9751Z"
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M23.425 24.225C21.2 26.2625 18.25 27.5 15 27.5C11.75 27.5 8.80001 26.2625 6.57501 24.225C6.70001 23.05 7.45001 21.9 8.78751 21C12.2125 18.725 17.8125 18.725 21.2125 21C22.55 21.9 23.3 23.05 23.425 24.225Z"
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.2"
        d="M23.425 24.225C21.2 26.2625 18.25 27.5 15 27.5C11.75 27.5 8.80001 26.2625 6.57501 24.225C6.70001 23.05 7.45001 21.9 8.78751 21C12.2125 18.725 17.8125 18.725 21.2125 21C22.55 21.9 23.3 23.05 23.425 24.225Z"
        stroke={fillColor ? appConfig.color.primary : "#B1B1B1"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const BookArchiveIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  const appConfig = useConfigApp();
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M2.70815 29.4497C1.96854 29.4497 1.35149 29.2024 0.856987 28.7079C0.362487 28.2134 0.114703 27.5964 0.113632 26.8568V3.14327C0.113632 2.40473 0.361417 1.78821 0.856987 1.29371C1.35256 0.79921 1.96961 0.551424 2.70815 0.550354H19.998C20.7365 0.550354 21.3536 0.798139 21.8492 1.29371C22.3447 1.78928 22.592 2.40633 22.5909 3.14487V26.8568C22.5909 27.5953 22.3437 28.2124 21.8492 28.7079C21.3547 29.2035 20.7371 29.4508 19.9964 29.4497H2.70815ZM2.70815 27.8442H19.998C20.2442 27.8442 20.4705 27.7414 20.6771 27.5359C20.8837 27.3304 20.9865 27.1035 20.9854 26.8552V3.14487C20.9854 2.89762 20.8826 2.67071 20.6771 2.46413C20.4716 2.25756 20.2447 2.1548 19.9964 2.15587H17.7743V11.9752L14.5633 10.0598L11.3523 11.9752V2.15587H2.70815C2.4609 2.15587 2.23399 2.25863 2.02741 2.46413C1.82083 2.66964 1.71808 2.89602 1.71915 3.14327V26.8568C1.71915 27.103 1.8219 27.3293 2.02741 27.5359C2.23292 27.7425 2.4593 27.8453 2.70655 27.8442"
        fill={fillColor ? appConfig.color.primary : "#B1B1B1"}
      />
    </svg>
  );
};
