import React from "react";
export interface IconCustomProps {
  primaryColor?: string;
  secondaryColor?: string;
  className?: string;
}
const CheckIcon: React.FC<IconCustomProps> = ({
  primaryColor,
  secondaryColor,
  className,
}) => {
  return (
    <svg
      className={className}
      width="74"
      height="74"
      viewBox="0 0 74 74"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M37 12C23.25 12 12 23.25 12 37C12 50.75 23.25 62 37 62C50.75 62 62 50.75 62 37C62 23.25 50.75 12 37 12ZM32 49.5L19.5 37L23.025 33.475L32 42.425L50.975 23.45L54.5 27L32 49.5Z"
        fill={primaryColor}
      />
    </svg>
  );
};

export default CheckIcon;
