import { AddShoppingCart } from "@mui/icons-material";
import { Box } from "@mui/material";
import React from "react";

export interface IconCustomProps {
  fillColor?: string;
  secondaryColor?: string;
  className?: string;
}

const AddToCartIcon: React.FC<IconCustomProps> = ({ fillColor, secondaryColor }) => {
  return (
    <Box
      sx={{
        borderRadius: 999,
        width: "20px",
        height: "22px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        transition: "background-color 0.3s ease",
        "&:hover": {
          backgroundColor: secondaryColor,
          cursor: "pointer",
        },
      }}
    >
      <AddShoppingCart
        sx={{
          color: fillColor,
          width: "20px",
          height: "22px",
        }}
      />
    </Box>
    // <svg
    //   width="30"
    //   height="30"
    //   viewBox="0 0 30 30"
    //   fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
    // >
    //   <circle cx="15" cy="15" r="15" fill={secondaryColor} />
    //   <path
    //     d="M9 15H21"
    //     stroke={fillColor}
    //     strokeWidth="1.5"
    //     strokeLinecap="round"
    //     strokeLinejoin="round"
    //   />
    //   <path
    //     d="M15 21V9"
    //     stroke={fillColor}
    //     strokeWidth="1.5"
    //     strokeLinecap="round"
    //     strokeLinejoin="round"
    //   />
    // </svg>
  );
};

export default AddToCartIcon;
