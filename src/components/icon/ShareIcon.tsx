import React from "react";
import { IconCustomProps } from "./AddToCartIcon";
import { useConfigApp } from "@/hooks/useConfigApp";

const ShareIcon: React.FC<IconCustomProps> = ({
  fillColor,
  className,
}) => {
  const appConfig = useConfigApp();

  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.5417 11.4587L22.0834 2.91699"
        stroke={appConfig.color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9167 7.0835V2.0835H17.9167"
        stroke={appConfig.color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.4583 2.0835H9.37498C4.16665 2.0835 2.08331 4.16683 2.08331 9.37516V15.6252C2.08331 20.8335 4.16665 22.9168 9.37498 22.9168H15.625C20.8333 22.9168 22.9166 20.8335 22.9166 15.6252V13.5418"
        stroke={appConfig.color.primary}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ShareIcon;
