import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const PaymentIcon: React.FC<IconCustomProps> = ({ fillColor, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        d="M6.70834 3.65088H18.2813C21.9896 3.65088 22.9167 4.56755 22.9167 8.2238V16.7759C22.9167 20.4321 21.9896 21.3488 18.2917 21.3488H6.70834C3.01042 21.3592 2.08334 20.4425 2.08334 16.7863V8.2238C2.08334 4.56755 3.01042 3.65088 6.70834 3.65088Z"
        fill={fillColor}
        stroke={fillColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.0835 8.85938H22.9168"
        stroke="white"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.25 17.1924H8.33333"
        stroke="white"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.9375 17.1924H15.1042"
        stroke="white"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default PaymentIcon;
