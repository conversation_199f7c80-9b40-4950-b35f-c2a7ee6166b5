import * as React from "react";

const BenefitIcon: React.FC<React.SVGProps<SVGSVGElement> & { fill?: string }> = ({
  fill = "#1531AD",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="66"
    height="66"
    fill="none"
    viewBox="0 0 66 66"
    {...props}
  >
    <rect width="66" height="66" fill={fill} fillOpacity="0.08" rx="33"></rect>
    <mask id="path-2-inside-1_13498_3021" fill="#fff">
      <path d="M33 40.5q.357 0 .595-.24a.8.8 0 0 0 .238-.593v-7.5a.817.817 0 0 0-.835-.833.8.8 0 0 0-.593.24.81.81 0 0 0-.238.593v7.5q0 .354.24.593.24.238.595.24M33 28.962a1 1 0 0 0 .732-.295.99.99 0 0 0 .293-.73q-.002-.435-.295-.732a.98.98 0 0 0-.73-.295 1 1 0 0 0-.73.295 1 1 0 0 0-.295.732.98.98 0 0 0 .295.73q.297.29.73.295M33.005 48q-3.111 0-5.85-1.18-2.739-1.182-4.765-3.207t-3.208-4.76Q18 36.12 18 33.005t1.182-5.85q1.18-2.739 3.201-4.765t4.762-3.208T32.995 18t5.85 1.182q2.739 1.18 4.765 3.203t3.208 4.762T48 32.995t-1.18 5.85-3.207 4.765q-2.025 2.026-4.76 3.208Q36.12 48.002 33.005 48M33 46.333q5.583 0 9.458-3.875T46.333 33t-3.875-9.458Q38.583 19.666 33 19.667t-9.458 3.875Q19.666 27.417 19.667 33t3.875 9.458q3.875 3.875 9.458 3.875"></path>
    </mask>
    <path
      fill={fill}
      d="M33 40.5q.357 0 .595-.24a.8.8 0 0 0 .238-.593v-7.5a.817.817 0 0 0-.835-.833.8.8 0 0 0-.593.24.81.81 0 0 0-.238.593v7.5q0 .354.24.593.24.238.595.24M33 28.962a1 1 0 0 0 .732-.295.99.99 0 0 0 .293-.73q-.002-.435-.295-.732a.98.98 0 0 0-.73-.295 1 1 0 0 0-.73.295 1 1 0 0 0-.295.732.98.98 0 0 0 .295.73q.297.29.73.295M33.005 48q-3.111 0-5.85-1.18-2.739-1.182-4.765-3.207t-3.208-4.76Q18 36.12 18 33.005t1.182-5.85q1.18-2.739 3.201-4.765t4.762-3.208T32.995 18t5.85 1.182q2.739 1.18 4.765 3.203t3.208 4.762T48 32.995t-1.18 5.85-3.207 4.765q-2.025 2.026-4.76 3.208Q36.12 48.002 33.005 48M33 46.333q5.583 0 9.458-3.875T46.333 33t-3.875-9.458Q38.583 19.666 33 19.667t-9.458 3.875Q19.666 27.417 19.667 33t3.875 9.458q3.875 3.875 9.458 3.875"
    ></path>
    <path
      fill={fill}
      d="M33.833 39.667h3zM33 28.962l-.023 3H33zM33.005 48l.002-3h-.002zm-5.85-1.18-1.189 2.755h.002zm-7.973-19.665 2.754 1.19v-.003zm19.663-7.973-1.188 2.754zM33 46.333v-3zm0-5.833v3c.989 0 1.968-.365 2.724-1.126l-2.129-2.114-2.129-2.114A2.2 2.2 0 0 1 33 37.5zm.595-.24 2.129 2.114a3.8 3.8 0 0 0 1.11-2.707h-6a2.198 2.198 0 0 1 .633-1.52zm.238-.593h3v-7.5h-6v7.5zm0-7.5h3c0-.992-.368-1.97-1.126-2.722l-2.114 2.128-2.114 2.129a2.2 2.2 0 0 1-.646-1.535zm-.24-.594 2.114-2.128a3.82 3.82 0 0 0-2.695-1.112l-.014 3-.014 3a2.193 2.193 0 0 1-1.505-.631zm-.595-.24.014-3a3.8 3.8 0 0 0-2.743 1.134l2.136 2.106 2.136 2.107a2.2 2.2 0 0 1-1.557.653zm-.593.24-2.136-2.106a3.8 3.8 0 0 0-1.102 2.7h6a2.19 2.19 0 0 1-.626 1.513zm-.238.594h-3v7.5h6v-7.5zm0 7.5h-3a3.8 3.8 0 0 0 1.126 2.722l2.114-2.129 2.114-2.129a2.2 2.2 0 0 1 .646 1.536zm.24.593-2.114 2.129a3.82 3.82 0 0 0 2.695 1.111l.014-3 .014-3a2.193 2.193 0 0 1 1.505.631zM33 28.962v3a4 4 0 0 0 2.853-1.174l-2.121-2.121-2.122-2.122a2.02 2.02 0 0 1 1.39-.583zm.732-.295 2.121 2.121a3.98 3.98 0 0 0 1.172-2.863l-3 .012-3 .011a2.02 2.02 0 0 1 .585-1.403zm.293-.73 3-.012a4 4 0 0 0-1.162-2.83l-2.133 2.11-2.133 2.11a2 2 0 0 1-.572-1.367zm-.295-.732 2.133-2.11c-.79-.799-1.82-1.189-2.874-1.185l.011 3 .011 3a2.03 2.03 0 0 1-1.415-.596zM33 26.91l-.011-3a4 4 0 0 0-2.84 1.174l2.121 2.121 2.121 2.121a2.02 2.02 0 0 1-1.38.584zm-.73.295-2.121-2.121a4 4 0 0 0-1.174 2.841l3 .012 3 .011a2.015 2.015 0 0 1-.584 1.378zm-.295.732-3-.012a3.97 3.97 0 0 0 1.192 2.88l2.103-2.138 2.103-2.14a2.04 2.04 0 0 1 .602 1.421zm.295.73-2.103 2.139a4 4 0 0 0 2.81 1.156l.023-3 .023-3a1.992 1.992 0 0 1 1.35.566zM33.005 48v-3c-1.684 0-3.229-.317-4.663-.935l-1.187 2.755-1.187 2.755C28.185 50.53 30.54 51 33.005 51zm-5.85-1.18 1.189-2.754c-1.493-.645-2.761-1.504-3.834-2.575l-2.12 2.122-2.12 2.123c1.63 1.628 3.538 2.907 5.696 3.839zm-4.765-3.207 2.12-2.122c-1.072-1.071-1.93-2.338-2.574-3.828l-2.754 1.19-2.754 1.19c.932 2.157 2.212 4.064 3.842 5.693zm-3.208-4.76 2.754-1.19c-.619-1.43-.936-2.973-.936-4.658h-6c0 2.466.47 4.822 1.428 7.038zM18 33.005h3c0-1.685.317-3.229.936-4.66l-2.754-1.19-2.754-1.19C15.47 28.183 15 30.54 15 33.006zm1.182-5.85 2.755 1.187c.643-1.492 1.5-2.761 2.57-3.833l-2.124-2.119-2.124-2.119c-1.626 1.63-2.902 3.538-3.832 5.697zm3.201-4.765 2.124 2.119c1.068-1.07 2.334-1.929 3.826-2.573l-1.188-2.754-1.188-2.755c-2.161.932-4.07 2.212-5.698 3.844zm4.762-3.208 1.188 2.754c1.436-.619 2.98-.936 4.662-.936v-6c-2.464 0-4.82.47-7.038 1.427zM32.995 18v3c1.682 0 3.226.317 4.662.936l1.188-2.754 1.188-2.755C37.815 15.47 35.459 15 32.995 15zm5.85 1.182-1.187 2.755c1.492.643 2.76 1.5 3.832 2.571l2.12-2.123 2.12-2.123c-1.63-1.628-3.539-2.905-5.698-3.835zm4.765 3.203-2.12 2.123c1.072 1.07 1.93 2.336 2.574 3.827l2.754-1.188 2.755-1.189c-.932-2.16-2.212-4.068-3.843-5.696zm3.208 4.762-2.754 1.188c.619 1.435.936 2.978.936 4.66h6c0-2.464-.47-4.82-1.427-7.037zM48 32.995h-3c0 1.683-.317 3.227-.935 4.663l2.755 1.187 2.755 1.187C50.53 37.814 51 35.459 51 32.995zm-1.18 5.85-2.755-1.187c-.643 1.492-1.501 2.76-2.572 3.83l2.12 2.122 2.12 2.122c1.632-1.63 2.912-3.54 3.842-5.7zm-3.207 4.765-2.12-2.122c-1.074 1.072-2.342 1.932-3.832 2.577l1.192 2.753 1.192 2.753a18.2 18.2 0 0 0 5.689-3.839zm-4.76 3.208-1.192-2.753c-1.428.619-2.97.936-4.654.935l-.002 3-.002 3c2.468.001 4.826-.47 7.042-1.429zM33 46.333v3c4.501 0 8.422-1.596 11.58-4.753l-2.122-2.122-2.121-2.121c-2.009 2.009-4.394 2.996-7.337 2.996zm9.458-3.875 2.122 2.122c3.157-3.158 4.753-7.079 4.753-11.58h-6c0 2.943-.987 5.328-2.996 7.337zM46.333 33h3c0-4.501-1.596-8.422-4.753-11.58l-2.122 2.122-2.121 2.121c2.009 2.009 2.996 4.394 2.996 7.337zm-3.875-9.458 2.122-2.122C41.422 18.263 37.5 16.667 33 16.667v6c2.943 0 5.328.987 7.337 2.996zM33 19.667v-3c-4.501 0-8.422 1.596-11.58 4.753l2.122 2.122 2.121 2.121c2.009-2.009 4.394-2.996 7.337-2.996zm-9.458 3.875L21.42 21.42C18.263 24.578 16.667 28.5 16.667 33h6c0-2.943.987-5.328 2.996-7.337zM19.667 33h-3c0 4.501 1.596 8.422 4.753 11.58l2.122-2.122 2.121-2.121c-2.009-2.009-2.996-4.394-2.996-7.337zm3.875 9.458L21.42 44.58c3.158 3.157 7.079 4.753 11.58 4.753v-6c-2.943 0-5.328-.987-7.337-2.996z"
      mask="url(#path-2-inside-1_13498_3021)"
    ></path>
  </svg>
);

export default BenefitIcon;
