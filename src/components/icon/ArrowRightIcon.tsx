import React from "react";
import { IconCustomProps } from "./AddToCartIcon";

const ArrowRightIcon: React.FC<IconCustomProps> = ({
  fillColor,
  className,
}) => {
  return (
    <svg
      className={className}
      width="13"
      height="21"
      viewBox="0 0 13 21"
      fill='none'
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.35929 18.8811L10.1907 10.8362L2.57855 2.5535"
        stroke={fillColor}
        strokeWidth="4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ArrowRightIcon;
