import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import { Router } from "@/constants/Route";
import { useNavigate } from "react-router-dom";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import { Platform } from "@/config";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import LoginPopup from "@/components/LoginPopup";

export default function GameBox() {
  const navigate = useNavigate();
  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();
  const { thumbnailLink } = useSelector((state: RootState) => state.gamification);
  const [position, setPosition] = useState({
    right: "20px",
    bottom: Platform === "zalo" ? 130 : 70,
  });

  const calculatePosition = () => {
    const isMobile = window.innerWidth <= 450;

    if (isMobile) {
      setPosition({
        right: "20px",
        bottom: Platform === "zalo" ? 110 : 70,
      });
    } else {
      // For desktop, calculate position relative to centered app container
      const appWidth = 450;
      const screenWidth = window.innerWidth;
      const appRight = (screenWidth - appWidth) / 2;
      const rightPosition = appRight + 20;

      setPosition({
        right: `${rightPosition}px`,
        bottom: Platform === "zalo" ? 110 : 70,
      });
    }
  };

  useEffect(() => {
    calculatePosition();

    const handleResize = () => {
      calculatePosition();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <>
      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
      <Box
        sx={{
          position: "fixed",
          right: position.right,
          bottom: position.bottom,
          zIndex: 1000,
        }}
      >
        {thumbnailLink && (
          <img
            onClick={() => checkLogin(() => navigate(Router.game))}
            src={thumbnailLink}
            alt="Game"
            style={{
              width: 70,
              height: 70,
              objectFit: "cover",
              animation: "shake 2s infinite",
              borderRadius: 10,
            }}
          />
        )}
      </Box>
    </>
  );
}
