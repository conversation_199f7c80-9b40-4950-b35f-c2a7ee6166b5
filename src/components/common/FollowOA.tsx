import CheckIcon from "@/components/icon/CheckIcon";
import { Platform } from "@/config";
import { Router } from "@/constants/Route";
import { StorageKeys } from "@/constants/storageKeys";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo, updateMe } from "@/redux/slices/authen/authSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { mapError } from "@/utils/common";
import { useNavigate } from "@/utils/component-util";
import { handleFollowOA } from "@/utils/followOA";
import { getItem } from "@/utils/storage";
import { FavoriteBorder, Star } from "@mui/icons-material";
import { Box, Button, Stack, Typography, useTheme } from "@mui/material";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";

export default function FollowOA() {
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const appConfig = useConfigApp();

  const { showAlert } = useAlert();

  const { user } = useSelector((state: RootState) => state.auth);

  const [checkActiveLoading, setActiveLoading] = useState(false);

  const onClickFollowOA = () => {
    if (!user) {
      showAlert({
        icon: (
          <CheckIcon
            primaryColor={appConfig.color.primary}
            secondaryColor={appConfig.bgColor.primary}
          />
        ),
        title: `Cảm ơn bạn đã quan tâm OA ${appConfig?.shopName}`,
        content: `Vui lòng kích hoạt tài khoản để tiếp tục.`,
        buttons: [
          {
            title: "Đóng",
          },
          {
            title: "Kích hoạt",
            action: async () => {
              await onClickRegister(() =>
                handleFollowOA(appConfig.oaId || "", onUpdateAfterFollowOA)
              );
            },
          },
        ],
      });
    } else {
      handleFollowOA(appConfig.oaId || "", onUpdateAfterFollowOA);
    }
  };

  const onUpdateAfterFollowOA = async () => {
    const res = await dispatch(updateMe({ isZaloOA: true })).unwrap();
    if (res) {
      showAlert({
        icon: (
          <CheckIcon
            primaryColor={appConfig.color.primary}
            secondaryColor={appConfig.color.secondary}
          />
        ),
        title: `Cảm ơn bạn đã quan tâm OA ${appConfig?.shopName}`,
        content: `Mời bạn mua sắm và trải nghiệm dịch vụ của ${appConfig?.shopName}.`,
        buttons: [
          {
            title: "Xem sản phẩm",
            action: () => {
              navigate(Router.menu);
            },
          },
        ],
      });
      dispatch(getUser());
    }
  };

  const onClickRegister = async (onFollowOA?: () => Promise<void>) => {
    setActiveLoading(true);
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(authZalo(refCode)).unwrap();
    if (res && res.idFor) {
      if (Platform == "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      await calcCartAfterLogin();

      showAlert({
        icon: (
          <CheckIcon
            primaryColor={appConfig.color.primary}
            secondaryColor={appConfig.color.secondary}
          />
        ),
        title: "Kích hoạt tài khoản thành công",
        buttons: [
          {
            title: "OK",
            action: onFollowOA,
          },
        ],
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setActiveLoading(false);
  };

  return (
    <Box
      sx={{
        background: `linear-gradient(135deg, ${appConfig.color.secondary} 0%, ${appConfig.color.primary} 100%)`,
        borderRadius: 2,
        p: { xs: 1.5, sm: 1.5 },
        minWidth: 0,
        width: "100%",
        minHeight: 40,
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Decorative circles */}
      <Box
        sx={{
          position: "absolute",
          top: -24,
          left: -24,
          width: 48,
          height: 48,
          backgroundColor: "rgba(255,255,255,0.10)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <Box
        sx={{
          position: "absolute",
          bottom: -16,
          right: -16,
          width: 32,
          height: 32,
          backgroundColor: "rgba(255,255,255,0.10)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <Stack sx={{ width: "100%", position: "relative", zIndex: 1 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 0.5 }}>
          <Star sx={{ color: "#ffeb3b", fontSize: 18, mr: 0.5 }} />
          <Box
            sx={{
              backgroundColor: "rgba(255,255,255,0.18)",
              color: "white",
              fontWeight: "bold",
              fontSize: 10,
              borderRadius: 1,
              px: 1,
              py: 0.1,
              display: "inline-block",
            }}
          >
            Độc quyền
          </Box>
        </Box>
        <Typography
          variant="subtitle1"
          component="h2"
          sx={{
            fontWeight: "bold",
            mb: 0.2,
            fontSize: { xs: "0.95rem", sm: "1.1rem" },
            color: "white",
          }}
        >
          Nhận ưu đãi đặc quyền
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: "rgba(255,255,255,0.85)",
            mb: 0.3,
            lineHeight: 1.3,
            fontSize: { xs: 11, sm: 12 },
          }}
        >
          {`Quan tâm OA để không bỏ lỡ chương trình từ ${appConfig?.shopName}`}
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mt: 1 }}>
          <Stack direction="row" alignItems="center" gap={0.5}>
            <Box
              sx={{
                p: 0.7,
                borderRadius: "50%",
                bgcolor: appConfig.color.primary,
                width: 28,
                height: 28,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <img
                width={20}
                src={
                  typeof appConfig.shopLogo === "string" && appConfig.shopLogo
                    ? appConfig.shopLogo
                    : appConfig.shopLogo?.link || "/images/logo.png"
                }
                style={{ maxHeight: 20, objectFit: "cover" }}
              />
            </Box>
            <Stack sx={{ fontSize: 13, color: appConfig.color.primary }}>
              <span style={{ fontWeight: 700, color: "white" }}>{appConfig?.shopName}</span>
            </Stack>
          </Stack>
          <Button
            variant="contained"
            size="small"
            startIcon={<FavoriteBorder sx={{ color: appConfig.color.primary, fontSize: 16 }} />}
            sx={{
              background: "white",
              color: appConfig.color.primary,
              borderRadius: 2,
              fontSize: 12,
              minWidth: 90,
              fontWeight: "bold",
              minHeight: 24,
              boxShadow: 2,
              py: 0.7,
              "&:hover": {
                backgroundColor: "#fce4ec",
                transform: "scale(1.02)",
                boxShadow: 4,
              },
              transition: "all 0.2s ease-in-out",
            }}
            onClick={onClickFollowOA}
          >
            Quan tâm
          </Button>
        </Box>
      </Stack>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  OAContainer: {
    background: "#FFF9ED",
    fontWeight: 700,
    gap: 1,
    padding: 2,
    alignItems: "center",
    justifyItems: "baseline",
    justifyContent: "space-between",
    border: 1,
    borderRadius: 1,
  },
  OAContent: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 1,
    width: "100%",
  },
  OABtn: {
    borderRadius: 2,
    fontSize: 12,
    width: 100,
  },
};
