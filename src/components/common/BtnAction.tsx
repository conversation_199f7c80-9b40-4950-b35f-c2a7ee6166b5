import { Button, Stack } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { COLOR, COLORS } from "@/constants/themes";

export default function BtnAction({
  text,
  eventAction,
  icon,
  styleOverride,
  textColor,
}: {
  text: string;
  icon?: ReactNode;
  eventAction?: () => void;
  styleOverride?: object;
  textColor?: string;
}) {
  return (
    <Button
      style={{ ...styles.seeMoreBtn, color: textColor || COLOR.text.seeall, ...styleOverride }}
      onClick={() => (eventAction ? eventAction() : null)}
    >
      {text}
      {icon && <Stack style={styles.seeMoreIcon}>{icon}</Stack>}
    </Button>
  );
}

const styles: Record<string, React.CSSProperties> = {
  seeMoreBtn: {
    display: "flex",
    alignItems: "center",
    fontSize: 15,
    fontWeight: 400,
    color: COLOR.text.seeall,
    paddingRight: 0,
  },
  seeMoreIcon: {
    marginLeft: 4,
    width: 12,
    height: 12,
  },
};
