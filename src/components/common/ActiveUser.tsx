import CheckIcon from "@/components/icon/CheckIcon";
import { Platform } from "@/config";
import { StorageKeys } from "@/constants/storageKeys";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo } from "@/redux/slices/authen/authSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { mapError } from "@/utils/common";
import { useNavigate } from "@/utils/component-util";
import { getItem } from "@/utils/storage";
import { AutoAwesome, CardGiftcard } from "@mui/icons-material";
import { Box, Button, CircularProgress, Stack, Typography, useTheme } from "@mui/material";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import LoginPopup from "../LoginPopup";

export default function ActiveUser({ item }: { item?: { description?: string } }) {
  const theme = useTheme();
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const appConfig = useConfigApp();

  const { showAlert } = useAlert();
  const { user } = useSelector((state: RootState) => state.auth);

  const [checkActiveLoading, setActiveLoading] = useState(false);
  const [openLoginPopup, setOpenLoginPopup] = useState(false);

  const onClickRegister = async (onFollowOA?: () => Promise<void>) => {
    setActiveLoading(true);
    const refCode = await getItem(StorageKeys.RefCode);
    if (Platform === "zalo") {
      const res = await dispatch(authZalo(refCode)).unwrap();
      if (res && res.idFor) {
        if (Platform == "zalo") await dispatch(getUserZalo());
        await dispatch(getUser());
        await calcCartAfterLogin();

        showAlert({
          icon: (
            <CheckIcon
              primaryColor={appConfig.color.primary}
              secondaryColor={appConfig.color.secondary}
            />
          ),
          title: "Kích hoạt tài khoản thành công",
          buttons: [
            {
              title: "OK",
              action: onFollowOA,
            },
          ],
        });
      } else {
        showAlert({
          content: mapError(res.error),
        });
      }
    } else {
      setOpenLoginPopup(true);
    }

    setActiveLoading(false);
  };

  return (
    <Box
      sx={{
        background: `linear-gradient(135deg, ${appConfig.color.primary} 0%, ${appConfig.color.accent} 100%)`,
        border: "1px solid " + appConfig.color.accent,
        position: "relative",
        overflow: "hidden",
        borderRadius: 2,
        p: { xs: 1.5, sm: 1.5 },
        minWidth: 0,
        width: "100%",
        minHeight: 40,
      }}
    >
      {/* Decorative circles */}
      <Box
        sx={{
          position: "absolute",
          top: -24,
          right: -24,
          width: 48,
          height: 48,
          backgroundColor: "rgba(255,255,255,0.10)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <Box
        sx={{
          position: "absolute",
          bottom: -16,
          left: -16,
          width: 32,
          height: 32,
          backgroundColor: "rgba(255,255,255,0.10)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />
      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={checkActiveLoading}
      />
      <Stack
        sx={{
          width: "100%",
          opacity: checkActiveLoading ? 0.5 : 1,
          position: "relative",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 0.5 }}>
          <AutoAwesome sx={{ color: "#ffeb3b", fontSize: 18, mr: 0.5 }} />
          <Box
            sx={{
              backgroundColor: "rgba(255,255,255,0.18)",
              color: "white",
              fontWeight: "bold",
              fontSize: 10,
              borderRadius: 1,
              px: 1,
              py: 0.1,
              display: "inline-block",
            }}
          >
            Đặc biệt
          </Box>
        </Box>
        <Typography
          variant="subtitle1"
          component="h2"
          sx={{
            fontWeight: "bold",
            mb: 0.2,
            fontSize: { xs: "0.95rem", sm: "1.1rem" },
            color: "white",
          }}
        >
          Kích hoạt tài khoản
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: "rgba(255,255,255,0.85)",
            mb: 0.3,
            lineHeight: 1.3,
            fontSize: { xs: 11, sm: 12 },
          }}
        >
          {item?.description || `Nhận nhiều ưu đãi đến từ ${appConfig?.shopName}`}
        </Typography>
        <Button
          variant="contained"
          fullWidth
          size="small"
          startIcon={<CardGiftcard sx={{ color: appConfig.color.primary, fontSize: 16 }} />}
          sx={{
            backgroundColor: "white",
            color: appConfig.color.primary,
            fontWeight: "bold",
            py: 0.7,
            borderRadius: 2,
            fontSize: 12,
            mt: 0.5,
            minHeight: 24,
            "&:hover": {
              backgroundColor: "#fce4ec",
              transform: "scale(1.02)",
            },
            transition: "all 0.2s ease-in-out",
          }}
          onClick={() => onClickRegister()}
          disabled={checkActiveLoading}
        >
          {checkActiveLoading ? (
            <>
              <CircularProgress
                size={12}
                style={{ color: appConfig.color.primary, marginRight: 4 }}
              />
              Đang kích hoạt
            </>
          ) : (
            "Kích hoạt ngay"
          )}
        </Button>
      </Stack>
      {checkActiveLoading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            background: "rgba(255,255,255,0.08)",
            zIndex: 2,
          }}
        ></Box>
      )}
    </Box>
  );
}
