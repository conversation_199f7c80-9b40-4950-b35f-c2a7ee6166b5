import { Box } from "@mui/material";
import React, { useState, useRef, useEffect } from "react";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Platform } from "@/config";
import { minimizeApp, showToast as zmpShowToast, getSystemInfo } from "zmp-sdk/apis";
import { useShareReferLink } from "@/hooks/useShareReferLink";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import LoginPopup from "@/components/LoginPopup";
import PopUpShareLink from "@/components/UI/PopUpShareLink";
import { openChat } from "@/utils/openChat";
import { showToast } from "@/utils/common";
import { addToFavorite } from "@/utils/favorite";
import { Router } from "@/constants/Route";
import { useNavigate } from "react-router-dom";
import { keyComponent } from "@/types/home";

// Import SVG paths từ public folder
const messageIcon = "/static/message-text-sticky.svg";
const shareIcon = "/static/share-sticky.svg";
const minimizeIcon = "/static/minimize-icon.svg";
const favoriteIcon = "/static/favorite-sticky.svg";
const gameIcon = "/static/gamepad.svg";

// Các hằng số cho vị trí an toàn
const BOTTOM_MENU_BAR_HEIGHT = 80; // Chiều cao của bottom navigation
const SAFE_AREA_TOP = 40; // Khoảng cách an toàn cho tai thỏ
const EDGE_MARGIN = 10; // Khoảng cách tối thiểu giữa nút và cạnh màn hình
const BUTTON_SIZE = 48; // Kích thước của nút

interface StickyButtonsProps {
  showChat?: boolean;
  showShare?: boolean;
  showMinimize?: boolean;
  showFavorite?: boolean;
  customButtons?: Array<{
    id: string;
    icon: string;
    alt: string;
    onClick: () => void;
  }>;
}

const StickyButtons = ({
  showChat = true,
  showShare = true,
  showMinimize = true,
  showFavorite = true,
  customButtons = [],
}: StickyButtonsProps) => {
  const appConfig = useConfigApp();
  const { home } = useConfigApp();
  const { shareLink } = useShareReferLink();
  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();

  if (Platform !== "zalo") return null;

  // Check if Game3 is enabled
  const showGame = home?.some((item) => item.type === keyComponent.Game3 && item.show);

  const buttonRef = useRef<HTMLDivElement>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isPositionAbsolute, setIsPositionAbsolute] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [isOpenShareLink, setIsOpenShareLink] = useState(false);
  const navigate = useNavigate();

  const menuConfig = {
    radius: 80,
    baseDelay: 100,
  };

  // Tham khảo từ code practice app
  const calculateAngles = (itemCount: number, direction = "left") => {
    let startAngle, endAngle;

    switch (direction) {
      case "right":
        startAngle = 90;
        endAngle = 270;
        break;
      case "left":
        startAngle = 90;
        endAngle = 270;
        break;
      case "up":
        startAngle = 180;
        endAngle = 360;
        break;
      case "down":
        startAngle = 0;
        endAngle = 180;
        break;
      default:
        startAngle = 90;
        endAngle = 270;
        break;
    }

    if (itemCount === 1) {
      const midAngle = (startAngle + endAngle) / 2;
      return [midAngle >= 360 ? midAngle - 360 : midAngle];
    }

    let totalSpan;
    if (direction === "up" && endAngle === 360) {
      totalSpan = 180;
    } else if (direction === "down" && endAngle === 180) {
      totalSpan = 180;
    } else {
      totalSpan = Math.abs(endAngle - startAngle);
    }

    const angleStep = totalSpan / (itemCount - 1);

    const angles = Array.from({ length: itemCount }, (_, index) => {
      let angle;

      if (direction === "down") {
        angle = startAngle + angleStep * index;
        if (angle >= 360) angle -= 360;
      } else {
        angle = startAngle + angleStep * index;
        if (angle >= 360) angle -= 360;
      }

      return angle;
    });

    return angles;
  };

  // Determine which buttons to show
  const defaultButtons = [
    ...(showMinimize
      ? [
          {
            id: "minimize",
            icon: minimizeIcon,
            alt: "Thu nhỏ app",
          },
        ]
      : []),
    ...(showChat
      ? [
          {
            id: "chat",
            icon: messageIcon,
            alt: "Chat",
          },
        ]
      : []),
    ...(showGame
      ? [
          {
            id: "game",
            icon: gameIcon,
            alt: "Game",
          },
        ]
      : []),
    ...(showFavorite
      ? [
          {
            id: "favorite",
            icon: favoriteIcon,
            alt: "Yêu thích",
          },
        ]
      : []),
    ...(showShare
      ? [
          {
            id: "share",
            icon: shareIcon,
            alt: "Chia sẻ",
          },
        ]
      : []),
  ];

  const menuItems = [...defaultButtons, ...customButtons];

  useEffect(() => {
    const handleMove = (x: number, y: number) => {
      if (!isDragging) return;
      if (!buttonRef.current) return;

      const screenWidth = window.innerWidth || document.documentElement.clientWidth;
      const screenHeight = window.innerHeight || document.documentElement.clientHeight;

      const rect = buttonRef.current.getBoundingClientRect();
      const buttonWidth = rect.width;
      const buttonHeight = rect.height;

      let newLeft = x - offset.x;
      let newTop = y - offset.y;

      // Giới hạn để không đi quá bottom bar
      const maxBottomPosition = screenHeight - BOTTOM_MENU_BAR_HEIGHT - buttonHeight - EDGE_MARGIN;
      // Giới hạn phía trên để tránh tai thỏ
      const minTopPosition = SAFE_AREA_TOP;

      // Giới hạn left/right
      const minLeft = EDGE_MARGIN;
      const maxLeft = screenWidth - buttonWidth - EDGE_MARGIN;

      // Áp dụng giới hạn
      newLeft = Math.max(minLeft, Math.min(maxLeft, newLeft));
      newTop = Math.max(minTopPosition, Math.min(maxBottomPosition, newTop));

      setPosition({ top: newTop, left: newLeft });
    };

    const handleEnd = () => {
      if (!isDragging) return;
      if (!buttonRef.current) return;

      setIsDragging(false);

      const screenWidth = window.innerWidth || document.documentElement.clientWidth;
      const screenHeight = window.innerHeight || document.documentElement.clientHeight;

      const rect = buttonRef.current.getBoundingClientRect();
      const buttonWidth = rect.width;
      const buttonHeight = rect.height;

      // Giới hạn để không đi quá bottom bar
      const maxBottomPosition = screenHeight - BOTTOM_MENU_BAR_HEIGHT - buttonHeight - EDGE_MARGIN;

      const distances = {
        left: rect.left - EDGE_MARGIN,
        right: screenWidth - EDGE_MARGIN - rect.right,
        top: rect.top - SAFE_AREA_TOP,
        bottom: maxBottomPosition - rect.top,
      };

      const nearestEdge = Object.entries(distances).sort((a, b) => a[1] - b[1])[0][0];

      let newPosition = { ...position };

      if (nearestEdge === "left") {
        newPosition.left = EDGE_MARGIN;
      } else if (nearestEdge === "right") {
        newPosition.left = screenWidth - buttonWidth - EDGE_MARGIN;
      } else if (nearestEdge === "top") {
        newPosition.top = SAFE_AREA_TOP;
      } else if (nearestEdge === "bottom") {
        newPosition.top = maxBottomPosition;
      }

      setPosition(newPosition);
    };

    const handleMouseMove = (e: MouseEvent) => handleMove(e.clientX, e.clientY);
    const handleMouseUp = handleEnd;

    const handleTouchMove = (e: TouchEvent) => {
      const touch = e.touches[0];
      handleMove(touch.clientX, touch.clientY);
    };
    const handleTouchEnd = handleEnd;

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    document.addEventListener("touchmove", handleTouchMove);
    document.addEventListener("touchend", handleTouchEnd);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isDragging, offset, position]);

  const startDrag = (x: number, y: number) => {
    if (isMenuOpen) return;
    if (!buttonRef.current) return;

    const rect = buttonRef.current.getBoundingClientRect();
    setOffset({ x: x - rect.left, y: y - rect.top });
    setIsDragging(true);
    setIsMenuOpen(false);

    // Chuyển sang vị trí tuyệt đối khi bắt đầu kéo
    setIsPositionAbsolute(true);
    setPosition({ top: rect.top, left: rect.left });
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    startDrag(e.clientX, e.clientY);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    startDrag(touch.clientX, touch.clientY);
  };

  const toggleMenu = () => {
    if (!isDragging) {
      setIsMenuOpen(!isMenuOpen);
    }
  };

  const handleMenuItemClick = async (action: string) => {
    setIsMenuOpen(false);

    // Handle custom buttons
    const customButton = customButtons.find((btn) => btn.id === action);
    if (customButton) {
      customButton.onClick();
      return;
    }

    // Handle default buttons
    if (action === "share") {
      setIsOpenShareLink(true);
    } else if (action === "chat") {
      // Sử dụng hàm openChat từ utils
      if (appConfig.oaId) {
        openChat(appConfig.oaId);
      } else if (appConfig.supportUrl) {
        window.open(appConfig.supportUrl, "_blank");
      } else if (appConfig.supportEmail) {
        window.location.href = `mailto:${appConfig.supportEmail}`;
      } else {
        showToast({
          content: "Không thể kết nối với hỗ trợ",
          type: "error",
        });
      }
    } else if (action === "minimize" && Platform === "zalo") {
      try {
        await minimizeApp();
      } catch (error) {
        showToast({
          content: "Không thể thu nhỏ ứng dụng. Vui lòng thử lại.",
          type: "error",
        });
      }
    } else if (action === "favorite") {
      await addToFavorite();
    } else if (action === "game") {
      checkLogin(() => navigate(Router.game));
    }
  };

  // Cập nhật logic xác định hướng của menu dựa theo mã tham khảo
  const getMenuDirection = () => {
    if (!buttonRef.current) return "left";

    const screenWidth = window.innerWidth || document.documentElement.clientWidth;
    const screenHeight = window.innerHeight || document.documentElement.clientHeight;

    const buttonRect = buttonRef.current.getBoundingClientRect();

    const buttonCenterX = buttonRect.left + buttonRect.width / 2;
    const buttonCenterY = buttonRect.top + buttonRect.height / 2;

    // Xác định xem button đang ở gần cạnh nào của màn hình
    const isNearLeft = buttonCenterX < screenWidth * 0.3;
    const isNearRight = buttonCenterX > screenWidth * 0.7;
    const isNearTop = buttonCenterY < screenHeight * 0.3;
    const isNearBottom = buttonCenterY > screenHeight * 0.7;

    if (isNearLeft) return "right";
    if (isNearRight) return "left";
    if (isNearTop) return "down";
    if (isNearBottom) return "up";

    return "left"; // Mặc định nếu button ở giữa màn hình
  };

  const getButtonPosition = (angle: number, radius: number, direction: string) => {
    const radian = (angle * Math.PI) / 180;
    let x = Math.cos(radian) * radius;
    let y = Math.sin(radian) * radius;

    // Đảm bảo phương hướng chính xác cho mỗi hướng
    switch (direction) {
      case "left":
        x = -Math.abs(x);
        break;
      case "right":
        x = Math.abs(x);
        break;
      case "up":
        y = -Math.abs(y);
        break;
      case "down":
        y = Math.abs(y);
        break;
    }

    return { x, y };
  };

  // Don't render if no buttons
  if (menuItems.length === 0) {
    return null;
  }

  return (
    <>
      <Box
        ref={buttonRef}
        className={`sticky-buttons ${isMenuOpen ? "menu-open" : ""} ${
          isDragging ? "dragging" : ""
        }`}
        sx={{
          position: "fixed",
          ...(isPositionAbsolute
            ? {
                top: `${position.top}px`,
                left: `${position.left}px`,
                right: "auto",
                bottom: "auto",
              }
            : {
                top: "auto",
                left: "auto",
                right: "10px",
                bottom: "40%",
              }),
          transition: isDragging ? "none" : "all 0.3s ease-out",
          cursor: isDragging ? "grabbing" : "grab",
          userSelect: "none",
          zIndex: 1050,
        }}
      >
        <Box sx={{ position: "absolute", bottom: 0, right: 0, width: 0, height: 0 }}>
          {menuItems.map((item, index) => {
            const direction = getMenuDirection();
            const angles = calculateAngles(menuItems.length, direction);
            const angle = angles[index];
            const delay = menuConfig.baseDelay * (index + 1);
            const position = getButtonPosition(angle, menuConfig.radius, direction);

            return (
              <button
                key={item.id}
                onClick={() => handleMenuItemClick(item.id)}
                style={{
                  position: "absolute",
                  width: "48px",
                  height: "48px",
                  backgroundColor: "white",
                  borderRadius: "50%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.15)",
                  border: "1px solid #e0e0e0",
                  transition: "all 0.5s ease-out",
                  transform: isMenuOpen
                    ? `translate(${position.x}px, ${position.y}px) scale(1)`
                    : "translate(0px, 0px) scale(0)",
                  transformOrigin: "center",
                  transitionDelay: isMenuOpen ? `${delay}ms` : "0ms",
                  right: "0px",
                  bottom: "0px",
                  opacity: isMenuOpen ? 1 : 0,
                  pointerEvents: isMenuOpen ? "auto" : "none",
                  WebkitTransform: isMenuOpen
                    ? `translate(${position.x}px, ${position.y}px) scale(1)`
                    : "translate(0px, 0px) scale(0)",
                  WebkitTransition: "all 0.5s ease-out",
                }}
              >
                <img
                  src={item.icon}
                  alt={item.alt}
                  style={{
                    width: "24px",
                    height: "24px",
                    objectFit: "contain",
                    transition: "transform 0.2s",
                  }}
                />
              </button>
            );
          })}
        </Box>

        <button
          onClick={toggleMenu}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          style={{
            width: "48px",
            height: "48px",
            backgroundColor: isMenuOpen ? "#f0f7ff" : "white",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.15)",
            border: "1px solid #e0e0e0",
            transition: "all 0.3s ease",
            transform: isMenuOpen ? "rotate(45deg)" : "rotate(0)",
            touchAction: "none",
            WebkitTransition: "all 0.3s ease", // Thêm cho Safari/iOS
            WebkitTransform: isMenuOpen ? "rotate(45deg)" : "rotate(0)", // Thêm cho Safari/iOS
            userSelect: "none",
          }}
        >
          {isMenuOpen ? (
            <svg
              style={{ width: "24px", height: "24px", color: "#e53935" }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          ) : (
            <svg
              style={{ width: "24px", height: "24px" }}
              fill="none"
              stroke={appConfig.color.primary}
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          )}
        </button>
      </Box>

      {/* Overlay to close menu when clicked outside */}
      {isMenuOpen && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            zIndex: 997,
          }}
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* Popup Share Link */}
      <PopUpShareLink isOpen={isOpenShareLink} setIsOpen={setIsOpenShareLink} />

      {/* Login Popup */}
      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
    </>
  );
};

export default StickyButtons;
