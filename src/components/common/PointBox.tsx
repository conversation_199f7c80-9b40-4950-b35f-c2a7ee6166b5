import { Icon } from "@/constants/Assets";
import { COLORS } from "@/constants/themes";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { searchByPointsCode, getDetailVoucherByCode } from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { showToast } from "@/utils/common";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { Box, Stack, Typography, useTheme } from "@mui/material";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { scanQRCode } from "zmp-sdk/apis";
import QrIcon from "../icon/QrIcon";
export default function PointBox() {
  const theme = useTheme();
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const appConfig = useConfigApp();

  const { showAlert } = useAlert();
  const { user } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  // const promotionItems = [
  //   { icon: Icon.ntt_gift, label: "Quản lý ưu đãi" },
  //   { icon: Icon.ntt_star, label: "Cách kiếm điểm" },
  //   { icon: Icon.ntt_clock, label: "Lịch sử điểm" },
  //   { icon: Icon.ntt_policy, label: "Chính sách" },
  // ];

  const handleStartQR = async () => {
    try {
      const { content } = await scanQRCode({});
      const url = new URL(content);
      const isReferLink =
        url.searchParams.get("targetType") === "REFER" && url.searchParams.has("refCode");
      const refCode = url.searchParams.get("refCode") || "";

      if (isReferLink) {
        if (!refCode || refCode === "undefined") {
          navigate("/refer");
          return;
        }
        if (!user) {
          navigate("/refer");
          return;
        }
        navigate(`/refer?refCode=${refCode}`);
        return;
      }

      const pointsCode = (() => {
        try {
          const url = new URL(content);
          return url.searchParams.get("promo-code");
        } catch (e) {
          return content;
        }
      })();

      if (pointsCode) {
        const result = await dispatch(getDetailVoucherByCode(pointsCode)).unwrap();
        if (result && result.result.data) {
          navigate(`/voucher/${result.result.data?.voucherDetails?.[0]?.voucherCode}`);
        } else {
          showToast({ content: "Không tìm thấy mã voucher", type: "error" });
        }
      }
    } catch (err) {
      showToast({ content: "Quét mã QR thất bại hoặc bị huỷ", type: "error" });
    }
  };

  return (
    <Box
      sx={{
        ...styles.activeCollabContainer,
        boxShadow: "0px 4px 12px 0px #0000001A",
        borderRadius: "10px",
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between" flexWrap="nowrap">
        <Stack
          direction="row"
          gap={1}
          alignItems="center"
          sx={{ cursor: "pointer", flexWrap: "nowrap" }}
          onClick={() => navigate("/profile/membership-detail")}
        >
          <img width={51} height={51} src={Icon.ntt_star} alt="Logo" />
          <Stack>
            <Stack direction={"row"} alignItems="center" gap={0.5} flexWrap="nowrap">
              <Typography fontSize={19} sx={{ whiteSpace: "nowrap" }}>
                {user?.point || 0}
              </Typography>
              <ChevronRightIcon sx={{ color: appConfig.color.primary }} />
            </Stack>
            <Typography sx={{ fontSize: 12, whiteSpace: "nowrap" }}>
              Điểm thưởng bạn đang có
            </Typography>
          </Stack>
        </Stack>
        <Stack>
          <Box
            display="flex"
            alignItems="center"
            sx={{
              backgroundColor: appConfig.color.primary,
              borderRadius: 16,
              px: 1.5,
              py: 1,
              cursor: "pointer",
              color: "white",
              fontSize: 14,
              fontWeight: 600,
              minWidth: 0,
              justifyContent: "left",
              overflow: "hidden",
              width: 125,
              maxWidth: 180,
            }}
            onClick={handleStartQR}
          >
            <QrIcon fillColor="#FF8800" />
            <Typography
              component="span"
              sx={{
                fontSize: 12,
                fontWeight: 600,
                ml: 0.5,
                whiteSpace: "nowrap",
                textAlign: "center",
                overflow: "hidden",
                textOverflow: "ellipsis",
                minWidth: 0,
                maxWidth: 85,
              }}
            >
              Quét mã QR
            </Typography>
          </Box>
        </Stack>
      </Stack>
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{ width: "100%", minWidth: 0, mt: 2 }}
      >
        {["Quản lý ưu đãi", "Cách kiếm điểm", "Lịch sử điểm", "Chính sách"].map((label) => (
          <Stack
            key={label}
            direction="row"
            alignItems="center"
            spacing={0.5}
            sx={{ flex: 1, minWidth: 0, justifyContent: "center" }}
          >
            <img src="/images/check.png" width={18} height={18} alt="check" />
            <Typography
              sx={{
                fontSize: 10,
                fontWeight: 500,
                color: "#222",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                minWidth: 0,
              }}
            >
              {label}
            </Typography>
          </Stack>
        ))}
      </Stack>
    </Box>
  );
}

const styles = {
  activeCollabContainer: {
    borderRadius: 1,
    padding: 2,
    backgroundColor: COLORS.white,
  },
  subtitleStyle: {
    fontSize: 12,
    fontWeight: 500,
    color: "#222",
  },
};
