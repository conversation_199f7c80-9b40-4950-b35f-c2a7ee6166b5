import React, { ReactNode } from "react";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { Box, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

export default function PopupCommon({
  open,
  setOpen,
  title,
  content,
  action,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  title?: ReactNode;
  content: ReactNode;
  action?: ReactNode;
}) {
  const handleClose = () => {
    setOpen(false);
  };
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      PaperProps={{
        style: {
          borderRadius: 24,
          boxShadow: "0px 8px 32px 0px rgba(26, 34, 82, 0.16)",
          padding: 0,
          minWidth: 320,
          maxWidth: 360,
        },
      }}
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: 4,
          margin: 0,
          padding: 0,
          background: "#fff",
        },
        "& .MuiBackdrop-root": {
          backgroundColor: "rgba(255,255,255,0.7)",
        },
      }}
    >
      <Box position="relative" pt={3} px={3} pb={0} textAlign="center">
        {title}
        <IconButton
          onClick={handleClose}
          style={{ position: "absolute", right: 12, top: 12, zIndex: 2 }}
          size="small"
        >
          <CloseIcon sx={{ fontSize: 24 }} />
        </IconButton>
      </Box>
      <DialogContent
        style={{
          padding: 0,
          paddingTop: 8,
          paddingBottom: 0,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          background: "#fff",
        }}
      >
        {content}
      </DialogContent>
      {action && (
        <DialogActions
          style={{
            justifyContent: "center",
            border: "none",
            paddingBottom: 24,
            paddingTop: 0,
            background: "#fff",
          }}
        >
          {action}
        </DialogActions>
      )}
    </Dialog>
  );
}
