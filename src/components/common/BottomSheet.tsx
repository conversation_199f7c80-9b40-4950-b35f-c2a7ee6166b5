import { Box, SwipeableDrawer, SxProps } from "@mui/material";
import React from "react";

interface BottomSheetProps {
  open: boolean;
  onOpen: () => void;
  onClose: () => void;
  children: React.ReactNode;
  footer?: React.ReactNode;
  height?: string;
  sx?: SxProps;
  disableSwipeToOpen?: boolean;
}

export default function BottomSheet({
  open,
  onOpen,
  onClose,
  children,
  footer,
  height = "85vh",
  sx,
  disableSwipeToOpen = false,
}: BottomSheetProps) {
  return (
    <SwipeableDrawer
      anchor="bottom"
      open={open}
      onOpen={disableSwipeToOpen ? () => {} : onOpen}
      onClose={onClose}
      disableSwipeToOpen={disableSwipeToOpen}
      disableDiscovery={disableSwipeToOpen}
      PaperProps={{
        sx: {
          height,
          borderTopLeftRadius: "20px",
          borderTopRightRadius: "20px",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          ...sx,
        },
      }}
    >
      <Box
        sx={{
          width: 50,
          height: 4,
          borderRadius: 2,
          bgcolor: "grey.300",
          mx: "auto",
          marginBlock: 1,
          flexShrink: 0,
        }}
      />
      <Box sx={{ flex: 1, overflow: "auto" }}>{children}</Box>
      {footer && <Box sx={{ flexShrink: 0 }}>{footer}</Box>}
    </SwipeableDrawer>
  );
}
