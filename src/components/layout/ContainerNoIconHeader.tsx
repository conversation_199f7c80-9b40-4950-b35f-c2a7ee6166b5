import { Box, Container, Grid, Stack } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { Header } from "zmp-ui";
import styles from "../../css/styles.module.css";
import { useConfigApp } from "@/hooks/useConfigApp";

export default function FrameContainerNoIcon({
  children,
  title,
  overrideStyle,
}: {
  children: ReactNode;
  title: string;
  overrideStyle?: React.CSSProperties;
}) {
  const [positionCss, setPositionCss] = useState({});
  const appConfig = useConfigApp();
  const handleScroll = (e) => {
    const bottom =
      e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    if (bottom) {
      // setPositionCss({
      //     position: 'fixed',
      // })
    }
  };

  return (
    <div style={{ backgroundColor: "#F2F3F5", ...overrideStyle }}>
      <Header
        showBackIcon={false}
        backgroundColor={appConfig.color.primary}
        textColor="#fff"
        style={positionCss}
        title={title}
      />
      <div className={styles.pageContent}>
        <Container onScroll={handleScroll} style={{ paddingBottom: 64 }}>
          <Stack paddingBlock={2}>{children}</Stack>
        </Container>
      </div>
    </div>
  );
}
