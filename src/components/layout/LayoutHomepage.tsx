import { Box, useTheme } from "@mui/material";
import moduleStyle from "@/css/styles.module.css";
import { RootState } from "@/redux/store";
import { Header } from "zmp-ui";
import React from "react";
import { useEffect } from "react";
import { useConfigApp } from "@/hooks/useConfigApp";
import { COLORS } from "@/constants/themes";

export default function LayoutHomePage({ children, title }: any) {
  const appConfig = useConfigApp();
  const { header } = useConfigApp();
  const { container } = useConfigApp();

  const headerStyleItem = appConfig.header as any;

  const headerBgColor = headerStyleItem?.backgroundColor || "#fff";
  const headerBgImage = headerStyleItem?.backgroundImage || "";
  const headerContainerStyle: React.CSSProperties = {
    ...styles.container,
    backgroundColor: headerBgImage ? undefined : headerBgColor,
    backgroundImage: headerBgImage ? `url(${headerBgImage})` : undefined,
    backgroundSize: headerBgImage ? "cover" : undefined,
    backgroundRepeat: headerBgImage ? "no-repeat" : undefined,
    backgroundPosition: headerBgImage ? "center" : undefined,
  };

  const containerStyleItem = container as any;
  const containerBgColor = containerStyleItem?.backgroundColor || "";
  const containerBgImage = containerStyleItem?.backgroundImage || "";

  const containerStyle = {
    paddingInline: 0,
    backgroundColor: containerBgImage ? undefined : containerBgColor,
    backgroundImage: containerBgImage ? `url(${containerBgImage})` : undefined,
    backgroundSize: containerBgImage ? "cover" : undefined,
    backgroundRepeat: containerBgImage ? "no-repeat" : undefined,
    backgroundPosition: containerBgImage ? "center" : undefined,
  };

  return (
    <Box style={{ ...containerStyle, position: "relative" }}>
      <Box style={styles.container}>
        <Header
          // backgroundColor={COLORS.bgColor.fourth}
          textColor={appConfig.color.primary}
          title={title}
          showBackIcon={false}
          style={{ paddingInline: 0, ...headerContainerStyle }}
        />
        <Box style={{ ...styles.container, ...containerStyle }} className={moduleStyle.pageContent}>
          <Box>{children}</Box>
        </Box>
      </Box>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  background: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: 160,
    zIndex: -1,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  container: {
    backgroundSize: "contain",
    backgroundRepeat: "no-repeat",
    backgroundPositionY: "center top",
    width: "100%",
    margin: "0 auto",
  },
};
