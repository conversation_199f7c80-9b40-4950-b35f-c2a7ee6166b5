import { Box, Container, IconButton, Stack } from "@mui/material";
import React, { ReactNode } from "react";
import styles from "../../css/styles.module.css";
import { useConfigApp } from "@/hooks/useConfigApp";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import { useNavigate } from "@/utils/component-util";

export default function FrameContainerWeb({
  children,
  overrideStyle,
}: {
  children: ReactNode;
  overrideStyle?: React.CSSProperties;
}) {
  const appConfig = useConfigApp();
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1); // Navigate to the previous page in the browser history
  };

  return (
    <Container style={overrideStyle}>
      <Box position={"fixed"} top={20} left={10}>
        <IconButton onClick={handleBack}>
          <ArrowBackIosNewIcon style={{ color: appConfig.color.accent }} />
        </IconButton>
      </Box>
      <div className={styles.pageContent}>
        <Stack>{children}</Stack>
      </div>
    </Container>
  );
}
