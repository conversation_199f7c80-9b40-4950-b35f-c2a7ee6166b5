import {
  AffiliateIcon,
  CartIcon,
  HomeIcon,
  ProfileIcon,
  VoucherIcon,
} from "@/components/icon/IconBottomTabs";
import { Router } from "@/constants/Route";
import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";

export interface BottomTabItem {
  label: string;
  value: string;
  icon: (props: any) => JSX.Element;
  color?: string;
}

export const BottomTabs: BottomTabItem[] = [
  {
    label: "Trang chủ",
    value: "/",
    icon: (props) => <HomeIcon {...props} />,
  },
  {
    label: "Ưu đãi",
    value: Router.promotion.index,
    icon: (props) => <VoucherIcon {...props} />,
  },
  {
    label: "Sản phẩm",
    value: Router.menu,
    icon: (props) => <AffiliateIcon {...props} />,
  },
  {
    label: "Giỏ hàng",
    value: Router.cartPayment,
    icon: (props) => <CartIcon {...props} />,
  },
  {
    label: "Tài khoản",
    value: Router.profile.index,
    icon: (props) => <ProfileIcon {...props} />,
  },
];

export function useBottomTabs(): BottomTabItem[] {
  const { container } = useConfigApp();
  const navbar = Array.isArray(container?.navbar) ? container.navbar : [];

  if (navbar.length !== BottomTabs.length) {
    return BottomTabs;
  }

  return navbar.map((item, idx) => {
    const defaultTab = BottomTabs[idx];
    return {
      label: item.text || defaultTab.label,
      value: item.link !== undefined && item.link !== "" ? item.link : defaultTab.value,
      icon:
        item.icon && item.icon !== ""
          ? (props: React.ImgHTMLAttributes<HTMLImageElement>) => (
              <img
                src={item.icon}
                alt={item.text || ""}
                style={{
                  width: 24,
                  height: 24,
                  ...(item.color ? { color: item.color } : {}),
                }}
                {...props}
              />
            )
          : defaultTab.icon,
      ...(item.color && item.color !== "" ? { color: item.color } : {}),
    };
  });
}
