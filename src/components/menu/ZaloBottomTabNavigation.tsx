import { Router } from "@/constants/Route";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Badge, Stack } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import { BottomNavigation } from "zmp-ui";
import { NO_BOTTOM_NAVIGATION_PAGES } from "../../constants/Const";
import { useCart } from "../../hooks/useCart";
import { BottomTabItem, useBottomTabs } from "./BottomTabs";

export default function ZaloBottomTabNavigation() {
  const { cartPayment } = useCart();
  const [activeTab, setActiveTab] = useState("chat");
  const location = useLocation();
  const { color } = useConfigApp();
  const bottomTabs = useBottomTabs();

  useEffect(() => {
    setActiveTab(location.pathname);
  }, [location]);

  const totalProduct = useMemo(() => {
    if (!Array.isArray(cartPayment?.listItems) || !cartPayment?.listItems.length) return 0;
    return cartPayment?.listItems.reduce((acc, item) => acc + item.quantity, 0);
  }, [cartPayment]);

  const noBottomNav = useMemo(() => {
    if (/^\/myVoucher\/\d+$/.test(location.pathname)) return true;
    if (/^\/voucher\/\d+$/.test(location.pathname)) return true;
    if (location.pathname.includes("/product-detail/")) return true;
    if (
      [Router.profile.address, Router.branch.index].includes(location.pathname) &&
      location.state?.shouldGoBack
    )
      return true;

    return NO_BOTTOM_NAVIGATION_PAGES.some((path) => location.pathname.includes(path));
  }, [location]);

  if (noBottomNav) {
    return null;
  }

  const renderIcon = (item: BottomTabItem) => (
    <Badge
      badgeContent={item.value === "/cartPayment" && totalProduct ? totalProduct : 0}
      color="error"
      sx={{
        span: {
          height: "18px",
          minWidth: "18px",
        },
      }}
    >
      {item.icon && typeof item.icon === "function"
        ? item.icon({ fillColor: activeTab === item.value })
        : null}
      <div style={{ height: 2 }} />
    </Badge>
  );

  const renderActiveIcon = (item: BottomTabItem) =>
    item.icon && typeof item.icon === "function"
      ? item.icon({ fillColor: activeTab === item.value ? "#FFFFFF" : null })
      : null;

  return (
    <Stack style={{ marginBottom: 49, display: "initial" }}>
      <BottomNavigation fixed activeKey={activeTab}>
        {bottomTabs.map((item) => (
          <BottomNavigation.Item
            linkTo={item.value}
            key={item.value}
            label={item.label}
            icon={renderIcon(item)}
            style={activeTab === item.value ? { color: color.primary } : { color: "#000" }}
            activeIcon={renderActiveIcon(item)}
          />
        ))}
      </BottomNavigation>
    </Stack>
  );
}
