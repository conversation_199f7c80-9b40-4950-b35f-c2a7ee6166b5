import BottomTabNavigationDetailFnB from "@/components/menu/bottomtabnav/BottomTabNavigationDetailFnB";
import BottomTabNavigationDetailRetail from "@/components/menu/bottomtabnav/BottomTabNavigationDetailRetail";
import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";

const componentMap = {
  FB: BottomTabNavigationDetailFnB,
  Retail: BottomTabNavigationDetailRetail,
};

export default function BottomTabNavigationDetail(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FB");
  const Component = componentMap[businessType] || componentMap.FB;
  return <Component {...props} />;
}
