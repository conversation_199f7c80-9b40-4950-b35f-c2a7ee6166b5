import PopupAddToCart from "@/components/UI/buttonaddtocart";
import PopupBuyNow from "@/components/UI/buttonbuynow";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Badge, Box, Button, IconButton, Stack, Typography } from "@mui/material";
import React, { useMemo } from "react";
import { Icon } from "../../../constants/Assets";
import { Router } from "../../../constants/Route";
import { COLORS } from "../../../constants/themes";
import { useCart } from "../../../hooks/useCart";
import { IProduct } from "../../../types/product";
import { useNavigate } from "../../../utils/component-util";
import { openChat } from "../../../utils/openChat";
import CartIcon from "../../icon/CartIcon";

const BottomTabNavigationDetail: React.FC<{
  product: IProduct;
}> = ({ product }) => {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const { addProductToCart } = useCart();
  const [isOpenPopup, setIsOpenPopup] = React.useState(false);
  const [shouldNavigate, setShouldNavigate] = React.useState(false);
  const { cartPayment } = useCart();
  const [isOpenPopupBuyNow, setIsOpenPopupBuyNow] = React.useState<boolean>(false);
  const totalProduct = useMemo(() => {
    if (!Array.isArray(cartPayment?.listItems) || !cartPayment?.listItems.length) return 0;
    return cartPayment?.listItems.reduce((acc, item) => acc + item.quantity, 0);
  }, [cartPayment]);

  const acceptAddToCart = async () => {
    // setShouldNavigate(true);
    // if (product.isVariant) {
    //   setIsOpenPopup(true);
    //   return;
    // }
    // const result = await addProductToCart(product, 1);
    // if (result) {
    //   showToast({
    //     content: "Thêm vào giỏ thành công",
    //     type: "success",
    //   });
    // }
    // setTimeout(() => navigate(Router.cart), 500);
  };

  const handleOpen = () => {
    setIsOpenPopup(true);
  };

  const handleClose = () => {
    setIsOpenPopup(false);
    setShouldNavigate(false);
  };

  return (
    <div style={styles.bottomCartElement}>
      <Stack sx={styles.container} spacing={0.5} direction="row" gap={2}>
        <Box
          display="flex"
          width={product.quantity > 0 ? "33%" : "50%"}
          justifyContent="space-between"
        >
          <Button sx={styles.btnStyle}>
            <IconButton
              style={{ marginInline: 8, padding: 0 }}
              aria-label="fingerprint"
              color="secondary"
              onClick={() => openChat(appConfig.oaId || "")}
            >
              <img src={Icon.zalo} width={32} height={24} />
            </IconButton>
            <Typography style={styles.textStyle}>Chat</Typography>
          </Button>
          <Button onClick={() => navigate(Router.cartPayment)} sx={styles.btnStyle}>
            {totalProduct > 0 ? (
              <Badge
                badgeContent={totalProduct} // Thay cartCount bằng số lượng giỏ hàng
                color="error"
                sx={{
                  span: {
                    height: "18px",
                    minWidth: "18px",
                  },
                }}
              >
                <CartIcon fillColor={appConfig.color.primary} />
              </Badge>
            ) : (
              <CartIcon fillColor={appConfig.color.primary} />
            )}

            <Typography style={styles.textStyle}>Giỏ hàng</Typography>
          </Button>
        </Box>
        {(!product.isVariant && product?.quantity > 0) ||
        (product.isVariant &&
          product.listVariant?.some((variant) => (variant?.quantity ?? 0) > 0)) ? (
          <Box display="flex" overflow="hidden" width={"67%"} height={48.5}>
            <PopupAddToCart
              product={product}
              isOpen={isOpenPopup}
              onOpen={handleOpen}
              onClose={handleClose}
              shouldNavigate={shouldNavigate}
              navigateAfterAdd={() => {
                navigate(Router.cartPayment);
              }}
            />

            <PopupBuyNow
              product={product}
              isOpen={isOpenPopupBuyNow}
              onOpen={() => setIsOpenPopupBuyNow(true)}
              onClose={() => setIsOpenPopupBuyNow(false)}
              shouldNavigate={shouldNavigate}
              navigateAfterAdd={() => {
                navigate(Router.cartPayment);
              }}
            />
          </Box>
        ) : (
          <Button
            variant="contained"
            style={{ width: "50%", borderRadius: 5, marginRight: 14 }}
            sx={{
              flex: 1,
              backgroundColor: appConfig.color.primary,
              color: COLORS.white,
              borderRadius: "0",
            }}
            onClick={() => openChat(appConfig.oaId || "")}
          >
            Liên hệ
          </Button>
        )}
      </Stack>
    </div>
  );
};

export default BottomTabNavigationDetail;

const styles: Record<string, React.CSSProperties> = {
  bottomCartElement: {
    width: "100vw",
    maxWidth: "450px",
    position: "fixed",
    bottom: "0",
    zIndex: 1000,
    height: "auto",
    borderTop: "1px solid rgb(223, 223, 223)",
    background: "rgb(255, 255, 255)",
  },
  container: {
    minWidth: "100%",
    alignItems: "center",
    justifyContent: "space-between",
    paddingLeft: 2,
    paddingBlock: 1,
  },
  textStyle: {
    color: "#6A6A6A",
    fontSize: 12,
    fontWeight: 700,
    marginTop: 5,
  },
  btnStyle: {
    flexDirection: "column",
    minWidth: 60,
    padding: 0,
  },
};
