import React, { useEffect, useState, memo } from "react";
import {
  FormControl,
  InputAdornment,
  Stack,
  Tab,
  Tabs,
  TextField,
} from "@mui/material";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import { IProductCategory } from "../../types/product";
import {
  getProductListByCategory,
  setSearchCondition,
} from "../../redux/slices/product/productListSlice";
import { useLocation } from "react-router-dom";
import ClearIcon from "@mui/icons-material/Clear";
import { Icon } from "../../constants/Assets";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useDebouncedCallback } from "use-debounce";
import { COLORS } from "@/constants/themes";

interface CategoriesProps { }

const SearchField = memo(({ 
  value, 
  onChange, 
  onClear,
  showClearIcon 
}: { 
  value: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClear: () => void;
  showClearIcon: string;
}) => {
  return (
    <FormControl sx={{ width: '100%', position: 'relative' }}>
      <img
        style={{
          top: 15,
          left: 15,
          position: "absolute",
          width: 20,
          zIndex: 1,
        }}
        src={Icon.search}
        alt="Tìm kiếm sản phẩm"
      />
      <TextField
        sx={{
          width: "100%",
          marginBottom: 1.5,
          backgroundColor: COLORS.white,
          '& .MuiOutlinedInput-root': {
            paddingLeft: '40px'
          }
        }}
        className="input-search"
        placeholder="Tìm kiếm sản phẩm"
        size="small"
        variant="outlined"
        onChange={onChange}
        value={value}
        InputProps={{
          endAdornment: (
            <InputAdornment
              position="end"
              style={{ display: showClearIcon }}
              onClick={onClear}
            >
              <ClearIcon />
            </InputAdornment>
          ),
        }}
      />
    </FormControl>
  );
});

const Categories: React.FC<CategoriesProps> = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { color } = useConfigApp();

  const [showClearIcon, setShowClearIcon] = useState("none");
  const [tabCategory, setTabCategory] = useState("0");
  const [tabSubCategory, setTabSubCategory] = useState("0");
  const [searchKey, setSearchKey] = useState("");

  const { productCategoryListLevel1, productCategoryListLevel2 } = useSelector(
    (state: RootState) => state.product,
  );
  const location = useLocation();

  const { searchCondition, isLoading } = useSelector(
    (state: RootState) => state.productList,
  );

  useEffect(() => {
    setTabCategory(searchCondition?.categoryId || "0");
    setTabSubCategory(searchCondition?.subCategoryId || "0");
    dispatch(getProductListByCategory({ ...searchCondition, parentId: "root" }));
  }, [searchCondition]);

  const debounced = useDebouncedCallback((value) => {
    if (value !== searchCondition.search) {
      dispatch(
        setSearchCondition({
          ...searchCondition,
          search: value,
        }),
      );
    }
  }, 500);

  useEffect(() => {
    let initCategoryIdLevel1 =
      productCategoryListLevel1.length > 0
        ? productCategoryListLevel1[0].categoryId
        : undefined;
    if (location.state?.id) initCategoryIdLevel1 = location.state?.id;
    getProductList(initCategoryIdLevel1);
  }, [productCategoryListLevel1]);

  const getProductList = (cateId: string | undefined) => {
    dispatch(
      setSearchCondition({
        ...searchCondition,
        categoryId: cateId,
      }),
    );
  };

  const handleCategoryLevel1Change = (
    event: React.SyntheticEvent,
    value: string,
  ) => {
    dispatch(
      setSearchCondition({
        ...searchCondition,
        categoryId: value,
        subCategoryId: null,
      }),
    );
  };

  const handleCategoryLevel2Change = (
    event: React.SyntheticEvent,
    value: string,
  ) => {
    dispatch(
      setSearchCondition({
        ...searchCondition,
        subCategoryId: value,
      }),
    );
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const value = event.target.value;
    setSearchKey(value);
    setShowClearIcon(value === "" ? "none" : "flex");
    debounced(value);
  };

  const handleClick = (): void => {
    setSearchKey("");
    setShowClearIcon("none");
    dispatch(
      setSearchCondition({
        ...searchCondition,
        search: "",
      }),
    );
  };

  const getCategoryLevel2 = (cateLv1: string | undefined) => {
    const catChild = productCategoryListLevel2.filter(
      (e) => e.parentId === cateLv1,
    );
    return catChild;
  };

  return (
    <>
      <Stack style={{ marginBlock: 10 }}>
        <Tabs
          className="product"
          value={tabCategory}
          onChange={handleCategoryLevel1Change}
          variant="scrollable"
          scrollButtons={false}
          aria-label="scrollable auto tabs example"
          TabIndicatorProps={{
            style: {
              backgroundColor: color.primary,
              border: 1,
              color: color.primary,
            },
          }}
          sx={styles.tabDimension}
        >
          {productCategoryListLevel1?.map((item: IProductCategory, index) => (
            <Tab
              key={item.categoryId}
              className={"catalog-item"}
              style={{
                ...styles.firstTabItem,
                color:
                  tabCategory === item.categoryId ? color.primary : "#9C9C9C",
                fontWeight: tabCategory === item.categoryId ? 700 : 400,
              }}
              label={item.categoryName}
              value={item.categoryId}
            />
          ))}
        </Tabs>
      </Stack>
      <Stack>
        <SearchField
          value={searchKey}
          onChange={handleChange}
          onClear={handleClick}
          showClearIcon={showClearIcon}
        />
      </Stack>
      <Stack>
        <Tabs
          className="product"
          value={tabSubCategory}
          onChange={handleCategoryLevel2Change}
          variant="scrollable"
          scrollButtons={false}
          aria-label="scrollable auto tabs example"
          TabIndicatorProps={{ style: { backgroundColor: "transparent" } }}
          sx={styles.tabDimension}
        >
          <Tab
            className={"type-item"}
            style={{
              ...styles.secondTabItem,
              background: tabSubCategory === "0" ? color.primary : "",
              color: tabSubCategory === "0" ? COLORS.white : "#9C9C9C",
            }}
            label="Tất cả"
            value={"0"}
          />
          {getCategoryLevel2(searchCondition.categoryId || undefined)?.map(
            (item: IProductCategory, index) => (
              <Tab
                key={item.categoryId}
                className={"type-item"}
                style={{
                  ...styles.secondTabItem,
                  background:
                    tabSubCategory === item.categoryId ? color.primary : "",
                  color:
                    tabSubCategory === item.categoryId
                      ? COLORS.white
                      : "#9C9C9C",
                }}
                label={item.categoryName}
                value={item.categoryId}
              />
            ),
          )}
        </Tabs>
      </Stack>
    </>
  );
};
export default Categories;

const styles: Record<string, React.CSSProperties> = {
  firstTabItem: {
    fontSize: "17px",
    padding: 0,
    marginRight: "20px",
    borderRadius: 5,
    minHeight: 40,
    height: 40,
    minWidth: 0,
  },
  secondTabItem: {
    fontSize: "15px",
    borderRadius: 2,
    minHeight: 40,
    height: 40,
  },
  tabDimension: {
    minHeight: 40,
    height: 40,
  },
};
