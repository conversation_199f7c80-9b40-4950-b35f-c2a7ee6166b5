import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useNavigate } from "../../utils/component-util";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { useConfigApp } from "@/hooks/useConfigApp";

interface IProductCart {
  id: number;
  img: string;
  name: string;
  price: number;
  quantity: number;
}
const OrderCard = () => {
  const appConfig = useConfigApp();
  // const [value, setValue] = useState(0)
  const navigator = useNavigate();
  const theme = useTheme();

  // const handleChange = (event: React.SyntheticEvent, newValue: number) => {
  //     setValue(newValue)
  // }

  // const catagory = [
  //     {
  //         label: 'Chờ xác nhận',
  //         icon: '',
  //     },
  //     {
  //         label: 'Ch<PERSON> lấy hàng',
  //         icon: '',
  //     },
  //     {
  //         label: 'Đang giao hàng',
  //         icon: '',
  //     },
  //     {
  //         label: 'Đã giao hàng',
  //         icon: '',
  //     },
  //     {
  //         label: 'Hủy đơn',
  //         icon: '',
  //     },
  // ]
  const [products, setProducts] = useState<IProductCart[]>([
    {
      id: 1,
      img: "/images/product1.png",
      name: "Nước rửa chén Cô cà chua tự nhiên 100% 1",
      price: 150000,
      quantity: 1,
    },
    {
      id: 2,
      img: "/images/product1.png",
      name: "Nước rửa chén Cô cà chua tự nhiên 100% 2",
      price: 150000,
      quantity: 1,
    },
    {
      id: 3,
      img: "/images/product1.png",
      name: "Nước rửa chén Cô cà chua tự nhiên 100% 3",
      price: 150000,
      quantity: 1,
    },
    {
      id: 4,
      img: "/images/product1.png",
      name: "Nước rửa chén Cô cà chua tự nhiên 100% 4",
      price: 150000,
      quantity: 1,
    },
  ]);
  return (
    <Stack gap={0}>
      {products?.length > 0 ? (
        products.map((item) => (
          <Stack
            key={`product-${item.id}`}
            style={{ background: "#fff", border: "1px solid #D9D9D9" }}
            padding={2}
            borderRadius={4}
            marginBlock={1}
          >
            <span
              style={{ fontWeight: 700, marginBottom: 12, color: "#2677E8" }}
            >
              {appConfig?.shopName}
            </span>
            <Stack direction="row" gap={2} alignItems={"center"}>
              <img width={60} src={item.img} />
              <Stack color={theme.palette.primary.main} gap={1}>
                <span>{item.name}</span>
                <span
                  style={{
                    color: "#2677E8",
                    fontWeight: 700,
                    fontSize: 16,
                  }}
                >
                  {item.price} x{item.quantity}
                </span>
              </Stack>
            </Stack>
            <Stack
              direction="row"
              marginBlock={2}
              gap={2}
              justifyContent={"space-between"}
            >
              <Button
                style={{
                  color: "#fff",
                  borderRadius: 99,
                  fontSize: 16,
                  fontWeight: 700,
                }}
                color="secondary"
                variant="contained"
                onClick={() => {
                  navigator(`/order/${item.id}`);
                }}
              >
                Xem sản phẩm
              </Button>
              <Button
                style={{ borderRadius: 99, fontSize: 16, fontWeight: 700 }}
                color="secondary"
                variant="outlined"
              >
                Đã hoàn thành
              </Button>
            </Stack>
          </Stack>
        ))
      ) : (
        <Stack direction="row" justifyContent={"center"} padding={2}>
          Không có sản phẩm nào
        </Stack>
      )}
    </Stack>
  );
};

export default OrderCard;
