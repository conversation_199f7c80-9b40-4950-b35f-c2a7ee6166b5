import React, { useState } from "react";
import { Switch, styled } from "@mui/material";

interface CustomSwitchProps {
  activeColor?: string;
  inactiveColor?: string;
}

export const CustomSwitch = styled(Switch)<CustomSwitchProps>(
  ({ theme, activeColor = "#8BC34A", inactiveColor = "#dcdcdc" }) => ({
    width: 40,
    height: 20,
    padding: 0,
    display: "flex",
    "& .MuiSwitch-switchBase": {
      padding: 2,
      color: "white",
      "&.Mui-checked": {
        transform: "translateX(20px)",
        color: "white",
        "& + .MuiSwitch-track": {
          backgroundColor: activeColor,
          opacity: 1,
        },
      },
    },
    "& .MuiSwitch-thumb": {
      width: 16,
      height: 16,
      boxShadow: "0 0 2px rgba(0,0,0,0.2)",
    },
    "& .MuiSwitch-track": {
      borderRadius: 25 / 2,
      opacity: 1,
      backgroundColor: inactiveColor,
      boxSizing: "border-box",
    },
  })
);
