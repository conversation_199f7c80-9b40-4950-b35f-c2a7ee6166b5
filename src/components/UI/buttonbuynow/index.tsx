import PopUpBuyNowFnB from "@/components/UI/buttonbuynow/PopupBuyNowFnB";
import PopupBuyNowRetail from "@/components/UI/buttonbuynow/PopupBuyNowRetail";
import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";

const componentMap = {
  FB: PopUpBuyNowFnB,
  Retail: PopupBuyNowRetail,
};

export default function PopupBuyNow(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FB");
  const Component = componentMap[businessType] || componentMap.FB;
  return <Component {...props} />;
}
