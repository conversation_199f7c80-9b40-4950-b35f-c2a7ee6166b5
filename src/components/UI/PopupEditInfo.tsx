import React, { useState } from "react";
import Button from "@mui/material/Button";
import PopupEditProfile from "./PopupEditProfile";
import { COLORS } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

export default function PopupEditInfo({ user }) {
  const [open, setOpen] = useState(false);
  const { color } = useConfigApp();

  const handleClickOpen = () => {
    setOpen(true);
  };

  return (
    <>
      <Button
        onClick={handleClickOpen}
        style={{
          ...styles.container,
          background: color.primary,
        }}
      >
        Cập nhật
      </Button>
      <PopupEditProfile {...{ open, setOpen }} />
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    fontSize: "14px",
    fontWeight: 700,
    color: COLORS.white,
    padding: "7px 20px",
    borderRadius: "8px",
  },
};
