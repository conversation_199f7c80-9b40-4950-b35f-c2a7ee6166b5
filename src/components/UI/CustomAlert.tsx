import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { CircularProgress, Stack, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { COLORS } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";

export default function CustomAlert() {
  const appConfig = useConfigApp();
  const { alert } = useSelector((state: RootState) => state.alert);
  const { hideAlert } = useAlert();
  const theme = useTheme();

  return (
    <Dialog
      open={!!alert?.isShow || false}
      onClose={hideAlert}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      PaperProps={{ sx: { borderRadius: 4 } }}
    >
      <DialogContent style={styles.containerContentStyle}>
        <Stack marginBlock={2} textAlign="center" width={"100%"} gap={2}>
          {alert?.icon && (
            <Stack direction={"row"} justifyContent="center" width={"100%"}>
              {alert?.icon}
            </Stack>
          )}
          <Stack>
            {alert?.title && (
              <Typography
                style={
                  alert?.titleStyle || {
                    ...styles.titleStyle,
                    color: appConfig.color.primary,
                  }
                }
              >
                {alert?.title}
              </Typography>
            )}
            {alert?.content && (
              <Typography style={alert?.contentStyle || styles.contentStyle}>
                {alert?.content}
              </Typography>
            )}
          </Stack>
        </Stack>
      </DialogContent>
      <DialogActions style={styles.containerButtonStyle}>
        {alert !== null && alert.buttons && alert.buttons.length > 0 ? (
          <Stack
            direction={
              alert.buttons.length === 1
                ? "row"
                : alert.buttons.length < 3
                ? "row"
                : "column"
            }
            justifyContent={
              alert.buttons.length === 1 ? "center" : "space-between"
            }
            alignItems="center"
            width={"100%"}
          >
            {alert.buttons.map((e, index) => {
              return (
                <Button
                  key={`btn-${index}`}
                  style={
                    e.customButtonStyle
                      ? e.customButtonStyle
                      : index % 2 === 0 && alert.buttons!.length > 1
                      ? styles.defaultFirstBtnSyle
                      : {
                          ...styles.defaultSecondBtnSyle,
                          backgroundColor: appConfig.color.primary,
                        }
                  }
                  onClick={() => {
                    setTimeout(() => {
                      if (e.action) {
                        e.action();
                      }
                    }, 200);
                    hideAlert();
                  }}
                  variant="contained"
                >
                  {alert?.loading && (
                    <CircularProgress
                      size={18}
                      sx={{ color: "#fff", marginRight: 1 }}
                    />
                  )}
                  {e.title}
                </Button>
              );
            })}
          </Stack>
        ) : (
          <Stack
            direction={"row"}
            justifyContent="center"
            alignItems="center"
            width={"100%"}
          >
            <Button
              style={{
                ...styles.defaultSecondBtnSyle,
                background: appConfig.color.primary,
              }}
              onClick={hideAlert}
              variant="contained"
            >
              {alert?.loading && (
                <CircularProgress
                  size={18}
                  sx={{ color: "#fff", marginRight: 1 }}
                />
              )}
              OK
            </Button>
          </Stack>
        )}
      </DialogActions>
    </Dialog>
  );
}

const styles: Record<string, React.CSSProperties> = {
  containerContentStyle: {
    margin: 0,
    paddingInline: 12,
    paddingTop: 12,
    paddingBottom: 0,
  },
  titleStyle: {
    fontWeight: 700,
    marginBlock: 2,
  },
  contentStyle: {
    marginBlock: 2,
  },
  containerButtonStyle: {
    margin: 0,
    paddingBottom: 16,
    minWidth: 240,
  },
  defaultFirstBtnSyle: {
    background: "#E8E8EA",
    color: "#666666",
    boxShadow: "none",
    width: "50%",
    margin: 8,
  },
  defaultSecondBtnSyle: {
    color: "white",
    boxShadow: "none",
    width: "50%",
    margin: 8,
  },
};
