import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import { Stack } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { updateUser } from "../../redux/slices/authen/authSlice";
import CloseIcon from "@mui/icons-material/Close";
import { ICampaign } from "../../types/campaign";
import { setCampaignPopupShowed } from "../../redux/slices/configSlice";

export default function PopupPromoted({ campaign }: { campaign: ICampaign }) {
  const dispatch = useDispatch<AppDispatch>();
  const [open, setOpen] = React.useState(true);
  const { user } = useSelector((state: RootState) => state.auth);
  const { isCampaignPopupShowed } = useSelector(
    (state: RootState) => state.config
  );

  const handleClose = async () => {
    setOpen(false);
    if (isCampaignPopupShowed) {
      dispatch(setCampaignPopupShowed(false));
    }
  };

  const onHidePopupAtNextTime = async () => {
    setOpen(false);
    if (isCampaignPopupShowed) {
      dispatch(setCampaignPopupShowed(false));
    }
    if (!user) return;

    const arrHideCampaignIds = user.hideCampaignIds?.split(",") || [];
    arrHideCampaignIds.push(`${campaign.id}`);
    await dispatch(
      updateUser({
        hideCampaignIds: arrHideCampaignIds.toString(),
      })
    );
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        maxWidth="lg"
        fullWidth={true}
        sx={{
          ".MuiPaper-root": {
            overflow: "hidden",
            borderRadius: "10px",
            backgroundColor: "transparent",
          },
        }}
        PaperProps={{
          style: {
            boxShadow: "none",
          },
        }}
      >
        <img
          src={
            import.meta.env.VITE_API_URL +
            campaign.attributes.banner.data.attributes.url
          }
          style={styles.image}
          alt=""
        />
        <DialogActions style={styles.buttonContainer}>
          <Stack
            gap={2}
            direction="row"
            justifyContent={"center"}
            width={"100%"}
          >
            <Button style={styles.closeBtn} onClick={handleClose}>
              <CloseIcon />
            </Button>
          </Stack>
        </DialogActions>
        <Button sx={{ color: "#FFF" }} onClick={onHidePopupAtNextTime}>
          Không hiện lại
        </Button>
      </Dialog>
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  image: {
    borderRadius: 10,
    maxWidth: "100%",
    maxHeight: "80%",
    objectFit: "fill",
  },
  buttonContainer: {
    margin: 0,
    paddingBottom: 18,
    position: "absolute",
    right: "-15px",
  },
  closeBtn: {
    color: "white",
    borderRadius: 99,
  },
};
