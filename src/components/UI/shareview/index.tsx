import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import ShareViewFnB from "./ShareViewFnB";
import ShareViewRetail from "./ShareViewRetail";

const componentMap = {
  FnB: ShareViewFnB,
  Retail: ShareViewRetail,
};

export default function ShareView(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FnB");
  const Component = componentMap[businessType] || componentMap.FnB;
  return <Component {...props} />;
}
