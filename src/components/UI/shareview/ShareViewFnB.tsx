import { Box, Stack, Typography } from "@mui/material";
import React from "react";
import { openShareSheet } from "zmp-sdk/apis";
import ShareInLineIcon from "../../icon/ShareInLineIcon";
import ShareOutLineIcon from "../../icon/ShareOutLineIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import NttShareIcon from "@/components/icon/NttShareIcon";

interface IShareView {
  title: string;
  description: string;
  thumbnail: string;
  path?: string;
  overrideStyles?: React.CSSProperties;
  color?: string;
}

export default function ShareView({
  title,
  description,
  thumbnail,
  path,
  overrideStyles,
  color,
}: IShareView) {
  const appConfig = useConfigApp();
  const onShareCurrentPage = async () => {
    try {
      let sharedObj = {
        title,
        description,
        thumbnail,
      };
      if (path) {
        sharedObj = Object.assign(sharedObj, { path });
      }
      const data = await openShareSheet({
        type: "zmp_deep_link",
        data: sharedObj,
      });
    } catch (err) {
      console.log("onShareCurrentPage error", err);
    }
  };

  return (
    <Stack
      bgcolor="#e5e5e5"
      p={1}
      sx={{ borderRadius: "0 8px 8px 0" }}
      onClick={onShareCurrentPage}
      mb={2}
    >
      <NttShareIcon fillColor={appConfig.color.primary} />
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  shareContainer: {
    background: "#FFF",
    fontWeight: 700,
    gap: 1,
    padding: 1,
    alignItems: "center",
    justifyItems: "baseline",
    justifyContent: "space-around",
    borderRadius: 1,
    display: "flex",
  },
  shareContent: {
    display: "flex",
    alignItems: "center",
    width: "100%",
  },
  shareText: {
    fontSize: 16,
    paddingLeft: 8,
    fontWeight: 700,
  },
};
