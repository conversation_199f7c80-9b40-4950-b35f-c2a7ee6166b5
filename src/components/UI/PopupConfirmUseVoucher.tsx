import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  Slide,
  Stack,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import React, { useState } from "react";
import PopupNotifitionUseVoucher from "./PopupNotificationUseVoucher";
import { useTheme } from "@mui/material/styles";
import { useNavigate } from "../../utils/component-util";
import { COLORS } from "../../constants/themes";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function PopupConfirmUseVoucher() {
  const nav = useNavigate();
  const [quantity, setQuantity] = useState(1);
  const productPopup = {
    img: "/images/product1.png",
    name: "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ch<PERSON> cà chua tự nhiên 100%",
    price: 100000,
    cost: 120000,
  };
  const [open, setOpen] = React.useState(false);
  const theme = useTheme();

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const reduce = () => {
    setQuantity((quantity) => (quantity > 0 ? quantity - 1 : quantity));
  };
  const increase = () => {
    setQuantity(quantity + 1);
  };

  const navigate = useNavigate();

  return (
    <>
      <Button
        color="secondary"
        variant="contained"
        style={{ color: "white" }}
        onClick={handleClickOpen}
      >
        Apply
      </Button>
      <Dialog
        className="popup-add-to-cart"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        {/* <DialogTitle>{"Add To Cart"}</DialogTitle> */}
        <DialogContent>
          <Stack className="voucher-profile">
            <div
              className="wrap"
              style={{ marginTop: "40px", marginBottom: "40px" }}
            >
              <div className="coupon">
                <div className="wrap" style={{ marginBlock: 4 }}>
                  <div className="coupon">
                    <Stack
                      direction="row"
                      alignItems="center"
                      justifyContent="center"
                      className="coupon-left"
                      p={1}
                    >
                      <Stack
                        className="content"
                        textAlign="center"
                        justifyContent={"center"}
                        alignItems="center"
                        color="white"
                        gap={1}
                        fontSize={10}
                        style={{
                          height: "100%",
                          width: "100%",
                          background: "#2677E8",
                          borderRadius: 10,
                        }}
                      >
                        <Stack>
                          <span
                            style={{
                              width: 40,
                              height: 40,
                              background: "white",
                              color: "#2677E8",
                              display: "flex",
                              alignItems: "center",
                              borderRadius: "50%",
                              justifyContent: "center",
                              margin: "0px auto",
                            }}
                          >
                            Zalo
                          </span>
                        </Stack>
                        <span>
                          Áp dụng <br />
                          Toàn hệ thống
                        </span>
                      </Stack>
                    </Stack>
                    <Stack className="coupon-con" direction="row" p={"5%"}>
                      <Stack
                        className="content"
                        height={"100%"}
                        width="100%"
                        alignItems="center"
                        fontSize={12}
                        direction="row"
                      >
                        <Stack alignItems="flex-start" width="100%" gap={1}>
                          <Stack
                            width="100%"
                            direction="row"
                            alignItems={"center"}
                            justifyContent="space-between"
                          >
                            <span
                              style={{
                                padding: "4px 8px",
                                background: COLORS.primary1,
                                textAlign: "center",
                                color: "white",
                                borderRadius: 4,
                              }}
                            >
                              Mã giảm giá
                            </span>
                          </Stack>
                          <span
                            style={{
                              color: theme.palette.primary.main,
                              fontWeight: 700,
                            }}
                          >
                            Giảm 20k cho đơn từ 100k
                          </span>
                          <span>Đơn tối thiểu: 20k</span>
                          <span>HSD: 17/11/2023</span>
                        </Stack>
                      </Stack>
                    </Stack>
                  </div>
                </div>
              </div>
            </div>
          </Stack>

          <Divider />
          <Stack>
            <p
              style={{
                fontSize: 16,
                fontWeight: 700,
                color: "#666666",
                textAlign: "center",
                margin: 4,
              }}
            >
              Bạn có muốn dùng{" "}
              <span style={{ color: COLORS.primary1 }}>100 điểm</span>
            </p>
            <p
              style={{
                fontSize: 16,
                fontWeight: 700,
                color: "#666666",
                textAlign: "center",
                margin: 4,
              }}
            >
              để đổi voucher này không?
            </p>
          </Stack>
        </DialogContent>
        <DialogActions
          sx={{ justifyContent: "center", gap: 2, marginBlock: 2 }}
        >
          <Button
            style={{
              background: "#D9D9D9",
              color: "#666666",
              margin: 0,
              height: "100%",
              padding: "10px 60px",
              borderRadius: 99,
              fontSize: 18,
            }}
            onClick={() => {
              nav("/cart");
            }}
          >
            Hủy
          </Button>
          {/* <Button
            style={{
              background: COLORS.primary1,
              color: "#fff",
              margin: 0,
              height: "100%",
              padding: "10px 60px",
              borderRadius: 99,
              fontSize: 18,
            }}
          >
            Đồng ý
          </Button> */}
          <PopupNotifitionUseVoucher
            buttonTitle="Đồng ý"
            buttonStyle={{
              borderRadius: 99,
              color: "#FFF",
              background: COLORS.primary1,
              padding: "10px 60px",
            }}
            actionAgree={() => {
              navigate("/order");
            }}
            content={
              <Stack marginBlock={2} textAlign="center" width={"100%"} gap={2}>
                <Stack>
                  <p
                    style={{
                      color: theme.palette.primary.main,
                      fontWeight: 700,
                      marginBlock: 2,
                      width: "168px",
                      margin: "0 auto",
                    }}
                  >
                    Bạn đã đổi{" "}
                    <span style={{ color: COLORS.primary1 }}>100 điểm</span> lấy
                    Voucher thành công
                  </p>
                  <p style={{ marginBlock: 2 }}>
                    Xem lại tại kho voucher của mình
                  </p>
                </Stack>
              </Stack>
            }
          />
        </DialogActions>
      </Dialog>
    </>
  );
}
