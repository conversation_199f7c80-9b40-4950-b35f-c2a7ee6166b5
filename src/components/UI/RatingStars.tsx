import { Box, Typography } from "@mui/material";
import React from "react";
import StarIcon from "@mui/icons-material/Star";
import StarBorderIcon from "@mui/icons-material/StarBorder";

export default function RatingStars({ rating }: { rating: number }) {
  const fullStars = Math.floor(rating);
  const partial = rating % 1;
  return (
    <Box display="flex" alignItems="center">
      {[...Array(5)].map((_, index) => {
        const isFull = index < fullStars;
        const isPartial = index === fullStars && partial > 0;

        return (
          <Box
            key={index}
            sx={{
              position: "relative",
              width: 16,
              height: 16,
              mr: 0.3,
              display: "inline-block",
            }}
          >
            {/* Gray star */}
            <StarIcon
              sx={{
                color: "#ccc",
                fontSize: 16,
                position: "absolute",
                top: 0,
                left: 0,
              }}
            />

            {/* Orange fill */}
            {(isFull || isPartial) && (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: isFull ? "100%" : `${partial * 100}%`,
                  height: "100%",
                  overflow: "hidden",
                }}
              >
                <StarIcon
                  sx={{
                    color: "#FF8800",
                    fontSize: 16,
                  }}
                />
              </Box>
            )}
          </Box>
        );
      })}

      {/* <Typography color="#666" fontSize={11} fontWeight={500} ml={0.5}>
        {rating.toFixed(1)}
      </Typography> */}
    </Box>
  );
}
