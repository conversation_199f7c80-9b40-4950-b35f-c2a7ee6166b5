import React, { useEffect, useState } from "react";
import { Box, Button, Grid, Typography } from "@mui/material";
import { formatPrice } from "../../utils/formatPrice";
import { COLORS } from "../../constants/themes";
import { BoxTick, People, ProfileTick, UserAdd, WalletTick } from "../../constants/IconSvg";
import { Router } from "@/constants/Route";
import { useNavigate } from "react-router-dom";
import PopupCommissionApproved from "@/pages/Collab/components/PopupCmsAprd";
import PopupCmsPending from "@/pages/Collab/components/PopupCmsPending";
import PopupOrderSuccessful from "@/pages/Collab/components/PopupOrderSuccessful";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import {
  getApprovedCommission,
  getPendingCommission,
  getSuccessOrderAffiliate,
} from "@/redux/slices/affiliation/affiliationSlice";

const ReportCard = () => {
  const { color } = useConfigApp();
  const navigate = useNavigate();
  const [isOpenCmsAprd, setIsOpenCmsAprd] = useState(false);
  const [isOpenCmsPending, setIsOpenCmsPending] = useState(false);
  const [isOpenOrdSucc, setIsOpenOrdSucc] = useState(false);
  const { user } = useSelector((state: RootState) => state.auth);
  const { affiliateReport, successOrders } = useSelector((state: RootState) => state.affiliation);
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    if (user) {
      dispatch(getApprovedCommission());
      dispatch(getPendingCommission());
    }
  }, [user]);

  const TopItems = [
    {
      icon: <WalletTick />,
      bgColor: "#1BAC4B14",
      title: "Hoa hồng đã duyệt",
      value: affiliateReport?.paidCommission,
      isMoney: true,
      action: () => {
        setIsOpenCmsAprd(true);
      },
    },
    {
      icon: <WalletTick />,
      bgColor: "rgba(0, 123, 255, 0.1)",
      title: "Hoa hồng chờ duyệt",
      value: affiliateReport?.pendingCommission,
      isMoney: true,
      action: () => {
        setIsOpenCmsPending(true);
      },
    },
    {
      icon: <People />,
      bgColor: "rgba(255, 193, 7, 0.1)",
      title: "Tổng thành viên",
      value: affiliateReport.totalCustomers,
      action: () => {
        navigate(Router.collabhistory.members.index);
      },
    },
    {
      icon: <ProfileTick />,
      bgColor: "rgba(23, 162, 184, 0.1)",
      title: "Thành viên đã mua hàng",
      value: affiliateReport.totalUserWithPurchased,
      action: () => {
        navigate(Router.collabhistory.members.index, {
          state: { type: "Purchased" },
        });
      },
    },
    {
      icon: <UserAdd />,
      bgColor: "rgba(220, 53, 69, 0.1)",
      title: "Thành viên mới",
      value: affiliateReport.totalUserWithoutPurchase,
      action: () => {
        navigate(Router.collabhistory.members.index, {
          state: { type: "NotPurchase" },
        });
      },
    },
    {
      icon: <BoxTick />,
      bgColor: "rgba(111, 66, 193, 0.1)",
      title: "Đơn hàng thành công",
      value: affiliateReport.successfulOrders,
      action: () => {
        setIsOpenOrdSucc(true);
      },
    },
  ];

  return (
    <>
      <Grid container spacing={2} style={{ marginTop: 0 }}>
        {TopItems.map((item, index) => (
          <Grid item xs={6} key={String(index)}>
            <Button
              sx={{
                width: "100%",
                display: "block",
                padding: 0,
                textAlign: "left",
                textTransform: "none",
              }}
              onClick={item.action}
            >
              <Box sx={styles.itemStl} alignItems="center" justifyItems="center">
                <Box
                  sx={{
                    ...styles.iconBgrd,
                    background: item.bgColor,
                  }}
                >
                  {item.icon}
                </Box>
                <div>
                  <Typography
                    style={{
                      color: COLORS.neutral3,
                      fontSize: 12.5,
                      fontWeight: 500,
                      height: "36px",
                      lineHeight: "15px",
                    }}
                  >
                    {item.title}
                  </Typography>
                  <Typography
                    style={{
                      fontSize: 14.5,
                      fontWeight: 700,
                      color: color.primary,
                    }}
                  >
                    {item.isMoney ? formatPrice(item.value || 0) : item.value}
                  </Typography>
                </div>
              </Box>
            </Button>
          </Grid>
        ))}
      </Grid>

      <PopupCommissionApproved
        isOpen={isOpenCmsAprd}
        setIsOpen={(e) => {
          setIsOpenCmsAprd(e);
        }}
      />
      <PopupCmsPending
        isOpen={isOpenCmsPending}
        setIsOpen={(e) => {
          setIsOpenCmsPending(e);
        }}
      />
      <PopupOrderSuccessful
        isOpen={isOpenOrdSucc}
        setIsOpen={(e) => {
          setIsOpenOrdSucc(e);
        }}
      />
    </>
  );
};

export default ReportCard;

const styles: Record<string, React.CSSProperties> = {
  itemStl: {
    display: "flex",
    borderRadius: "8px",
    padding: "16px",
    gap: "13px",
    backgroundColor: "#EDF3FF",
  },
  iconBgrd: {
    borderRadius: "50%",
    height: "40px",
    width: "40px",
    minWidth: "40px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
};
