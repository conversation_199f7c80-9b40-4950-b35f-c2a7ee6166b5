import React from "react";
import { <PERSON>, Button, Stack, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import PhoneIcon from "@mui/icons-material/Phone";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import { IBranchItem } from "../../types/branch";
import { COLORS } from "../../constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useNavigate } from "@/utils/component-util";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { setDefaultBranch } from "@/redux/slices/branch/branchSlice";

type TBranchItemProps = {
  item: IBranchItem;
  shouldGoBack: boolean;
};

export default function BranchItem(props: TBranchItemProps) {
  const { color } = useConfigApp();
  const { item, shouldGoBack } = props;
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const BranchImage = ({ item }: { item: IBranchItem }) => {
    if (!item?.image) {
      return (
        <Box
          sx={{
            width: 80,
            height: 80,
            borderRadius: "8px",
            backgroundColor: "#f5f5f5",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "1px solid #e0e0e0",
          }}
        >
          <Typography
            sx={{
              fontSize: 10,
              color: "#999",
              textAlign: "center",
              fontWeight: 500,
            }}
          >
            No Image
          </Typography>
        </Box>
      );
    }

    return (
      <img
        width={80}
        height={80}
        src={item.image}
        alt="branch-image"
        style={{
          objectFit: "cover",
          borderRadius: "8px",
        }}
      />
    );
  };

  return (
    <Stack
      style={styles.container}
      direction="row"
      alignItems={"flex-start"}
      justifyContent={"space-between"}
      gap={2}
      padding={"16px"}
      borderRadius={4}
      marginBottom={2}
    >
      <Stack direction="column" gap={1} justifyContent={"space-between"}>
        <Tooltip title={item.branchName} placement="top" arrow>
          <Typography
            sx={{
              fontSize: 18,
              fontWeight: 700,
              color: color.primary,
              lineHeight: 1.2,
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              width: "100%",
            }}
          >
            {item.branchName}
          </Typography>
        </Tooltip>
        <Box sx={{ display: "flex", gap: 1, flex: 1 }}>
          <BranchImage item={item} />
          <Stack sx={{ flex: 1 }} gap={1}>

            <Stack direction="row" alignItems="center" gap={0.5}>
              <Typography sx={styles.labelText}>
                Điện thoại:
              </Typography>
              <Typography sx={{ ...styles.valueText, color: color.primary }}>
                {item.phoneNumber || "Chưa cập nhật"}
              </Typography>
            </Stack>


            <Stack>
              <span style={{ ...styles.labelText, display: '-webkit-box', WebkitLineClamp: 3, WebkitBoxOrient: 'vertical', overflow: 'hidden' }}>
                Địa chỉ: {' '}
                <span style={{ ...styles.valueText, color: color.primary }}>
                  {item.address}
                </span>
              </span>
            </Stack>

            {shouldGoBack && (
              <Button
                variant="contained"
                sx={{
                  alignSelf: "flex-end",
                  backgroundColor: color.primary,
                  color: "white",
                  marginTop: 1,
                  borderRadius: "20px",
                  padding: "8px 16px",
                  textTransform: "none",
                  fontWeight: 600,
                }}
                onClick={async (e: any) => {
                  await dispatch(setDefaultBranch(item));
                  navigate(-1);
                }}
              >
                Chọn
              </Button>
            )}
          </Stack>
        </Box>
      </Stack>

      <Stack gap={1} flex={1} height="100%" display="flex" direction="column" alignItems="flex-end" justifyContent="space-between">
        <Button
          sx={{
            minWidth: 48,
            width: 48,
            height: 48,
            borderRadius: "50%",
            backgroundColor: "#246BFD14",
            color: color.primary,
            "&:hover": {
              backgroundColor: "#246BFD14",
            },
          }}
          onClick={() => {
            if (item.phoneNumber) {
              window.location.href = `tel:${item.phoneNumber}`;
            }
          }}
        >
          <PhoneIcon />
        </Button>
        <Button
          sx={{
            minWidth: 48,
            width: 48,
            height: 48,
            borderRadius: "50%",
            backgroundColor: "#246BFD14",
            color: color.primary,
            "&:hover": {
              backgroundColor: "#246BFD14",
            },
          }}
          onClick={() => {
            if (item.address) {
              const query = encodeURIComponent(`${item.address}`);
              window.open(`https://maps.google.com/maps?q=${query}`, '_blank');
            }
          }}
        >
          <LocationOnIcon />
        </Button>
      </Stack>
    </Stack >
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    borderRadius: "8px",
    background: COLORS.white,
    boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
    border: "1px solid #f0f0f0",
  },
  labelText: {
    fontSize: 14,
    fontWeight: 400,
    color: "#666666",
    minWidth: "fit-content",
  },
  valueText: {
    fontSize: 14,
    fontWeight: 500,
    lineHeight: 1.4,
  },
  itemText: {
    fontSize: 13,
    fontWeight: 400,
    color: COLORS.neutral12,
  },
  itemTextValue: {
    marginLeft: "5px",
  },
  iconBgrd: {
    borderRadius: "5px",
    height: "40px",
    width: "40px",
    minWidth: "40px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
};
