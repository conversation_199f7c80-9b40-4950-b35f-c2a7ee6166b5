import React, { useEffect, useState, useMemo, useCallback } from "react";
import {
  Stack,
  Typography,
  CircularProgress,
  Box,
  TextField,
  InputAdornment,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { useDispatch, useSelector } from "react-redux";
import BranchItem from "./BranchItem";
import { AppDispatch, RootState } from "../../redux/store";
import { getBranchList } from "../../redux/slices/branch/branchSlice";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useDebounce } from "@/hooks/use-debounce";

interface BranchListProps {
  shouldGoBack?: boolean;
}

export default function BranchList(props: BranchListProps) {
  const { shouldGoBack } = props;
  const { color } = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();
  const { list, isLoading } = useSelector((state: RootState) => state.branch);

  const [searchText, setSearchText] = useState("");

  const debouncedSearchText = useDebounce(searchText, 500);

  useEffect(() => {
    dispatch(getBranchList(debouncedSearchText));
  }, [dispatch, debouncedSearchText]);

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value);
  }, []);

  return (
    <Stack gap={2}>
      <TextField
        placeholder="Tìm cửa hàng..."
        value={searchText}
        onChange={handleSearchChange}
        sx={{
          "& .MuiOutlinedInput-root": {
            borderRadius: "25px",
            backgroundColor: "#f5f5f5",
            "& fieldset": {
              borderColor: "#ccc",
            },
            "&:hover fieldset": {
              borderColor: "#ccc",
            },
            "&.Mui-focused fieldset": {
              borderColor: color.primary,
              borderWidth: "1px",
            },
          },
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon sx={{ color: "#999" }} />
            </InputAdornment>
          ),
        }}
      />

      {isLoading ? (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          paddingTop={4}
        >
          <CircularProgress />
        </Box>
      ) : list && list.length > 0 ? (
        list.map((item, index) => (
          <BranchItem
            item={item}
            key={String(index)}
            shouldGoBack={shouldGoBack || false}
          />
        ))
      ) : (
        <Typography
          style={{
            fontSize: 16,
            fontWeight: 400,
            color: "#666666",
            textAlign: "center",
          }}
          pt={4}
        >
          {searchText ? "Không có kết quả tương ứng" : "Không có cửa hàng nào"}
        </Typography>
      )}
    </Stack>
  );
}
