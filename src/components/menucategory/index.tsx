import { useConfigApp } from "@/hooks/useConfigApp";
import { IMenu } from "@/types/menu";
import React from "react";
import MenuCategoriesFnB from "./MenuCategoriesFnB";
import MenuCategoriesRetail from "./MenuCategoriesRetail";

export interface MenuCategoriesProps {
  title?: string;
  listMenu: IMenu[];
  onClickMenu: (item: IMenu) => void;
  subBtn?: string;
  onClickSubBtn?: () => void;
  itemOption?: number;
  item: {
    style?: {
      radius?: number;
      title?: string;
      category?: string;
      fontSize?: number;
      fontWeight?: number;
      itemInRow?: number;
    };
  };
}

const componentMap = {
  FB: MenuCategoriesFnB,
  Retail: MenuCategoriesRetail,
};

export default function MenuCategories(props: MenuCategoriesProps) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FB");
  const Component = componentMap[businessType] || componentMap.Retail;
  return <Component {...props} />;
}
