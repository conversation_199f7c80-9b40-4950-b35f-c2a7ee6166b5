import ArrowRightIcon from "@/components/icon/ArrowRightIcon";
import MenuCategoryItemFnB from "@/components/menucategory/MenuCategoryItemFnB";
import { Router } from "@/constants/Route";
import { COLOR, COLORS, commonStyle } from "@/constants/themes";
import "@/css/hideScrollbar.css";
import { useConfigApp } from "@/hooks/useConfigApp";
import { AppDispatch, RootState } from "@/redux/store";
import { IMenu } from "@/types/menu";
import { useNavigate } from "@/utils/component-util";
import { Box, Button, Stack, Typography, useTheme } from "@mui/material";
import React from "react";
import "react-horizontal-scrolling-menu/dist/styles.css";
import { useDispatch, useSelector } from "react-redux";
import Slider from "react-slick";
import MenuCategoryItemRetail from "./MenuCategoryItemRetail";
import MenuCategoryItemRetailV2 from "./MenuCategoryItemRetailV2";

export default function ({
  title,
  listMenu,
  onClickMenu,
  subBtn,
  onClickSubBtn,
  itemOption,
  item,
}: {
  title?: string;
  listMenu: IMenu[];
  onClickMenu: (item: IMenu) => void;
  subBtn?: string;
  onClickSubBtn?: () => void;
  itemOption?: number;
  item: any;
}) {
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const appConfig = useConfigApp();
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);

  const onNavigateToProduction = () => {
    navigate(Router.menu);
  };

  const slidesToShow = item?.style?.itemInRow || 5;
  const settings = {
    infinite: listMenu.length > slidesToShow,
    speed: 200,
    cssEase: "ease-in-out",
    slidesToShow,
    arrows: false,
    dots: false,
    swipeToSlide: true,
    touchThreshold: 8,
    responsive: [
      {
        breakpoint: 600,
        settings: {
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 900,
        settings: {
          slidesToScroll: 1,
        },
      },
    ],
  };

  const handleClickMenu = (menuItem) => {
    navigate(Router.menu, { state: { categoryId: menuItem.id } });
  };

  function renderContent() {
    return listMenu?.map((menuItem) => (
      <Stack display={"flex !important"} sx={{ alignItems: "center !important" }} key={menuItem.id}>
        <Button
          className="btn-menu-container"
          onClick={() => handleClickMenu(menuItem)}
          style={{ paddingInline: 0 }}
        >
          {itemOption === 1 && <MenuCategoryItemFnB item={menuItem} />}
          {itemOption === 2 && (
            <MenuCategoryItemRetail
              item={menuItem}
              shopInfo={{ ...shopInfo, businessType: shopInfo.businessType || "" }}
              style={item.style || {}}
            />
          )}
        </Button>
      </Stack>
    ));
  }

  function renderContentGrid() {
    const maxItems = 4;
    return (
      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          justifyContent: "space-between",
          rowGap: 2,
          my: 1,
        }}
      >
        {listMenu?.slice(0, maxItems).map((menuItem) => (
          <Box
            key={menuItem.id}
            sx={{
              width: "48%",
              borderRadius: "10px",
              p: 0.5,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              position: "relative",
              background: "#FBFCF7",
              boxShadow: "inset 0 4px 24px 0 #E6EFFF",
            }}
          >
            <Button
              onClick={() => handleClickMenu(menuItem)}
              style={{
                padding: 0,
                width: "100%",
                display: "flex",
                justifyContent: "center",
              }}
            >
              <MenuCategoryItemRetailV2
                item={menuItem}
                shopInfo={{ ...shopInfo, businessType: shopInfo.businessType || "" }}
                category={item.style?.category}
              />
            </Button>
          </Box>
        ))}
      </Box>
    );
  }

  function renderContentGrid2() {
    const maxItems = item?.limit || 4;
    return (
      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          justifyContent: "space-between",
          rowGap: 2,
          my: 1,
        }}
      >
        {listMenu?.slice(0, maxItems).map((menuItem) => (
          <Box
            key={menuItem.id}
            sx={{
              width: "48%",
              borderRadius: "10px",
              p: 0.5,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              position: "relative",
            }}
          >
            <Typography
              fontWeight="600"
              fontSize={14}
              textAlign="center"
              sx={{
                color: "#000",
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                width: "100%",
                height: "40px",
              }}
              title={menuItem.title}
            >
              {menuItem.title}
            </Typography>
            <Button
              onClick={() => handleClickMenu(menuItem)}
              style={{
                padding: 0,
                width: "100%",
                display: "flex",
                justifyContent: "center",
              }}
            >
              <MenuCategoryItemRetailV2
                item={menuItem}
                shopInfo={{ ...shopInfo, businessType: shopInfo.businessType || "" }}
                category={item.style?.category}
              />
            </Button>
            <Stack
              onClick={() => handleClickMenu(menuItem)}
              direction="row"
              alignItems="center"
              justifyContent="flex-end"
              sx={{
                position: "absolute",
                bottom: 12,
                right: 10,
              }}
            >
              {/* <Typography fontSize={15} color={theme.palette.primary.main} sx={{ fontWeight: 500 }}>
                Xem thêm
              </Typography> */}
              {/* <Box
                ml={0.5}
                sx={{
                  "& svg": { width: 12, height: 12 },
                }}
              >
                <ArrowRightIcon fillColor={theme.palette.primary.main} />
              </Box> */}
            </Stack>
          </Box>
        ))}
      </Box>
    );
  }

  function renderTitle() {
    return (
      title && (
        <Box sx={styles.productionTitleContainer}>
          <Typography color={appConfig.color.primary} style={{ ...commonStyle.headline20 }}>
            {title}
          </Typography>
          {subBtn && (
            <Button
              style={{ ...styles.seeMoreBtn, color: COLOR.text.color1 }}
              onClick={onClickSubBtn ? () => onClickSubBtn() : () => null}
            >
              {subBtn}
              <Stack style={styles.seeMoreIcon}>
                <ArrowRightIcon fillColor="gray" />
              </Stack>
            </Button>
          )}
          <Box
            mt={1}
            sx={{
              cursor: "pointer",
              display: "inline-flex",
              alignItems: "center",
              color: appConfig.color.primary,
            }}
            onClick={onNavigateToProduction}
          >
            <Typography fontSize={14} fontWeight={400}>
              Xem tất cả
            </Typography>
            <Stack ml={1} sx={{ height: 14 }}>
              <ArrowRightIcon fillColor={appConfig.color.primary} />
            </Stack>
          </Box>
        </Box>
      )
    );
  }

  function renderSlider() {
    const category = item.style?.category;
    if (category === "slice") {
      return <Slider {...settings}>{renderContent()}</Slider>;
    }
    if (category === "grid2") {
      return (
        <Stack>
          {title && (
            <Box sx={{ textAlign: "center", my: 2 }}>
              <Typography color={appConfig.color.primary} style={{ ...commonStyle.headline20 }}>
                {title}
              </Typography>
            </Box>
          )}
          {renderContentGrid2()}
        </Stack>
      );
    }
    return (
      <Stack>
        {title && (
          <Box sx={{ textAlign: "center", my: 2 }}>
            <Typography color={appConfig.color.primary} style={{ ...commonStyle.headline20 }}>
              {title}
            </Typography>
          </Box>
        )}
        {renderContentGrid()}
      </Stack>
    );
  }

  return (
    <Box
      style={{
        ...styles.container,
        background: `unset`,
        paddingRight: "16px",
        paddingLeft: "16px",
        paddingTop: "0px",
        paddingBottom: "0px",
      }}
    >
      {renderSlider()}
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    paddingBlock: "5px",
    position: "relative",
  },
  productionTitleContainer: {
    display: "flex",
    paddingBottom: 1,
    marginTop: 0,
    justifyContent: "space-between",
    alignItems: "center",
  },
  homeTabContainerTop: {
    display: "flex",
    paddingTop: 3,
    alignItems: "flex-start",
  },
  seeMoreBtn: {
    display: "flex",
    alignItems: "center",
    fontSize: 15,
    fontWeight: 500,
    color: COLORS.textColor.primary,
  },
  seeMoreIcon: {
    marginLeft: 4,
    width: 12,
    height: 12,
  },
};
