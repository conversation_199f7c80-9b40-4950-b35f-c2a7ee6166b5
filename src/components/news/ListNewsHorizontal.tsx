import React, { memo } from "react";
import { ScrollMenu } from "react-horizontal-scrolling-menu";
import "react-horizontal-scrolling-menu/dist/styles.css";
import { INews } from "../../types/news";
import NewsItemHorizontal from "./newsitemhorizontal";
// NOTE: for hide scrollbar
import "../../css/hideScrollbar.css";

interface ListNewsHorizontalProps {
  list?: INews[];
  titleFromParent?: string;
}

const ListNewsHorizontal = ({ list = [], titleFromParent }: ListNewsHorizontalProps) => {
  if (!list?.length) return null;

  return (
    <ScrollMenu>
      {list.map((news: INews, index) => (
        <NewsItemHorizontal
          news={news}
          key={index}
          titleFromParent={titleFromParent}
          containerStyles={{ width: 250, paddingRight: 16 }}
        />
      ))}
    </ScrollMenu>
  );
};

export default memo(ListNewsHorizontal);
