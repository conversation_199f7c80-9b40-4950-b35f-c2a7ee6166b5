import React, { useState, useEffect } from "react";
import { Box, Typography, Divider, Skeleton } from "@mui/material";
import { IProduct } from "../../types/product";
import { formatPrice } from "../../utils/formatPrice";
import { useNavigate } from "../../utils/component-util";
import { COLOR, COLORS } from "../../constants/themes";
import PopupIconAddToCart from "../UI/PopupIconAddToCart";
import { useConfigApp } from "@/hooks/useConfigApp";
import { formatNumberToK } from "@/utils/common";
import RatingStars from "../UI/RatingStars";
import LazyImage from "../UI/LazyImage";

interface VerticalProductItemProps {
  item: IProduct;
}

const VerticalProductItem: React.FC<VerticalProductItemProps> = ({ item }) => {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  let URLImage = item?.images && item?.images.length > 0 ? item?.images[0]?.link : "";

  // Kiểm tra xem ảnh đã tải chưa từ sessionStorage
  const getInitialLoadingState = () => {
    if (!URLImage) return true;
    const loadedImagesCache = sessionStorage.getItem("loadedImages");
    const loadedImages = loadedImagesCache ? JSON.parse(loadedImagesCache) : {};
    return !loadedImages[URLImage];
  };

  const [imageLoading, setImageLoading] = useState(getInitialLoadingState);

  // Cập nhật trạng thái nếu URL thay đổi
  useEffect(() => {
    setImageLoading(getInitialLoadingState());
  }, [URLImage]);

  const onNavigateToDetail = () => {
    navigate(`/product-detail/${item.itemsCode}`);
  };

  const salePrice = item?.price || item?.listVariant[0].price || 0;
  const priceReal = item?.priceReal || item?.listVariant[0].priceReal || 0;

  const handleImageLoad = () => {
    setImageLoading(false);

    // Lưu trạng thái đã tải vào sessionStorage
    if (URLImage) {
      const loadedImagesCache = sessionStorage.getItem("loadedImages");
      const loadedImages = loadedImagesCache ? JSON.parse(loadedImagesCache) : {};
      loadedImages[URLImage] = true;
      sessionStorage.setItem("loadedImages", JSON.stringify(loadedImages));
    }
  };

  const handleImageError = () => {
    setImageLoading(false);
  };

  return (
    <Box sx={styles.container}>
      <Box sx={styles.imageContainer}>
        {imageLoading && (
          <Skeleton
            variant="rectangular"
            width={80}
            height={80}
            animation="wave"
            sx={{ borderRadius: 1, position: "absolute" }}
          />
        )}

        <LazyImage
          src={URLImage}
          alt={item?.itemsName || ""}
          aspectRatio="1"
          style={styles.imageProduct}
          onClick={onNavigateToDetail}
          // loading="lazy"
        />
      </Box>
      <Box sx={styles.contentContainer}>
        <Box sx={styles.infoSection}>
          <Box onClick={onNavigateToDetail}>
            <Box sx={styles.productTitle}>{item?.itemsName}</Box>
          </Box>

          <Box sx={styles.priceContainer}>
            <Typography
              sx={{
                ...styles.salePriceText,
                color: appConfig.color.primary,
              }}
            >
              {formatPrice(salePrice)}
            </Typography>
          </Box>

          <Box sx={styles.infoContainer}>
            <Box sx={styles.ratingContainer}>
              <RatingStars rating={4.9} />

              <Divider orientation="vertical" flexItem sx={styles.divider} />
              <Typography sx={styles.soldText}>
                Đã bán {formatNumberToK(item?.sold || 0)}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box sx={styles.cartContainer}>
          <PopupIconAddToCart product={item} />
        </Box>
      </Box>
    </Box>
  );
};

export default React.memo(VerticalProductItem);

const styles = {
  container: {
    background: "#f9f9f9",
    cursor: "pointer",
    position: "relative",
    display: "flex",
    marginBottom: 1,
    alignItems: "flex-start",
    padding: 1.25,
    borderRadius: 0.6,
    border: "1px solid #eee",
    width: "100%",
    gap: 1.25,
    boxSizing: "border-box",
  },
  imageContainer: {
    width: 80,
    height: 80,
    minWidth: 80,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  },
  imageProduct: {
    borderRadius: 5,
    width: 80,
    height: 80,
    objectFit: "cover" as any, // Fix type error by casting as any
    background: "#fff",
  },
  contentContainer: {
    display: "flex",
    flexDirection: "column",
    width: "calc(100% - 90px)",
    height: 80,
    position: "relative",
    paddingTop: 0,
    paddingBottom: 0,
    paddingRight: 1.25,
  },
  infoSection: {
    display: "flex",
    flexDirection: "column",
    gap: 0.4,
    width: "100%",
  },
  priceContainer: {
    display: "flex",
    alignItems: "center",
    marginTop: 0,
  },
  productTitle: {
    color: "#000",
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    cursor: "pointer",
    fontWeight: 500,
    lineHeight: "16px",
    letterSpacing: "0.1px",
    fontSize: 13,
    marginBottom: 0,
    WebkitLineClamp: 1,
  },
  salePriceText: {
    fontSize: 15,
    fontWeight: 700,
  },
  soldText: {
    color: COLOR.text.product_sold,
    fontSize: 8,
    fontWeight: 400,
    fontStyle: "normal",
    whiteSpace: "nowrap",
  },
  ratingContainer: {
    display: "flex",
    alignItems: "center",
    gap: 0.25,
  },
  infoContainer: {
    display: "flex",
    alignItems: "center",
  },
  cartContainer: {
    position: "absolute",
    right: -5,
    bottom: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  divider: {
    height: 8,
    margin: "0 6px 0 2px",
    backgroundColor: "#ddd",
    alignSelf: "center",
  },
};
