import * as React from "react";
import { IProductCategory } from "../../types/product";
import { Box, Grid, Typography } from "@mui/material";

interface CategoryItemProps {
  item: IProductCategory;
  onClickItem: () => void;
  styles?: React.CSSProperties;
}

const CategoryItem = ({ item, onClickItem, styles }: CategoryItemProps) => {
  /**
   * @description styles
   */
  const titleStyle: React.CSSProperties = {
    textAlign: "center",
    fontWeight: 700,
    fontSize: 14,
    color: "#143374",
  };

  //------------------------

  return (
    <Grid key={item.id} item xs={3} style={{ ...styles }}>
      <Box
        onClick={onClickItem}
        sx={{
          display: "flex",
          flexDirection: "column",
        }}
        justifyContent="center"
        alignItems="center"
      >
        <img
          width={40}
          src={`${import.meta.env.VITE_API_URL}${
            item?.image?.data?.attributes?.url
          }`}
          alt=""
        />
        <Typography sx={titleStyle}>{item.name}</Typography>
      </Box>
    </Grid>
  );
};

export default CategoryItem;
