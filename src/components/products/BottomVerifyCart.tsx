import React, { useMemo } from "react";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { Icon } from "../../constants/Assets";
import { showToast } from "../../utils/common";
import { <PERSON><PERSON>, Container, Stack, Typography } from "@mui/material";
import cssStyles from "../../css/styles.module.css";
import { formatPrice } from "../../utils/formatPrice";
import { COLORS, commonStyle } from "../../constants/themes";
import { RootState } from "../../redux/store";
import { useSelector } from "react-redux";
import CheckIcon from "../icon/CheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { ICart } from "@/types/cart";

export default function BottomVerifyCart({
  cart,
  cartPayment,
  handlerCreatePayment,
}: {
  cart: ICart;
  cartPayment: ICart;
  handlerCreatePayment: () => void;
}) {
  const appConfig = useConfigApp();

  return (
    <Container className={cssStyles.bottomPayElement}>
      <Stack sx={styles.bottomContainer} direction="row" gap={1}>
        <Stack width={"100%"}>
          <Stack
            direction={"row"}
            justifyContent={"end"}
            width={"100%"}
            alignItems={"baseline"}
          >
            <Typography style={styles.titleStyle}>Tổng thanh toán</Typography>
            <Typography
              style={{ ...styles.textStyle, color: appConfig.color.accent }}
            >
              {formatPrice(cartPayment?.originPrice)}
            </Typography>
          </Stack>
          <Stack
            direction={"row"}
            justifyContent={"end"}
            width={"100%"}
            alignItems={"baseline"}
          >
            <Typography style={styles.titleStyle}>Tiết kiệm</Typography>
            <Typography
              style={{
                ...styles.textSmallStyle,
                color: appConfig.color.accent,
              }}
            >
              {formatPrice(cartPayment?.savingMoney)}
            </Typography>
          </Stack>
        </Stack>
        <Button
          style={{
            ...styles.orderBtn,
            background: appConfig.color.primary,
            color: appConfig.color.accent,
          }}
          onClick={handlerCreatePayment}
        >
          Đặt hàng
        </Button>
      </Stack>
    </Container>
  );
}

const styles: Record<string, React.CSSProperties> = {
  sectionContainer: {
    borderRadius: 4,
    background: "#fff",
    padding: 2,
    marginBottom: 1,
  },
  bottomContainer: {
    minWidth: "100%",
    alignItems: "center",
    justifyContent: "space-between",
  },
  orderBtn: {
    background: COLORS.primary,
    color: "#fff",
    paddingBlock: 10,
    borderRadius: 5,
    fontSize: 17,
    minWidth: 140,
  },
  titleStyle: {
    color: "#919191",
    fontSize: 13,
    fontWeight: 400,
    marginRight: 3,
  },
  textStyle: {
    fontSize: 19,
    fontWeight: 700,
  },
  textSmallStyle: {
    fontSize: 13,
    fontWeight: 400,
  },
};
