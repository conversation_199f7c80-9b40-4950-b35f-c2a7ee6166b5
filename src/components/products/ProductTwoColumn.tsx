import React, { useEffect, useState, useCallback, useRef } from "react";
import { Box, CircularProgress, Grid, Stack, Typography } from "@mui/material";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import ProductItem from "./item/ProductItem";
import { IProduct } from "../../types/product";
import NoDataView from "../UI/NoDataView";
import InfiniteScroll from "../UI/InfiniteScroll";
import { getProductListByCategory } from "@/redux/slices/product/productListSlice";
import ProductItemSkeleton from "./ProductItemSkeleton";
import { useConfigApp } from "@/hooks/useConfigApp";

interface ListType {
  products: IProduct[];
  pagination: {
    skip: number;
    limit: number;
    total: number;
  };
}

interface ProductTwoColumnProps {
  onUpdateCount?: (count: number) => void;
  disableInfiniteScroll?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

const ProductTwoColumn: React.FC<ProductTwoColumnProps> = ({
  onUpdateCount,
  disableInfiniteScroll = false,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { list, isLoading, isInitialLoading, searchCondition, sortBy } = useSelector(
    (state: RootState) => state.productList
  );
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const appConfig = useConfigApp();
  const discountType =
    appConfig?.home?.find(
      (item) =>
        (item.type === "ProductList" || item.type === "ProductList2") &&
        item.style?.discountType !== undefined
    )?.style?.discountType || "";

  const [localList, setLocalList] = useState<ListType>(list);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [showNoData, setShowNoData] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [visibleCount, setVisibleCount] = useState(20);
  const listContainerRef = useRef<HTMLDivElement | null>(null);

  // Apply sorting and filtering when list, sortBy, or searchCondition changes
  useEffect(() => {
    // Start with the original list
    let processedProducts = [...list.products];

    // Apply price filtering if needed
    if (
      searchCondition.priceFilter &&
      (searchCondition.minPrice !== undefined || searchCondition.maxPrice !== undefined)
    ) {
      const minPrice = searchCondition.minPrice || 0;
      const maxPrice = searchCondition.maxPrice || 5000000; // Giới hạn giá tối đa 5 triệu

      processedProducts = processedProducts.filter((item) => {
        const itemPrice = item.price || item.listVariant[0]?.price || 0;
        return itemPrice >= minPrice && itemPrice <= maxPrice;
      });
    }

    // Apply sorting if needed
    if (sortBy && sortBy.field && sortBy.order !== "default") {
      processedProducts.sort((a, b) => {
        const a_price = a.price || a.listVariant[0]?.price || 0;
        const b_price = b.price || b.listVariant[0]?.price || 0;

        return sortBy.order === "asc" ? a_price - b_price : b_price - a_price;
      });
    }

    // Update local list with processed products
    setLocalList({
      ...list,
      products: processedProducts,
      pagination: {
        ...list.pagination,
        total: processedProducts.length,
      },
    });

    // Report the filtered count back to parent
    if (onUpdateCount) {
      onUpdateCount(processedProducts.length);
    }

    // Mark initial load as complete if not loading anymore
    if (!isInitialLoading && !initialLoadComplete) {
      setInitialLoadComplete(true);

      // Nếu không có sản phẩm sau khi tải xong, đặt timeout để hiển thị "Không có sản phẩm"
      if (list.products.length === 0) {
        setTimeout(() => {
          setShowNoData(true);
        }, 1500);
      } else {
        setShowNoData(false);
      }
    }

    // Kết thúc trạng thái loading khi tải thêm dữ liệu
    // Trong useEffect KHÔNG set loadingMore(false) nữa khi tải thêm sản phẩm
  }, [list, sortBy, searchCondition, onUpdateCount, isInitialLoading, initialLoadComplete]);

  // Hiển thị skeleton loading khi mới vào trang
  useEffect(() => {
    // Reset trạng thái khi điều kiện tìm kiếm thay đổi
    if (isInitialLoading) {
      setInitialLoadComplete(false);
      setShowNoData(false);
    }

    // Đảm bảo hiển thị sản phẩm ngay khi có dữ liệu, ngay cả khi vẫn đang loading
    if (list.products.length > 0 && !initialLoadComplete) {
      setInitialLoadComplete(true);
      setShowNoData(false);
    }
  }, [searchCondition, isInitialLoading, list.products.length, initialLoadComplete]);

  // Reset visibleCount khi filter/search thay đổi
  useEffect(() => {
    setVisibleCount(40);
  }, [list, sortBy, searchCondition]);

  // Hàm xử lý scroll để lazy render thêm sản phẩm
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      setVisibleCount((prev) => Math.min(prev + 20, localList.products.length));
    }
  };

  // Cải thiện logic tải thêm sản phẩm
  const fetchMoreData = useCallback(() => {
    if (disableInfiniteScroll || loadingMore) return;

    setLoadingMore(true);

    dispatch(async (_, getState) => {
      const state = getState().productList?.list;
      if (!state) {
        setLoadingMore(false);
        return;
      }

      // Kiểm tra xem còn sản phẩm để tải không dựa trên pagination
      const currentItemCount = state.products.length;
      const totalItems = state.pagination.total;

      // Nếu đã tải hết hoặc gần hết sản phẩm, không cần tải thêm
      if (currentItemCount >= totalItems) {
        setLoadingMore(false);
        return;
      }

      // Tính toán số lượng sản phẩm còn lại để tải
      const remaining = totalItems - currentItemCount;
      // Chọn giá trị nhỏ hơn giữa limit ban đầu và số lượng còn lại
      const nextLimit = Math.min(state.pagination.limit, remaining);

      if (nextLimit <= 0) {
        setLoadingMore(false);
        return;
      }

      // Gọi API để tải thêm sản phẩm
      await dispatch(
        getProductListByCategory({
          ...searchCondition,
          skip: currentItemCount,
          limit: nextLimit,
          // shopId,
        })
      );
      setTimeout(() => setLoadingMore(false), 3000); // Delay 3s sau khi load xong
    });
  }, [disableInfiniteScroll, loadingMore, dispatch, searchCondition, shopId]);

  const renderSkeletons = (count = 6) => (
    <Grid container spacing={2} sx={{ paddingLeft: 2, paddingRight: 0 }}>
      {Array.from({ length: count }).map((_, index) => (
        <Grid item xs={6} key={index} style={styles.itemContainer}>
          <ProductItemSkeleton />
        </Grid>
      ))}
    </Grid>
  );

  // Sửa renderProductGrid để chỉ render visibleCount sản phẩm
  const renderProductGrid = () => {
    return (
      <Grid container spacing={0} sx={{ paddingLeft: 0, paddingRight: 0, paddingBottom: 4 }}>
        {localList.products.slice(0, visibleCount).map((item: IProduct) => (
          <Grid item xs={6} key={item.itemsCode} style={styles.itemContainer}>
            <ProductItem item={item} discountType={discountType} />
          </Grid>
        ))}
      </Grid>
    );
  };

  // Thêm hàm renderLoadingIndicator để hiển thị skeleton khi scroll
  const renderLoadingIndicator = () => {
    if (!loadingMore) return null;

    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          py: 2,
          width: "100%",
        }}
      >
        <ProductItemSkeleton />
      </Box>
    );
  };

  // Render cho virtualized item
  const renderVirtualizedItem = ({
    index,
    style,
    data,
  }: {
    index: number;
    style: React.CSSProperties;
    data: IProduct[];
  }) => {
    // Kiểm tra nếu index vượt quá số sản phẩm hiện có
    if (index >= data.length) {
      return (
        <div style={style}>
          <ProductItemSkeleton />
        </div>
      );
    }

    const item = data[index];
    return (
      <div style={style}>
        <Grid container spacing={2} sx={{ paddingLeft: 1, paddingRight: 1 }}>
          <Grid item xs={6} style={styles.itemContainer}>
            <ProductItem item={item} discountType={discountType} />
          </Grid>
          {/* Nếu có item kế tiếp và là index chẵn, hiển thị item kế tiếp */}
          {index % 2 === 0 && index + 1 < data.length && (
            <Grid item xs={6} style={styles.itemContainer}>
              <ProductItem item={data[index + 1]} discountType={discountType} />
            </Grid>
          )}
        </Grid>
      </div>
    );
  };

  const hasMore = list.pagination.skip + list.pagination.limit < list.pagination.total;

  if (!isLoading && localList.products.length === 0) {
    return (
      <Box sx={{ width: "100%", height: "100%", display: "flex", justifyContent: "center" }}>
        <Box
          sx={{
            width: "100%",
            maxWidth: { xs: "100%", sm: "600px", md: "800px" },
            margin: "0 auto",
            padding: "0 8px",
          }}
        >
          <NoDataView content="Không có sản phẩm nào" />
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{ width: "100%", height: "100%", overflowY: "auto" }}
      ref={listContainerRef}
      onScroll={handleScroll}
    >
      {!initialLoadComplete || isInitialLoading ? (
        renderSkeletons(6)
      ) : Array.isArray(localList.products) && localList.products.length > 0 ? (
        disableInfiniteScroll ? (
          renderProductGrid()
        ) : (
          <InfiniteScroll
            loader={loadingMore ? renderSkeletons(3) : null}
            className="mx-auto"
            fetchMore={fetchMoreData}
            hasMore={hasMore}
            endMessage={null}
          >
            {renderProductGrid()}
          </InfiniteScroll>
        )
      ) : loadingMore || isInitialLoading ? (
        renderSkeletons(6)
      ) : showNoData ? (
        <Box sx={{ height: "100%", width: "100%" }}>
          <NoDataView content="Không có sản phẩm nào" />
        </Box>
      ) : (
        renderSkeletons(6)
      )}
    </Box>
  );
};

const styles: Record<string, React.CSSProperties> = {
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  itemContainer: {
    textAlign: "left",
    padding: "0 4px",
    marginBottom: 8,
  },
  gridContainer: {
    marginTop: 9,
  },
};

export default React.memo(ProductTwoColumn);
