import { useConfigApp } from "@/hooks/useConfigApp";
import { IProduct } from "@/types/product";
import AddIcon from "@mui/icons-material/Add";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CloseIcon from "@mui/icons-material/Close";
import HorizontalRuleIcon from "@mui/icons-material/HorizontalRule";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  IconButton,
  Slide,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import axios from "axios";
import React, { useEffect, useState } from "react";
import { COLORS } from "../../constants/themes";
import { formatPrice } from "../../utils/formatPrice";
import { getMinPriceInListVariant } from "./item/ProductItemFnB";

export const getItemOptionByIds = async (itemOptionIds) => {
  const response = await axios.post(
    `${import.meta.env.VITE_API_URL_SYSTEM}/api/user/itemoptionuser/getitemoptionbyids`,
    itemOptionIds
  );
  return response;
};

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function ChangeInfoATC({
  item,
  onChangeQuantity,
}: {
  item: IProduct;
  onChangeQuantity: (item: IProduct, quantity: number, note: string) => void;
}) {
  const appConfig = useConfigApp();

  const [open, setOpen] = React.useState(false);
  const [quantity, setQuantity] = useState(item.quantity);
  const [itemOptionGroups, setItemOptionGroups] = useState<any[]>([]);
  const [note, setNote] = useState("");
  const salePrice = item?.price || getMinPriceInListVariant(item?.listVariant) || 0;
  const priceReal = formatPrice(item.priceReal);
  const URLImage =
    item.isVariant === true
      ? item?.variantImage?.link
        ? item?.variantImage?.link
        : item?.images && item?.images.length > 0
        ? item?.images[0]?.link
        : ""
      : item?.images && item?.images.length > 0
      ? item?.images[0]?.link
      : "";
  const [imageSrc, setImageSrc] = useState(URLImage);
  useEffect(() => {
    setImageSrc(URLImage);
  }, [URLImage]);

  const fetchAndSetItemOptions = async (itemOptionIds) => {
    const response = await getItemOptionByIds(itemOptionIds);

    if (response?.data) {
      setItemOptionGroups(response?.data.data);
    }
  };

  useEffect(() => {
    setQuantity(item?.quantity);
  }, [item]);

  useEffect(() => {
    setNote(item?.note || "");
  }, [item]);

  useEffect(() => {
    if (Array.isArray(item.extraOptions) && item.extraOptions.length > 0) {
      fetchAndSetItemOptions(item.extraOptions);
    }
  }, [item?.extraOptions]);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setQuantity(item?.quantity);
  };

  const reduce = () => {
    setQuantity((quantity) => (quantity > 0 ? quantity - 1 : quantity));
  };
  const increase = () => {
    setQuantity(quantity + 1);
  };
  return (
    <Box>
      <Stack onClick={handleClickOpen} direction={"row"} alignItems={"start"}>
        <img
          style={styles.imageProduct}
          src={URLImage}
          onError={(e) => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = appConfig?.shopLogo?.link || "";
          }}
        />
        <Stack
          direction={"column"}
          ml={2}
          flex={1}
          justifyContent="space-between"
          minHeight={"80px"}
          maxWidth="100%"
          overflow="hidden"
        >
          <Box>
            <Typography
              noWrap={false}
              color="#444"
              fontSize="16px"
              fontWeight={500}
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                whiteSpace: "normal",
              }}
            >
              {item.itemsName}
            </Typography>
            {item.isVariant && (
              <Typography noWrap fontSize="12px" style={{ color: COLORS.neutral4 }}>
                {[item.variantValueOne, item.variantValueTwo, item.variantValueThree]
                  .filter(Boolean)
                  .join(", ")}
              </Typography>
            )}
            {itemOptionGroups.length > 0 && (
              <Typography
                noWrap
                fontSize="12px"
                sx={{
                  color: COLORS.neutral4,

                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  maxWidth: "100%", // Đảm bảo nó không vượt quá container
                }}
              >
                {itemOptionGroups
                  .map(
                    (itemGroup) =>
                      `${itemGroup.name}: ${itemGroup.itemOptions
                        .map((itemOption) => itemOption.name)
                        .join(", ")}`
                  )
                  .join("; ")}
              </Typography>
            )}
            {item.note && (
              <Typography
                noWrap
                fontSize="12px"
                sx={{
                  color: COLORS.neutral4,
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap", // Không cho phép xuống dòng
                  overflow: "hidden", // Ẩn phần nội dung bị tràn
                  maxWidth: "100%", // Giữ nội dung trong giới hạn container
                }}
              >
                Ghi chú: {item.note}
              </Typography>
            )}
          </Box>
          <Box style={styles.priceContainer}>
            <Typography
              style={{
                ...styles.salePriceText,
                color: appConfig.color.primary,
              }}
            >
              {formatPrice(salePrice)}
            </Typography>
            {/* {priceReal !== salePrice && (
              <Typography style={styles.discountPrice}>{priceReal}</Typography>
            )} */}
            <Typography style={styles.quantityText}>x{quantity}</Typography>
          </Box>
        </Stack>
      </Stack>
      <Dialog
        className="popup-add-to-cart"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        {/* <DialogTitle>{"Add To Cart"}</DialogTitle> */}
        <DialogContent>
          <Stack onClick={handleClickOpen} direction={"row"} alignItems={"start"}>
            <img
              style={styles.imageProduct}
              src={imageSrc}
              onError={() => {
                setImageSrc(appConfig?.shopLogo?.link || "");
              }}
            />
            <Stack
              direction={"column"}
              ml={2}
              flex={1}
              justifyContent="space-between"
              minHeight={"80px"}
            >
              <Box>
                <Typography
                  color="#444"
                  fontSize="16px"
                  fontWeight={500}
                  sx={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "-webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                    whiteSpace: "normal",
                  }}
                >
                  {item.itemsName}
                </Typography>
                {item.isVariant && (
                  <Typography fontSize="12px" style={{ color: COLORS.neutral4 }}>
                    {[item.variantValueOne, item.variantValueTwo, item.variantValueThree]
                      .filter(Boolean)
                      .join(", ")}
                  </Typography>
                )}

                {itemOptionGroups.map((itemGroup, index) => (
                  <Typography
                    key={index}
                    fontSize="12px"
                    sx={{
                      color: COLORS.neutral4,
                    }}
                  >
                    `{itemGroup.name}: `
                    {itemGroup.itemOptions.map((itemOption) => itemOption.name).join(", ")}
                  </Typography>
                ))}
                <Stack>
                  <Typography
                    fontSize="12px"
                    sx={{
                      color: COLORS.neutral4,
                    }}
                  >
                    Ghi chú
                  </Typography>
                  <Box>
                    <TextField
                      placeholder="Nhập ghi chú"
                      variant="outlined"
                      multiline
                      rows={1} // Số dòng hiển thị
                      fullWidth
                      value={note} // Giá trị của input
                      onChange={(e) => setNote(e.target.value)} // Cập nhật state khi nhập
                      sx={{
                        "& .MuiInputBase-root": {
                          padding: 1, // Điều chỉnh padding tổng thể
                        },
                      }}
                      inputProps={{ maxLength: 50 }} // Giới hạn ký tự tối đa
                    />
                  </Box>
                </Stack>
              </Box>
            </Stack>
          </Stack>
          <Stack direction="row" style={styles.qualityContainer}>
            <IconButton
              style={styles.qualityBtn}
              aria-label="fingerprint"
              color="secondary"
              onClick={reduce}
            >
              <HorizontalRuleIcon style={{ color: COLORS.black }} />
            </IconButton>
            <Stack direction="row" alignItems={"center"} gap={2}>
              <Typography style={{ fontWeight: 700 }}>Số lượng</Typography>
              <input
                type="number"
                style={styles.qualityInput}
                min="0"
                step="1"
                onChange={(e) => {
                  const value = Number(e.target.value);
                  if (value >= 0) {
                    setQuantity(value);
                  }
                }}
                value={quantity}
              />
            </Stack>
            <IconButton
              style={styles.qualityBtn}
              aria-label="fingerprint"
              color="secondary"
              onClick={increase}
            >
              <AddIcon style={{ color: COLORS.black }} />
            </IconButton>
          </Stack>
        </DialogContent>
        <DialogActions sx={styles.bottomBtnContainer}>
          <Button
            style={{
              ...styles.bottomBtn,
              background: COLORS.accent4,
              color: appConfig.color.primary,
            }}
            onClick={handleClose}
            startIcon={<CloseIcon />}
          >
            Hủy bỏ
          </Button>
          <Button
            style={{
              ...styles.bottomBtn,
              background: appConfig.color.primary,
              color: COLORS.white,
            }}
            onClick={() => {
              if (item) {
                onChangeQuantity(item, quantity, note);
                handleClose();
              }
            }}
            startIcon={<CheckCircleOutlineIcon />}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  qualityContainer: {
    border: "1px solid #D9D9D9",
    borderRadius: 5,
    padding: "6px 0px",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
    width: "100%",
  },
  qualityBtn: {
    marginInline: 8,
    background: "#EDF7FE",
  },
  qualityInput: {
    width: 60,
    height: 30,
    borderRadius: 20,
    border: "1px solid #D9D9D9",
    textAlign: "center",
    fontWeight: 700,
  },
  bottomBtnContainer: {
    justifyContent: "center",
    gap: 2,
    marginBlock: 2,
  },
  bottomBtn: {
    margin: 0,
    height: "100%",
    padding: "10px 30px",
    display: "flex",
    gap: "8px",
  },
  imageProduct: {
    borderRadius: 5,
    width: 80,
    height: 80,
    minWidth: 80,
    objectFit: "cover",
  },
  priceContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 4,
  },
  salePriceText: {
    fontSize: 19,
    fontWeight: 700,
  },
  discountPrice: {
    textDecoration: "line-through",
    fontSize: 11,
    fontWeight: 400,
    color: "#B8B8B8",
  },
  quantityText: {
    color: "#7D7D7D",
    fontSize: 17,
    fontWeight: 500,
    marginLeft: 5,
  },
};
