import { useConfigApp } from "@/hooks/useConfigApp";
import React from "react";
import ProductItemFnB from "./ProductItemFnB";
import ProductItemV2 from "./ProductItemRetail";

const componentMap = {
  FnB: ProductItemFnB,
  Retail: ProductItemV2,
};

export default function ProductItem(props: any) {
  const appConfig = useConfigApp();
  const businessType = String(appConfig.businessType ?? "FnB");
  const Component = componentMap[businessType] || componentMap.FnB;
  return <Component {...props} />;
}
