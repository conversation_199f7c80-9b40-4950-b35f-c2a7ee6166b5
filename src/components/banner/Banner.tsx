import React, { memo, useEffect, useState } from "react";
import <PERSON>lide<PERSON> from "react-slick";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { useNavigate } from "../../utils/component-util";
import { Box } from "@mui/material";
import { getListNewsUser } from "@/redux/slices/news/newsListSlice";
import { INews } from "@/types/news";

const Banner = () => {
  const [listArticle, setListArticle] = useState<INews[]>([]);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    fetchListNewsUser();
  }, [shopId]);

  const fetchListNewsUser = async () => {
    if (shopId) {
      const res = await dispatch(getListNewsUser(shopId));
      setListArticle(res?.payload?.data);
    }
  };
  // const banner = useSelector((state: RootState) => state.newsList.banner);
  const settings = {
    dots: true,
    infinite: listArticle.length > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    arrows: false,
  };
  const navigate = useNavigate();

  return Array.isArray(listArticle) && listArticle.length ? (
    <Box>
      <Slider {...settings}>
        {listArticle?.map((item) => (
          <Box
            key={item.articleId}
            onClick={() => {
              navigate(`/posts/${item.articleId}`);
            }}
            style={{ ...styles.imageContainer }}
          >
            <img style={styles.imageContainer} src={item?.images[0]?.link} />
          </Box>
        ))}
      </Slider>
    </Box>
  ) : null;
};

export default memo(Banner);

const styles: Record<string, React.CSSProperties> = {
  imageContainer: {
    display: "flex",

    overflow: "hidden",
    aspectRatio: 7 / 4,
    width: "100%",
    backgroundSize: "cover",
    backgroundPosition: "center",
  },
};
