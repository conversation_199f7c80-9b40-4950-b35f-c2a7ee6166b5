import React, { useEffect, useRef, useState } from "react";

interface CustomVideoProps {
  src: string;
  type: string;
}

const CustomVideo: React.FC<CustomVideoProps> = ({ src, type }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    handleVideo();
  }, []);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVideo = () => {
    const videoElement = videoRef.current;
    if (videoElement) {
      const handleEnded = () => {
        setIsPlaying(false);
      };
      videoElement.addEventListener("ended", handleEnded);
      return () => {
        videoElement.removeEventListener("ended", handleEnded);
      };
    }
    return undefined;
  };

  return (
    <div style={{ position: "relative", width: "100%", height: "100%" }}>
      <video
        ref={videoRef}
        style={{ width: "100%", height: "100%", objectFit: "cover" }}
        controlsList="nodownload nofullscreen noremoteplayback"
        onClick={handlePlayPause}
        preload="metadata"
      >
        <source src={`${src}#t=0.1`} type={type} />
        Your browser does not support the video tag.
      </video>
      {!isPlaying && (
        <button
          onClick={handlePlayPause}
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            background: "rgba(0, 0, 0, 0.5)",
            border: "none",
            borderRadius: "50%",
            width: "50px",
            height: "50px",
            cursor: "pointer",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: "#fff",
          }}
          disabled={isPlaying}
        >
          ▶
        </button>
      )}
    </div>
  );
};

export default CustomVideo;
