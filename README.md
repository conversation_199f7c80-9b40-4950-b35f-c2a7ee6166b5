# GMGC

## ZMP CLI Options

ZMP app created with following options:

```
{
  "cwd": "/Volumes/HoangVu/code/duan/zalo app/loyalty-cli",
  "newProject": true,
  "name": "gmgc",
  "package": "zmp-ui",
  "framework": "react-typescript",
  "cssPreProcessor": false,
  "template": "single-view",
  "theming": {
    "color": "#007aff",
    "darkTheme": false,
    "fillBars": false
  },
  "customBuild": false,
  "includeTailwind": false,
  "stateManagement": "recoil"
}
```

## NPM Scripts

* 🔥 `start` - run development server
* 🙏 `deploy` - deploy mini app for production