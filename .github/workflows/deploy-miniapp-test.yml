on:
  push:
    tags:
      - '[0-9]*_*_t'

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: '--max_old_space_size=8192'

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: "yarn"
      - name: Deploy
        run: |
          TAG_NAME=${GITHUB_REF#refs/tags/}
          APP_ID=$(echo $TAG_NAME | cut -d'_' -f1)
          MESSAGE=$(echo $TAG_NAME | cut -d'_' -f2)
          echo "APP_ID=$APP_ID" >> $GITHUB_ENV
          echo "MESSAGE=$MESSAGE" >> $GITHUB_ENV
          response=$(curl -s -L "${{ secrets.EVO_BE_TEST }}/api/partner/shopsetting/zalo-access-token" \
            -H 'Content-Type: application/json' \
            -d "{
              \"EvoSecretKey\": \"${{ secrets.EVO_SECRET_KEY_STG }}\",
              \"MiniAppId\": \"$APP_ID\"
            }")
          ACCESS_TOKEN=$(echo $response | jq -r '.accessToken')
          yarn
          yarn add global zmp-cli
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          sed -i "s/^APP_ID=.*/APP_ID=$APP_ID/" .env.test
          cp .env.test .env
          printf "\\033[B\n$ACCESS_TOKEN" | APP_ID=${APP_ID} yarn zmp login
          printf "\\033[B\n${MESSAGE}" | yarn deploy:zalo-test

      - name: Send noti
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build MiniApp Test: ${{ env.APP_ID}}, version: ${{ env.MESSAGE}} - ${{ github.event.head_commit.message }}'
