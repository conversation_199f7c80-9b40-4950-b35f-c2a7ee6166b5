name: Deploy Web App Prod

on:
  push:
    branches:
      - production

jobs:
  build:
    runs-on: [self-hosted, prod]
    env:
      NODE_OPTIONS: '--max_old_space_size=4096'
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'yarn'
      - run: |
          yarn
          yarn build:web-prod
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          cp -r dist/. /var/www/webapp/dist

      - name: Send noti
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build EvoRetail Web Prod success - ${{ github.event.head_commit.message }}'

